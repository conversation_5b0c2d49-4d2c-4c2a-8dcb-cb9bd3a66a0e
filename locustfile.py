from locust import HttpUser, task, between
import time
import random
from datetime import datetime, timedelta


class PatientEncounter(HttpUser):
    # Track all active encounters for relative logins
    active_encounters = set()
    active_encounter_shares = {}
    # Track patient info for relative logins
    active_encounter_patient_info = {}

    host = "http://localhost:8080"
    wait_time = between(1, 1)

    def on_start(self):
        # Set service headers for creating/updating encounter
        self.service_headers = {
            "X-Dev-User-Id": "1",
            "X-Dev-Org-Id": "demo-akihospit",
            "X-Dev-Role": "SERVICE",
        }

        # Create patient and encounter
        patient_data, login_info = self.generate_patient()
        response = self.client.post(
            "/api/v1/patient", json=patient_data, headers=self.service_headers
        )
        self.patient_id = response.json()["patient_id"]

        encounter_data = {
            "subject": self.patient_id,
            "service_provider": "demo-akihospit",
            "service_type": "demo-akihospit-emergency",
            "identifier": [
                {
                    "system": "https://www.demo-akihospit.com",
                    "value": f"ENC{random.randint(10000, 99999)}",
                }
            ],
        }
        response = self.client.post(
            "/api/v1/encounter", json=encounter_data, headers=self.service_headers
        )
        encounter_response = response.json()
        self.encounter_id = encounter_response["encounter_id"]
        self.share_id = encounter_response["share_id"]

        # Track encounter for relative logins
        PatientEncounter.active_encounters.add(self.encounter_id)
        PatientEncounter.active_encounter_shares[self.encounter_id] = self.share_id
        PatientEncounter.active_encounter_patient_info[self.encounter_id] = {
            "last_name": login_info["last_name"][:3],
            "birth_date": login_info["birth_date"],
        }

        # Create initial IOA request
        self.ioa_data = {
            "encounter_id": self.encounter_id,
            "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
            "status": "not-yet-started",
            "encounter_timing": {
                "wait_count": random.randint(1, 10),
                "planned_end_date": 0,
            },
        }
        self.client.post(
            "/api/v1/service_request", json=self.ioa_data, headers=self.service_headers
        )

        # Store login info for relogins
        self.login_info = login_info
        # Randomly decide if this user checks view
        self.checks_view = random.random() < 0.5
        if self.checks_view:
            self.do_login()

        self.status = "ioa_waiting"
        self.next_update = time.time() + random.uniform(30, 300)  # Between 30s and 5min
        if self.checks_view:
            self.next_view_check = time.time() + 5  # Check view every 5 seconds
            self.next_relogin = time.time() + random.uniform(20, 900)  # Between 20-60s

    def do_login(self):
        response = self.client.post(
            "/api/v1/login/access-token",
            params={
                "id": self.encounter_id,
                "truncated_last_name": self.login_info["last_name"][:3],
                "birth_date": self.login_info["birth_date"],
            },
            name="/api/v1/login/access-token",
        )
        self.patient_headers = {
            "Authorization": f"Bearer {response.json()['access_token']}"
        }

    def generate_patient(self):
        first_name = random.choice(["John", "Jane", "Bob", "Alice"])
        last_name = random.choice(["Smith", "Doe", "Johnson", "Brown"])
        birth_date = (
            datetime.now() - timedelta(days=random.randint(365 * 20, 365 * 80))
        ).strftime("%Y-%m-%d")

        return {
            "name": {"first_name": first_name, "last_name": last_name},
            "birth_date": birth_date,
            "phone": "+33626890767",
        }, {"last_name": last_name, "birth_date": birth_date}

    @task(1)
    def check_status(self):
        current_time = time.time()

        # Only check view if this user is a view checker
        if self.checks_view and current_time >= self.next_view_check:
            # Patient view check
            self.client.get("/api/v1/view", headers=self.patient_headers)
            self.next_view_check = current_time + 5  # Set next check in 5 seconds

            # Occasional relogin and share reset
            if current_time >= self.next_relogin:
                self.do_login()
                self.next_relogin = current_time + random.uniform(
                    20, 900
                )  # Between 20-60s

                # Maybe reset share access
                if random.random() < 0.2:
                    response = self.client.post(
                        f"/api/v1/encounter/{self.encounter_id}/$reset-share",
                        headers=self.patient_headers,
                        name="/api/v1/encounter/$reset-share",
                    )
                    # Update share id for future relatives
                    PatientEncounter.active_encounter_shares[self.encounter_id] = (
                        response.json()["share_id"]
                    )

        # Service updates (between 30s and 5min)
        if current_time >= self.next_update:
            self.update_encounter()
            self.next_update = current_time + random.uniform(30, 300)

    def update_encounter(self):
        if self.status == "ioa_waiting":
            # Update wait times
            self.ioa_data["encounter_timing"]["wait_count"] = random.randint(1, 15)
            self.client.post(
                "/api/v1/service_request",
                json=self.ioa_data,
                headers=self.service_headers,
            )

            # Maybe progress to in-progress
            if random.random() < 0.2:
                self.ioa_data["status"] = "in-progress"
                self.ioa_data["encounter_timing"]["wait_count"] = 0
                self.client.post(
                    "/api/v1/service_request",
                    json=self.ioa_data,
                    headers=self.service_headers,
                )
                self.status = "ioa_in_progress"

        elif self.status == "ioa_in_progress":
            # Maybe complete IOA and start medical care
            if random.random() < 0.3:
                self.ioa_data["status"] = "completed"
                self.client.post(
                    "/api/v1/service_request",
                    json=self.ioa_data,
                    headers=self.service_headers,
                )

                self.medical_care_data = {
                    "encounter_id": self.encounter_id,
                    "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
                    "status": "not-yet-started",
                    "encounter_timing": {
                        "wait_count": random.randint(1, 5),
                        "planned_end_date": 0,
                    },
                }
                self.client.post(
                    "/api/v1/service_request",
                    json=self.medical_care_data,
                    headers=self.service_headers,
                )
                self.status = "medical_care_waiting"

        elif self.status == "medical_care_waiting":
            # Update wait times
            self.medical_care_data["encounter_timing"]["wait_count"] = random.randint(
                1, 8
            )
            self.client.post(
                "/api/v1/service_request",
                json=self.medical_care_data,
                headers=self.service_headers,
            )

            # Maybe progress to in-progress
            if random.random() < 0.2:
                self.medical_care_data["status"] = "in-progress"
                self.medical_care_data["encounter_timing"]["wait_count"] = 0
                self.client.post(
                    "/api/v1/service_request",
                    json=self.medical_care_data,
                    headers=self.service_headers,
                )
                self.status = "medical_care_in_progress"

        elif self.status == "medical_care_in_progress":
            # Maybe complete encounter
            if random.random() < 0.25:
                self.medical_care_data["status"] = "completed"
                self.client.post(
                    "/api/v1/service_request",
                    json=self.medical_care_data,
                    headers=self.service_headers,
                )

                discharge_data = {
                    "actual_period": {"end": int(datetime.now().timestamp() * 1000)},
                    "discharge": {
                        "disposition": "home",
                        "instructions": [
                            {"id": "TC", "name": "Head Trauma Instructions"}
                        ],
                    },
                }
                self.client.patch(
                    f"/api/v1/encounter/{self.encounter_id}",
                    json=discharge_data,
                    headers=self.service_headers,
                    name="/api/v1/encounter",
                )
                # Remove encounter from active list
                PatientEncounter.active_encounters.remove(self.encounter_id)
                del PatientEncounter.active_encounter_shares[self.encounter_id]
                del PatientEncounter.active_encounter_patient_info[self.encounter_id]
                self.status = "completed"
                self.stop()


class RelativeUser(HttpUser):
    host = "http://localhost:8080"
    wait_time = between(1, 1)

    def on_start(self):
        if not PatientEncounter.active_encounters:
            self.stop()
            return

        # Pick a random active encounter
        self.encounter_id = random.choice(list(PatientEncounter.active_encounters))
        self.share_id = PatientEncounter.active_encounter_shares[self.encounter_id]
        self.patient_info = PatientEncounter.active_encounter_patient_info[
            self.encounter_id
        ]
        self.do_login()
        self.next_relogin = time.time() + random.uniform(20, 900)  # Between 20-60s

    def do_login(self):
        response = self.client.post(
            "/api/v1/login/access-token",
            params={
                "id": self.share_id,
                "truncated_last_name": self.patient_info["last_name"],
                "birth_date": self.patient_info["birth_date"],
            },
            name="/api/v1/login/access-token/share",
        )
        try:
            self.patient_headers = {
                "Authorization": f"Bearer {response.json()['access_token']}"
            }
        except Exception:
            # Login failed (likely due to share reset)
            self.stop()

    @task(1)
    def check_status(self):
        if self.encounter_id not in PatientEncounter.active_encounters:
            self.stop()
            return

        # View check
        response = self.client.get("/api/v1/view", headers=self.patient_headers)
        if response.status_code == 403:  # Forbidden - share likely reset
            self.stop()
            return

        # Occasional relogin
        if time.time() >= self.next_relogin:
            self.do_login()
            self.next_relogin = time.time() + random.uniform(20, 900)  # Between 20-60s
