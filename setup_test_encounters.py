from datetime import datetime
import requests
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
SERVICE_PROVIDER = "demo-akihospit"
SERVICE_TYPE = "demo-akihospit-emergency"


class EncounterSetup:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip("/")
        self.patients = []
        self.encounters = []

    def create_patient(
        self, first_name: str, last_name: str, birth_date: str, phone: str
    ) -> Dict[str, Any]:
        """Create a new patient"""
        data = {
            "name": {"first_name": first_name, "last_name": last_name},
            "birth_date": birth_date,
            "phone": phone,
        }
        try:
            response = requests.post(f"{self.base_url}/api/v1/patient", json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 422:
                logger.error(f"Validation error creating patient: {e.response.json()}")
            raise

    def create_encounter(self, patient_id: str) -> Dict[str, Any]:
        """Create a new encounter for a patient"""
        data = {
            "subject": patient_id,
            "service_provider": SERVICE_PROVIDER,
            "service_type": SERVICE_TYPE,
            "identifier": [
                {"system": "system", "value": patient_id, "use": "secondary"}
            ],
        }
        try:
            response = requests.post(f"{self.base_url}/api/v1/encounter", json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 422:
                logger.error(
                    f"Validation error creating encounter: {e.response.json()}"
                )
            raise

    def update_encounter(
        self,
        encounter_id: str,
        end_date: Optional[str] = None,
        planned_duration: Optional[int] = None,
        discharge: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Update an encounter with an end date, planned duration, and/or discharge info"""
        data = {}
        if end_date:
            data["actual_period"] = {"end": end_date}
        if planned_duration is not None:
            data["planned_duration"] = planned_duration
        if discharge:
            data["discharge"] = discharge

        try:
            response = requests.patch(
                f"{self.base_url}/api/v1/encounter/{encounter_id}", json=data
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 422:
                logger.error(
                    f"Validation error updating encounter: {e.response.json()}"
                )
            raise

    def create_service_request(
        self,
        encounter_id: str,
        activity_id: str,
        status: str,
        wait_count: int = 0,
        wait_time: int = 0,
    ) -> Dict[str, Any]:
        """Create a service request for an encounter"""
        data = {
            "encounter_id": encounter_id,
            "activity_definition_id": activity_id,
            "status": status,
            "encounter_timing": {
                "planned_end_date": wait_time,
                "wait_count": wait_count,
            },
        }
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/service_request", json=data
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 422:
                logger.error(
                    f"Validation error creating service request: {e.response.json()}"
                )
                logger.error(f"Request data was: {data}")
            raise

    def setup_encounters(self):
        """Create 5 patients with different encounter states"""
        # Create 5 patients with different details
        patient_details = [
            ("John", "Smith", datetime.now().strftime("%Y-%m-%d"), "+33611111111"),
            ("Marie", "Dubois", datetime.now().strftime("%Y-%m-%d"), "+33622222222"),
            ("James", "Wilson", datetime.now().strftime("%Y-%m-%d"), "+33633333333"),
            ("Sophie", "Martin", datetime.now().strftime("%Y-%m-%d"), "+33644444444"),
            ("David", "Brown", datetime.now().strftime("%Y-%m-%d"), "+33655555555"),
        ]

        # Create patients and their encounters
        for i, (first_name, last_name, birth_date, phone) in enumerate(patient_details):
            patient = self.create_patient(first_name, last_name, birth_date, phone)
            self.patients.append(patient)
            encounter = self.create_encounter(patient["patient_id"])
            self.encounters.append(encounter)
            logger.info(
                f"Created patient {i + 1} ({first_name} {last_name}) with encounter {encounter['encounter_id']}"
            )

        # Patient 2: Needs IOA
        self.create_service_request(
            self.encounters[1]["encounter_id"],
            "demo-akihospit-emergency-activity-ioa",
            "not-yet-started",
            10,
            20,
        )

        # Patient 3: Waiting for doctor
        self.create_service_request(
            self.encounters[2]["encounter_id"],
            "demo-akihospit-emergency-activity-ioa",
            "completed",
        )
        # Set planned duration after IOA is completed
        self.update_encounter(self.encounters[2]["encounter_id"], planned_duration=60)
        self.create_service_request(
            self.encounters[2]["encounter_id"],
            "demo-akihospit-emergency-activity-medical-care",
            "not-yet-started",
            15,
            30,
        )

        # Patient 4: Waiting to leave (all activities completed)
        self.create_service_request(
            self.encounters[3]["encounter_id"],
            "demo-akihospit-emergency-activity-ioa",
            "completed",
        )
        # Set planned duration after IOA is completed
        self.update_encounter(self.encounters[3]["encounter_id"], planned_duration=60)
        self.create_service_request(
            self.encounters[3]["encounter_id"],
            "demo-akihospit-emergency-activity-medical-care",
            "completed",
        )

        # Patient 5: Leaving (set end date on encounter)
        self.create_service_request(
            self.encounters[4]["encounter_id"],
            "demo-akihospit-emergency-activity-ioa",
            "completed",
        )
        # Set planned duration after IOA is completed
        self.update_encounter(self.encounters[4]["encounter_id"], planned_duration=60)
        self.create_service_request(
            self.encounters[4]["encounter_id"],
            "demo-akihospit-emergency-activity-medical-care",
            "completed",
        )
        # Set end date and discharge disposition for the encounter
        self.update_encounter(
            self.encounters[4]["encounter_id"],
            end_date=str(int(datetime.now().timestamp() * 1000)),
            discharge={"disposition": "home"},
        )

        # Log final state
        logger.info("\nFinal state of encounters:")
        logger.info(
            f"Patient 1 ({patient_details[0][0]} {patient_details[0][1]}): No activities"
        )
        logger.info(
            f"Patient 2 ({patient_details[1][0]} {patient_details[1][1]}): Waiting for IOA"
        )
        logger.info(
            f"Patient 3 ({patient_details[2][0]} {patient_details[2][1]}): Waiting for doctor"
        )
        logger.info(
            f"Patient 4 ({patient_details[3][0]} {patient_details[3][1]}): All activities completed, ready to leave"
        )
        logger.info(
            f"Patient 5 ({patient_details[4][0]} {patient_details[4][1]}): Discharged (encounter ended)"
        )


def main():
    # Replace with your backend URL
    BACKEND_URL = "http://localhost:8080"

    try:
        setup = EncounterSetup(BACKEND_URL)
        setup.setup_encounters()
    except requests.RequestException as e:
        logger.error(f"Error setting up encounters: {e}")
        raise


if __name__ == "__main__":
    main()
