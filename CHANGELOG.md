# CHANGELOG


## v0.33.3 (2025-05-13)

### Bug Fixes

- Resolve encounter_id in login and accepted_tos event logging tests
  ([`54c6cc6`](https://github.com/Focus-Sante/ppu/commit/54c6cc69e12b0d2d96fa91e620719fc4fe461727))

### Documentation

- Added gh copilot compatible instructions from the cursor rules
  ([`bdbfbf0`](https://github.com/Focus-Sante/ppu/commit/bdbfbf027b9da065de390986ee13593f6905a59a))

- Update README and guidelines with project overview, key features, and DevOps practices
  ([`a44d646`](https://github.com/Focus-Sante/ppu/commit/a44d646e5a66bb83580a80dd6f0314d92569a787))


## v0.33.2 (2025-05-13)

### Bug Fixes

- Fetch actual encounter_id if share_id is provided for logging
  ([`61e113b`](https://github.com/Focus-Sante/ppu/commit/61e113bd565870feca835e50bb19929616f6f5e0))


## v0.33.1 (2025-05-13)

### Bug Fixes

- Remove unnecessary response variable in logLoginPageViewed function
  ([`709490f`](https://github.com/Focus-Sante/ppu/commit/709490fcb9a7019e2b07e4df7565bdcc1fbc8616))


## v0.33.0 (2025-05-13)

### Build System

- **deps**: Bump esbuild and tsx in /packages/webapp
  ([`70e0473`](https://github.com/Focus-Sante/ppu/commit/70e0473e28042a61aacebbee81d067e32aebc459))

Bumps [esbuild](https://github.com/evanw/esbuild) and [tsx](https://github.com/privatenumber/tsx).
  These dependencies needed to be updated together.

Updates `esbuild` from 0.23.1 to 0.25.4 - [Release notes](https://github.com/evanw/esbuild/releases)
  - [Changelog](https://github.com/evanw/esbuild/blob/main/CHANGELOG-2024.md) -
  [Commits](https://github.com/evanw/esbuild/compare/v0.23.1...v0.25.4)

Updates `tsx` from 4.19.1 to 4.19.4 - [Release notes](https://github.com/privatenumber/tsx/releases)
  - [Changelog](https://github.com/privatenumber/tsx/blob/master/release.config.cjs) -
  [Commits](https://github.com/privatenumber/tsx/compare/v4.19.1...v4.19.4)

--- updated-dependencies: - dependency-name: esbuild dependency-version: 0.25.4

dependency-type: indirect

- dependency-name: tsx dependency-version: 4.19.4

dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.18 to 5.4.19 in /packages/doctor-ui
  ([`c12121b`](https://github.com/Focus-Sante/ppu/commit/c12121bf8b3648a32792cfc627bbed5f3bbe8bf7))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.18 to 5.4.19. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.19/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.19/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-version: 5.4.19

dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 6.3.3 to 6.3.4 in /packages/webapp
  ([`631f8d7`](https://github.com/Focus-Sante/ppu/commit/631f8d7be3802352666e12703243ad7a42900c79))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 6.3.3 to 6.3.4. - [Release
  notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/main/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v6.3.4/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-version: 6.3.4

dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

### Chores

- Added cursor rules
  ([`88f928a`](https://github.com/Focus-Sante/ppu/commit/88f928a31065e2197c124d9296d9b4cb78ea42e4))

### Features

- Add logging for new events (LOGIN_PAGE_VIEWED, ACCEPTED_TOS, SENT_SHARE_LINK) and update event
  repository methods
  ([`7ffeef2`](https://github.com/Focus-Sante/ppu/commit/7ffeef25a53435540bde4488b27e3bb13403952e))


## v0.32.3 (2025-05-06)

### Bug Fixes

- Log text messages sent to patients and relatives after discharge
  ([`3a9d650`](https://github.com/Focus-Sante/ppu/commit/3a9d650c72afac3e9cb84d5a8ec38032a4145d2d))


## v0.32.2 (2025-05-06)

### Bug Fixes

- Fix incorrect string for expired journey
  ([`ee6f58c`](https://github.com/Focus-Sante/ppu/commit/ee6f58c3d6dc372b7279dd7b252df02e7acfdc46))


## v0.32.1 (2025-05-06)

### Bug Fixes

- Update event search endpoint to root path
  ([`650bba2`](https://github.com/Focus-Sante/ppu/commit/650bba21dcab115a6fdca28e16675860e07d339e))


## v0.32.0 (2025-05-05)

### Features

- Enhance SMS functionality in EncounterService to respect quiet hours in Paris. Added timezone
  handling and updated logic to defer SMS sending during specified quiet hours. Added unit tests to
  verify behavior during quiet hours and outside of them. Fixes Do not send SMS between 23:00 PM &
  08:00 AM #103
  ([`44f19cc`](https://github.com/Focus-Sante/ppu/commit/44f19cc0a4353516c934b623befc0c1786ec25a1))

- Fix typo in parameter name from "troncated_last_name" to "truncated_last_name" across multiple
  files; implement event logging for encounter creation, text message sending, and connection
  attempts; add EventRepository and related models for event handling.
  ([`d118070`](https://github.com/Focus-Sante/ppu/commit/d118070a9ca8853c1b9ef61ebbcc6c890754ee9f))

### Testing

- Fix test with discharge instruction URL
  ([`580d149`](https://github.com/Focus-Sante/ppu/commit/580d1498dbed269a80155af0a2058747c4a217a6))


## v0.31.0 (2025-04-28)

### Features

- Enhance SMS notification logic for discharge instructions
  ([`21d53f6`](https://github.com/Focus-Sante/ppu/commit/21d53f687d1fea01bcb4f8d620733b3544a8c639))

Improved the SMS sending logic in EncounterServiceImpl to check for new discharge instructions
  before sending notifications. Added logic to store existing instruction IDs and determine if new
  instructions were included in the patch request, ensuring SMS is sent only when there are updates.
  Added debug logging for cases where no new instructions are found.


## v0.30.1 (2025-04-28)

### Bug Fixes

- Correct file path for discharge instruction retrieval
  ([`4cd6eee`](https://github.com/Focus-Sante/ppu/commit/4cd6eee097999783037cbb33dacab98ae8b45bbd))

Updated the file path in the get_discharge_instruction_file function to remove the .pdf extension,
  ensuring the correct file is accessed for discharge instructions.


## v0.30.0 (2025-04-28)

### Features

- Add long attribute to SMS message structure
  ([`8c87385`](https://github.com/Focus-Sante/ppu/commit/8c87385a360129d758704898dd8c7b21b5c887a8))

Added a new "long" attribute with a value of "yes" to the SMS message structure in CapitoleMobile.py
  to enhance message formatting capabilities.

### Refactoring

- Update SMS message formatting in EncounterService
  ([`b226b8f`](https://github.com/Focus-Sante/ppu/commit/b226b8f8bcda5ca600cde27126b243b700519c4f))

Modified the SMS message structure to replace newlines with spaces and removed bullet points for
  better readability. Each instruction is now concatenated into a single line, enhancing the overall
  message presentation.


## v0.29.2 (2025-04-25)

### Bug Fixes

- Handle potential bug in patch_request for actual_period
  ([`4d87071`](https://github.com/Focus-Sante/ppu/commit/4d87071195079dd934ccf4f9fb69f61994f8600d))

Added a temporary fix in EncounterServiceImpl to delete actual_period.end if it is set to 0, and
  remove actual_period if start is not defined. This change aims to mitigate a potential bug during
  encounter updates.

### Build System

- **deps**: Bump @babel/runtime in /packages/doctor-ui
  ([`d40cefe`](https://github.com/Focus-Sante/ppu/commit/d40cefe181edf04c9a31c36aa8a22e17d633bad7))

Bumps [@babel/runtime](https://github.com/babel/babel/tree/HEAD/packages/babel-runtime) from 7.25.7
  to 7.27.0. - [Release notes](https://github.com/babel/babel/releases) -
  [Changelog](https://github.com/babel/babel/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/babel/babel/commits/v7.27.0/packages/babel-runtime)

--- updated-dependencies: - dependency-name: "@babel/runtime" dependency-version: 7.27.0

dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump @babel/runtime in /packages/webapp
  ([`be5846c`](https://github.com/Focus-Sante/ppu/commit/be5846c39754ca061af2bd55a4c9fbbf8a4087ba))

Bumps [@babel/runtime](https://github.com/babel/babel/tree/HEAD/packages/babel-runtime) from 7.25.6
  to 7.27.0. - [Release notes](https://github.com/babel/babel/releases) -
  [Changelog](https://github.com/babel/babel/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/babel/babel/commits/v7.27.0/packages/babel-runtime)

--- updated-dependencies: - dependency-name: "@babel/runtime" dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump axios from 1.7.7 to 1.8.2 in /packages/webapp
  ([`265d9d6`](https://github.com/Focus-Sante/ppu/commit/265d9d67277bede1d3b4ea9abad2184397a6c1b8))

Bumps [axios](https://github.com/axios/axios) from 1.7.7 to 1.8.2. - [Release
  notes](https://github.com/axios/axios/releases) -
  [Changelog](https://github.com/axios/axios/blob/v1.x/CHANGELOG.md) -
  [Commits](https://github.com/axios/axios/compare/v1.7.7...v1.8.2)

--- updated-dependencies: - dependency-name: axios dependency-type: direct:production ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump cryptography from 43.0.1 to 44.0.1
  ([`2de1735`](https://github.com/Focus-Sante/ppu/commit/2de17353766aca35dc3db3dd6a61d76e1aa77ec0))

Bumps [cryptography](https://github.com/pyca/cryptography) from 43.0.1 to 44.0.1. -
  [Changelog](https://github.com/pyca/cryptography/blob/main/CHANGELOG.rst) -
  [Commits](https://github.com/pyca/cryptography/compare/43.0.1...44.0.1)

--- updated-dependencies: - dependency-name: cryptography dependency-type: direct:production ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump h11 from 0.14.0 to 0.16.0
  ([`f2124d0`](https://github.com/Focus-Sante/ppu/commit/f2124d07d3bcc7e52658d05b4eb0f50c41a0a611))

Bumps [h11](https://github.com/python-hyper/h11) from 0.14.0 to 0.16.0. -
  [Commits](https://github.com/python-hyper/h11/compare/v0.14.0...v0.16.0)

--- updated-dependencies: - dependency-name: h11 dependency-version: 0.16.0

dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump nanoid from 3.3.7 to 3.3.11 in /packages/doctor-ui
  ([`87e01fa`](https://github.com/Focus-Sante/ppu/commit/87e01fab4dd7698cf3fb43863dad9466dd6e4e8c))

Bumps [nanoid](https://github.com/ai/nanoid) from 3.3.7 to 3.3.11. - [Release
  notes](https://github.com/ai/nanoid/releases) -
  [Changelog](https://github.com/ai/nanoid/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/ai/nanoid/compare/3.3.7...3.3.11)

--- updated-dependencies: - dependency-name: nanoid dependency-version: 3.3.11

dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump nanoid from 3.3.7 to 3.3.8 in /packages/webapp
  ([`2351f71`](https://github.com/Focus-Sante/ppu/commit/2351f7194f91ae617986562963013e68b2a12c16))

Bumps [nanoid](https://github.com/ai/nanoid) from 3.3.7 to 3.3.8. - [Release
  notes](https://github.com/ai/nanoid/releases) -
  [Changelog](https://github.com/ai/nanoid/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/ai/nanoid/compare/3.3.7...3.3.8)

--- updated-dependencies: - dependency-name: nanoid dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump pnpm from 9.10.0 to 10.0.0 in /packages/webapp
  ([`5c4dfda`](https://github.com/Focus-Sante/ppu/commit/5c4dfdadd9ee441fffac1acc506bc4bbb9f97578))

Bumps [pnpm](https://github.com/pnpm/pnpm/tree/HEAD/pnpm) from 9.10.0 to 10.0.0. - [Release
  notes](https://github.com/pnpm/pnpm/releases) -
  [Changelog](https://github.com/pnpm/pnpm/blob/main/pnpm/CHANGELOG.md) -
  [Commits](https://github.com/pnpm/pnpm/commits/v10.0.0/pnpm)

--- updated-dependencies: - dependency-name: pnpm dependency-version: 10.0.0

dependency-type: direct:production ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps**: Bump rollup from 4.21.3 to 4.27.3 in /packages/webapp
  ([`c7a63ed`](https://github.com/Focus-Sante/ppu/commit/c7a63ed42c1390dbefe0eb54e7c2076bde58bbfc))

Bumps [rollup](https://github.com/rollup/rollup) from 4.21.3 to 4.27.3. - [Release
  notes](https://github.com/rollup/rollup/releases) -
  [Changelog](https://github.com/rollup/rollup/blob/master/CHANGELOG.md) -
  [Commits](https://github.com/rollup/rollup/compare/v4.21.3...v4.27.3)

--- updated-dependencies: - dependency-name: rollup dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump @babel/helpers in /packages/doctor-ui
  ([`fae2182`](https://github.com/Focus-Sante/ppu/commit/fae2182359b91facd0a4e3e429b3d34717da06a5))

Bumps [@babel/helpers](https://github.com/babel/babel/tree/HEAD/packages/babel-helpers) from 7.25.7
  to 7.27.0. - [Release notes](https://github.com/babel/babel/releases) -
  [Changelog](https://github.com/babel/babel/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/babel/babel/commits/v7.27.0/packages/babel-helpers)

--- updated-dependencies: - dependency-name: "@babel/helpers" dependency-version: 7.27.0

dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump @babel/helpers in /packages/webapp
  ([`5ee085e`](https://github.com/Focus-Sante/ppu/commit/5ee085ef8004710fca61cd0fd188065553312f16))

Bumps [@babel/helpers](https://github.com/babel/babel/tree/HEAD/packages/babel-helpers) from 7.25.6
  to 7.27.0. - [Release notes](https://github.com/babel/babel/releases) -
  [Changelog](https://github.com/babel/babel/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/babel/babel/commits/v7.27.0/packages/babel-helpers)

--- updated-dependencies: - dependency-name: "@babel/helpers" dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump @eslint/plugin-kit in /packages/doctor-ui
  ([`a688a9e`](https://github.com/Focus-Sante/ppu/commit/a688a9e6ad82d5b8fabce7c291fe30f319bafbfa))

Bumps [@eslint/plugin-kit](https://github.com/eslint/rewrite) from 0.2.0 to 0.2.8. - [Release
  notes](https://github.com/eslint/rewrite/releases) -
  [Changelog](https://github.com/eslint/rewrite/blob/main/release-please-config.json) -
  [Commits](https://github.com/eslint/rewrite/compare/core-v0.2.0...plugin-kit-v0.2.8)

--- updated-dependencies: - dependency-name: "@eslint/plugin-kit" dependency-version: 0.2.8

dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite and @vitejs/plugin-react in /packages/webapp
  ([`9abacbe`](https://github.com/Focus-Sante/ppu/commit/9abacbe77d23c80faf0a2dc56becf5775bc820a7))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) and
  [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/tree/HEAD/packages/plugin-react).
  These dependencies needed to be updated together.

Updates `vite` from 5.4.18 to 6.3.3 - [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/main/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v6.3.3/packages/vite)

Updates `@vitejs/plugin-react` from 4.3.1 to 4.4.1 - [Release
  notes](https://github.com/vitejs/vite-plugin-react/releases) -
  [Changelog](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/CHANGELOG.md)
  -
  [Commits](https://github.com/vitejs/vite-plugin-react/commits/plugin-react@4.4.1/packages/plugin-react)

--- updated-dependencies: - dependency-name: vite dependency-version: 6.3.3

dependency-type: direct:development

- dependency-name: "@vitejs/plugin-react" dependency-version: 4.4.1

dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.14 to 5.4.16 in /packages/doctor-ui
  ([`7261a15`](https://github.com/Focus-Sante/ppu/commit/7261a153647fe3292b8fef1a6004de710b323b35))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.14 to 5.4.16. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.16/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.16/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.16 to 5.4.18 in /packages/doctor-ui
  ([`58379cd`](https://github.com/Focus-Sante/ppu/commit/58379cdd5398550d221d7700f4d431f03d84b8cf))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.16 to 5.4.18. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.18/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.18/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-version: 5.4.18

dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.16 to 5.4.18 in /packages/webapp
  ([`32b1e80`](https://github.com/Focus-Sante/ppu/commit/32b1e80ec1abb834d75af1c99e29ac82fd78429a))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.16 to 5.4.18. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.18/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.18/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-version: 5.4.18

dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>


## v0.29.1 (2025-04-16)

### Bug Fixes

- Remove unused Link import from SessionExpiredPage
  ([`9b01687`](https://github.com/Focus-Sante/ppu/commit/9b01687a4d53be1444dc7b39353df6cfed5d6180))


## v0.29.0 (2025-04-16)

### Chores

- Enhance logging and update Dockerfile CMD for proxy headers
  ([`73a8273`](https://github.com/Focus-Sante/ppu/commit/73a8273cc168516b124dd7aea5396edfc239ed29))

### Features

- Implement patient session timeout handling and add session expired page
  ([`2b0e441`](https://github.com/Focus-Sante/ppu/commit/2b0e441cec017c326c7ce9ed5e640b0a0ae17cbe))

### Refactoring

- Clean up tests by removing unused logging and assertions
  ([`eec9e83`](https://github.com/Focus-Sante/ppu/commit/eec9e83b6d8d6436683678e5d7110257916fe281))


## v0.28.5 (2025-04-15)

### Bug Fixes

- Multiple sms at once
  ([`f9c6ec1`](https://github.com/Focus-Sante/ppu/commit/f9c6ec1a366063026c16070bac62ac3a2463a607))

### Chores

- Added debugging logs to sms sent
  ([`2e74b64`](https://github.com/Focus-Sante/ppu/commit/2e74b64cc832de1e20f6ef1b76be1f812f799ae4))


## v0.28.4 (2025-04-14)

### Bug Fixes

- Update interval_ms for scheduled tasks to use a clearer calculation
  ([`e2ac855`](https://github.com/Focus-Sante/ppu/commit/e2ac855d89d348cd2d8eaa8c51647e34fe09cfea))


## v0.28.3 (2025-04-14)

### Bug Fixes

- Improve SMS sending logic by checking discharge time more clearly
  ([`34c7861`](https://github.com/Focus-Sante/ppu/commit/34c7861f018e6770b87ab32bf77555c4549533f8))


## v0.28.2 (2025-04-09)

### Bug Fixes

- Convert encounter discharge time to seconds for SMS processing
  ([`e866664`](https://github.com/Focus-Sante/ppu/commit/e866664e22a95ca6cd5b2c59bd91bb4b321197ee))


## v0.28.1 (2025-04-09)

### Bug Fixes

- Use timestamp_now for post_discharge_sms_sent_at in EncounterService
  ([`9ebd9e1`](https://github.com/Focus-Sante/ppu/commit/9ebd9e1c9aeae02370add5d9debb1baca2cbaaa8))


## v0.28.0 (2025-04-09)

### Features

- Add sender field to SMS message in CapitoleMobile
  ([`a69b01d`](https://github.com/Focus-Sante/ppu/commit/a69b01d9b50066d5569486f1a1a20b24eaf4e340))


## v0.27.3 (2025-04-08)

### Bug Fixes

- Update timestamp handling in encounter tests to use milliseconds
  ([`92a3167`](https://github.com/Focus-Sante/ppu/commit/92a316775d934bf965658f404243b992e7392002))


## v0.27.2 (2025-04-08)

### Bug Fixes

- Improve error handling in EncounterRepository and update Encounter model with UnixTimestamp for
  post_discharge_sms_sent_at
  ([`ff41268`](https://github.com/Focus-Sante/ppu/commit/ff41268d33eda3faf939c4f68ca81b73943c011c))


## v0.27.1 (2025-04-07)

### Bug Fixes

- Add redirect for non-original users in home route to /journey
  ([`b37869b`](https://github.com/Focus-Sante/ppu/commit/b37869b04749e873955066b037c6ba34427f3470))


## v0.27.0 (2025-04-07)

### Features

- Add infection service details to pediatric emergency healthcare
  ([`7f366b9`](https://github.com/Focus-Sante/ppu/commit/7f366b9eef31f2ac8ac4fe4ab2df7813b2bc89da))


## v0.26.0 (2025-04-07)

### Features

- Update patient contact model to make relationship field optional
  ([`7c6d0f5`](https://github.com/Focus-Sante/ppu/commit/7c6d0f537a41260eb24471bf4edc1719652d81ad))


## v0.25.0 (2025-04-07)

### Bug Fixes

- Ensure patient phone exists before sending SMS
  ([`c8b3c5b`](https://github.com/Focus-Sante/ppu/commit/c8b3c5bb256c07f1cee8e8233730d1b9e7c71d41))

### Features

- Add patient contacts and relative login/view functionality
  ([`7d52fed`](https://github.com/Focus-Sante/ppu/commit/7d52fed9d9e5f59c79efb7c2604e37fee6460443))

- Implement post-discharge SMS functionality for patients and relatives
  ([`25a4fdb`](https://github.com/Focus-Sante/ppu/commit/25a4fdb51a4be25b7038650ad7bddaee28e48ea9))

### Refactoring

- **ServiceRequest**: Improve latest response filtering and update test fixtures for clarity
  ([`2e36cf3`](https://github.com/Focus-Sante/ppu/commit/2e36cf3a3520f08a3ee7b86ae4ca59bdd673a286))

- **tests**: Reorganize backend tests by type (API, model, workflow)
  ([`605c525`](https://github.com/Focus-Sante/ppu/commit/605c525d56eba91fe7eef82e80b6ebc332951aa6))

- **tests**: Split workflow tests into separate files by scenario
  ([`d54d124`](https://github.com/Focus-Sante/ppu/commit/d54d12422b127039a211a2f563f4f4437eb51a8f))


## v0.24.0 (2025-04-02)

### Bug Fixes

- **EncounterService**: Update SMS content for post-discharge notifications
  ([`4857e20`](https://github.com/Focus-Sante/ppu/commit/4857e207e1cd2e6c7391d9a156497b89b3b5ee2f))

### Chores

- Add ignore_errors for ts_types.generate in mypy.ini
  ([`592a483`](https://github.com/Focus-Sante/ppu/commit/592a483b88075d774c8d887b22e10bac926886c9))

- Update poetry.lock and pyproject.toml for new dependencies and versions; add tests for
  EncounterService
  ([`37dbd39`](https://github.com/Focus-Sante/ppu/commit/37dbd39e54959e5d49e9909287fed47e3260b436))

### Features

- **EncounterService**: Add task to send delayed post-discharge SMS
  ([`c3f5b59`](https://github.com/Focus-Sante/ppu/commit/c3f5b5906470c87e24f8b7631bdc352c8b1af2a3))

### Refactoring

- **tests**: Remove unused patient object from encounter service tests
  ([`c6c9462`](https://github.com/Focus-Sante/ppu/commit/c6c94622fac9f7ed358d0e71f93e9e5a3519f3a6))


## v0.23.6 (2025-04-02)

### Bug Fixes

- Remove unused pain value state and related function in Menu component
  ([`cbdebf3`](https://github.com/Focus-Sante/ppu/commit/cbdebf3069abf30b78372f32e20ef380a41c3971))


## v0.23.5 (2025-04-01)

### Bug Fixes

- Step header grayed out for everyone
  ([`1cadd91`](https://github.com/Focus-Sante/ppu/commit/1cadd915ac1579af19c098266fe54d59494f880e))


## v0.23.4 (2025-04-01)

### Bug Fixes

- Fix step2 and 4 type errors
  ([`67b9b69`](https://github.com/Focus-Sante/ppu/commit/67b9b69784c8934a6aabf9a6271bf62ead8589f9))


## v0.23.3 (2025-04-01)

### Bug Fixes

- Update Step1 and Step3 components to use defaultActiveStepId for step title logic
  ([`473f192`](https://github.com/Focus-Sante/ppu/commit/473f1927cb67fbe7aafa72279e416b6e502119a3))

### Build System

- **deps**: Bump jinja2 from 3.1.4 to 3.1.6
  ([`ab7e528`](https://github.com/Focus-Sante/ppu/commit/ab7e528f7ab37f70db47aa5dcb066e6d042b6d83))

Bumps [jinja2](https://github.com/pallets/jinja) from 3.1.4 to 3.1.6. - [Release
  notes](https://github.com/pallets/jinja/releases) -
  [Changelog](https://github.com/pallets/jinja/blob/main/CHANGES.rst) -
  [Commits](https://github.com/pallets/jinja/compare/3.1.4...3.1.6)

--- updated-dependencies: - dependency-name: jinja2 dependency-type: indirect ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.14 to 5.4.16 in /packages/webapp
  ([`0da8f2e`](https://github.com/Focus-Sante/ppu/commit/0da8f2ea8785eda7abce3ff63ca5f999599e130d))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.14 to 5.4.16. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.16/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.16/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.9 to 5.4.14 in /packages/webapp
  ([`5a9bd3f`](https://github.com/Focus-Sante/ppu/commit/5a9bd3f40b1d4e711561034dbc6a294e13bd1e62))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.9 to 5.4.14. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.14/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.14/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>


## v0.23.2 (2025-03-31)

### Bug Fixes

- Update Step3Props to require viewedStepId
  ([`33bd7df`](https://github.com/Focus-Sante/ppu/commit/33bd7dfb0967f64ac7c2aaff84f96f5630a71c7d))


## v0.23.1 (2025-03-31)

### Bug Fixes

- Better step handling related to #82
  ([`102a1f1`](https://github.com/Focus-Sante/ppu/commit/102a1f1bda9a871a8e8231445b259330d1a79322))


## v0.23.0 (2025-03-31)

### Features

- **Menu**: Conditionally render pain assessment link for original users fixes #84
  ([`8ada65a`](https://github.com/Focus-Sante/ppu/commit/8ada65a5229fdb28e6a12955e4a94edb30f24a97))


## v0.22.0 (2025-03-28)


## v0.21.2 (2025-03-19)

### Bug Fixes

- **SolutionPresentationModal**: Remove width constraints to avoid overflowing content on tiny
  screens
  ([`2422f3b`](https://github.com/Focus-Sante/ppu/commit/2422f3b84ee94c692120dff405aee36c7def3a53))

### Features

- **header**: Wrap the logo in an anchor tag linking to the main page
  ([`2225aaa`](https://github.com/Focus-Sante/ppu/commit/2225aaaaf54d786adba18f28cacbeea6960ffdff))


## v0.21.1 (2025-03-18)


## v0.21.0 (2025-03-18)

### Bug Fixes

- **datepicker**: Fixed hover contrast and removed coloring of weekend days
  ([`f4d5954`](https://github.com/Focus-Sante/ppu/commit/f4d59540024848021f25fe2ed20d663fb51a852f))

- **i18n**: Improved French support for DateInput
  ([`ca54446`](https://github.com/Focus-Sante/ppu/commit/ca5444684acfa07e9e6af92929bee6563153b40b))

- **i18n**: Monday as first day of the week
  ([`13dce07`](https://github.com/Focus-Sante/ppu/commit/13dce07f494b47ca912689ef3358b8636e265da1))

- **i18n**: Update French date format placeholder
  ([`a25a1fe`](https://github.com/Focus-Sante/ppu/commit/a25a1fedac0d3972020cb28d79ad1794c9cc6123))

### Chores

- Update README to include 'dev' dependencies in poetry install command
  ([`ccc8c39`](https://github.com/Focus-Sante/ppu/commit/ccc8c3985c9941523a6301877eb084b3901bbbf6))

### Features

- **login**: Save form in session storage
  ([`93884d7`](https://github.com/Focus-Sante/ppu/commit/93884d7039469b355d8e2a31e68eb53b820bf902))


## v0.20.4 (2025-03-13)

### Bug Fixes

- Add assertion to ensure share_id_map is loaded in EncounterRepository
  ([`6d28351`](https://github.com/Focus-Sante/ppu/commit/6d2835104bd42c7391cd1721e631de5c752f3883))

### Chores

- Fix doctor-ui env and deps
  ([`dbe85b1`](https://github.com/Focus-Sante/ppu/commit/dbe85b17386fcdad5304f9774f205bd2d388b9d7))


## v0.20.3 (2025-03-11)

### Bug Fixes

- Implement recovery mechanism for share_id map in EncounterRepository
  ([`6ae0fde`](https://github.com/Focus-Sante/ppu/commit/6ae0fde31e2ab9ee3f0b47c2893f97beeabc57ea))

### Build System

- **deps**: Bump @eslint/plugin-kit and eslint in /packages/webapp
  ([`3a5db17`](https://github.com/Focus-Sante/ppu/commit/3a5db170913d9ea89a1f4ced648f678d66e2c076))

Bumps [@eslint/plugin-kit](https://github.com/eslint/rewrite) to 0.2.3 and updates ancestor
  dependency [eslint](https://github.com/eslint/eslint). These dependencies need to be updated
  together.

Updates `@eslint/plugin-kit` from 0.1.0 to 0.2.3 - [Release
  notes](https://github.com/eslint/rewrite/releases) -
  [Changelog](https://github.com/eslint/rewrite/blob/main/release-please-config.json) -
  [Commits](https://github.com/eslint/rewrite/compare/core-v0.1.0...plugin-kit-v0.2.3)

Updates `eslint` from 9.10.0 to 9.15.0 - [Release notes](https://github.com/eslint/eslint/releases)
  - [Changelog](https://github.com/eslint/eslint/blob/main/CHANGELOG.md) -
  [Commits](https://github.com/eslint/eslint/compare/v9.10.0...v9.15.0)

--- updated-dependencies: - dependency-name: "@eslint/plugin-kit" dependency-type: indirect

- dependency-name: eslint dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>

- **deps-dev**: Bump vite from 5.4.9 to 5.4.14 in /packages/doctor-ui
  ([`4a3b28e`](https://github.com/Focus-Sante/ppu/commit/4a3b28e1b89f2c758e5c0070757a13a870e26e84))

Bumps [vite](https://github.com/vitejs/vite/tree/HEAD/packages/vite) from 5.4.9 to 5.4.14. -
  [Release notes](https://github.com/vitejs/vite/releases) -
  [Changelog](https://github.com/vitejs/vite/blob/v5.4.14/packages/vite/CHANGELOG.md) -
  [Commits](https://github.com/vitejs/vite/commits/v5.4.14/packages/vite)

--- updated-dependencies: - dependency-name: vite dependency-type: direct:development ...

Signed-off-by: dependabot[bot] <<EMAIL>>


## v0.20.2 (2025-03-03)

### Bug Fixes

- Improve error handling for SSO authentication service availability
  ([`9091460`](https://github.com/Focus-Sante/ppu/commit/9091460e5c502ccb57b4bbcfb5fe33a500c48684))

- Include authorization headers in patient service calls
  ([`7a27104`](https://github.com/Focus-Sante/ppu/commit/7a27104f32d705989cd22894e3953be6ef127fbc))


## v0.20.1 (2025-02-25)

### Bug Fixes

- Reduce data archival retention period to 3 days
  ([`ee2376b`](https://github.com/Focus-Sante/ppu/commit/ee2376bb8e09d3b6f72e13d8fb9ff0d3ee6feec6))

- Update BackendModule to archive data after 3 days instead of 30 days - Modify archival service
  interval to retain less historical data


## v0.20.0 (2025-02-25)

### Bug Fixes

- Conditionally render share access menu item for original users
  ([`465f05c`](https://github.com/Focus-Sante/ppu/commit/465f05cc61ad700005e489a59fbb4a0fe6ad6de5))

### Features

- Improve discharge instructions SMS notification
  ([`672f599`](https://github.com/Focus-Sante/ppu/commit/672f599740cc2c5bf9042b7ee077609e119d9e3a))

- Consolidate multiple discharge instructions into a single SMS - Format instructions with bullet
  points and PDF links

- Prevent duplicate SMS notifications for service requests
  ([`7f0e756`](https://github.com/Focus-Sante/ppu/commit/7f0e7567f9073687c39d57aedd4ce2414e34a210))

- Modify NotificationService to track and prevent repeated notifications - Update notification logic
  to check against all previous service requests - Enhance test coverage for duplicate notification
  prevention

### Testing

- Update document reference file retrieval test cases
  ([`11d4c0d`](https://github.com/Focus-Sante/ppu/commit/11d4c0d6fa4573fb70e26698186373b313f6d6d1))


## v0.19.0 (2025-02-20)

### Features

- Enhance French dayjs localization and relative time handling
  ([`7d23d5f`](https://github.com/Focus-Sante/ppu/commit/7d23d5fc8e5ee9def37ee711b1d84958418ab55a))

- Add updateLocale plugin for dayjs - Customize French relative time translations - Modify relative
  time calculation to handle past dates gracefully


## v0.18.0 (2025-02-20)

### Documentation

- Update discharge instruction IDs and file path handling
  ([`b9bb011`](https://github.com/Focus-Sante/ppu/commit/b9bb01150933aa118d000a5bd29964352c381edc))

### Features

- Enhance activity retrieval and error logging
  ([`1ea7634`](https://github.com/Focus-Sante/ppu/commit/1ea76347476a9279e4fcab259697b35a7570ea8a))

- Add recursive activity definition retrieval for healthcare services - Include parent service
  activities in activity definition - Add debug print for share ID mapping in encounter repository -
  Remove debug print in authentication service

### Refactoring

- Remove unused sharedBy variable from route search
  ([`5821dcc`](https://github.com/Focus-Sante/ppu/commit/5821dcc27b3ffa3e59ecf7fb74e377aa93597894))


## v0.17.2 (2025-02-17)

### Bug Fixes

- Append .pdf extension to discharge instruction file path
  ([`1fbf044`](https://github.com/Focus-Sante/ppu/commit/1fbf0445f5f932d2a374990c6bbb91451cc12609))

### Testing

- Update healthcare service ID in discharge instructions test
  ([`394e51d`](https://github.com/Focus-Sante/ppu/commit/394e51dcc8976c4d228f0839aa472893d6edf22b))


## v0.17.1 (2025-02-17)

### Bug Fixes

- Remove unused import
  ([`52751ff`](https://github.com/Focus-Sante/ppu/commit/52751ff9d4ecf2b5e360c4fa209ef8f4d801acf6))


## v0.17.0 (2025-02-14)

### Chores

- Update configuration to add more filières for limoges
  ([`75eb19c`](https://github.com/Focus-Sante/ppu/commit/75eb19c9da9f33d3aa7535a59215003d13946cfe))

### Features

- Update estimated discharge time display and localization
  ([`93dcf99`](https://github.com/Focus-Sante/ppu/commit/93dcf992abf1b1831a6bcac132de85a3c8d30edd))


## v0.16.0 (2025-02-10)

### Features

- Add configurable base URL for SMS notifications
  ([`651d074`](https://github.com/Focus-Sante/ppu/commit/651d07480dcba2c3100b3da845de730ea7fec586))

- Replace hardcoded URLs with dynamic base URL in SMS notifications


## v0.15.4 (2025-02-10)

### Bug Fixes

- Resolve errored merge
  ([`da6bffe`](https://github.com/Focus-Sante/ppu/commit/da6bffeb23c575f8fca450985f796b1f226e854f))


## v0.15.3 (2025-01-17)

### Bug Fixes

- Discharge instruction download
  ([`d02e925`](https://github.com/Focus-Sante/ppu/commit/d02e92572e23baebe57ee3adc5a69f91312a457c))


## v0.15.2 (2025-01-17)

### Bug Fixes

- Allow patient to call /patient/self
  ([`df6a3c9`](https://github.com/Focus-Sante/ppu/commit/df6a3c9f107bbc83e5b6cb5a9a7a00536da0ba6c))

- Show discharge instructions during all cases
  ([`4025bc5`](https://github.com/Focus-Sante/ppu/commit/4025bc598e3baa9a8bcbcc23bb5216164e8b3eee))


## v0.15.1 (2025-01-17)

### Bug Fixes

- Stay logged in on refresh
  ([`93e4c45`](https://github.com/Focus-Sante/ppu/commit/93e4c45879c492fc0dd04678174e378523bf9613))


## v0.15.0 (2025-01-17)

### Chores

- Update .dockerignore to exclude .env file
  ([`86d41a8`](https://github.com/Focus-Sante/ppu/commit/86d41a8c1493541d38fa9d45a956f313a0982cef))

### Documentation

- Update README to include deployment instructions for preprod and production servers
  ([`a562a35`](https://github.com/Focus-Sante/ppu/commit/a562a35885790ef9b9fd0b208614e8dac91e58bb))

### Features

- Add end of medical care in Step3
  ([`58cac33`](https://github.com/Focus-Sante/ppu/commit/58cac333ea2c8f59397d88841b37e1dce14262c6))


## v0.14.0 (2025-01-16)

### Features

- Add EstimatedDuration component and French translations for discharge timing
  ([`d78db03`](https://github.com/Focus-Sante/ppu/commit/d78db032d83a92bc5708fc277b1c357fa69bb3a5))

- Introduced a new `EstimatedDuration` component to display the estimated time before discharge. -
  Updated `Step2` and `Step3` components to utilize the new `EstimatedDuration` component, enhancing
  the user interface. - Created a new CSS module for styling the `EstimatedDuration` component.

- Add planned_length parameter to encounter updates and enhance UI to display estimated duration
  ([`471755e`](https://github.com/Focus-Sante/ppu/commit/471755e99e485e9dd78c251a08373c32895f5864))

- Introduced `planned_length` parameter in encounter-related models and requests to specify the
  estimated duration of encounters. - Updated `update_encounter` method to handle optional
  `planned_duration`. - Enhanced API documentation to include `planned_length` in request examples.
  - Modified frontend components to display estimated duration before discharge in Steps 2 and 3 of
  the journey. - Added utility function `formatDuration` to format duration in a user-friendly way.
  - Refactored related tests to validate the new `planned_length` functionality.


## v0.13.1 (2024-12-09)

### Bug Fixes

- Update Dockerfile to support multi-platform builds and adjust .env configuration in compose.yml
  ([`20d180f`](https://github.com/Focus-Sante/ppu/commit/20d180f2936f56887a78a26e8fbd89963865f92c))


## v0.13.0 (2024-12-09)

### Features

- Add 'pending' option to DischargeDisposition in encounter model
  ([`e9a168b`](https://github.com/Focus-Sante/ppu/commit/e9a168b2358655ef19ec3b9673c45b2b95251ddb))


## v0.12.7 (2024-12-05)

### Bug Fixes

- Update README to simplify poetry commands for backend setup
  ([`89b7509`](https://github.com/Focus-Sante/ppu/commit/89b7509fdde3909be5a0b09875960c5a7168b2c9))


## v0.12.6 (2024-12-05)

### Bug Fixes

- Docker compose push disable parallel to solve 500 error
  ([`0df94e1`](https://github.com/Focus-Sante/ppu/commit/0df94e16115732bf5169d9dc1bc8d295c2c17cf0))


## v0.12.5 (2024-12-03)

### Bug Fixes

- Update DNS domain for development environment
  ([`b766b13`](https://github.com/Focus-Sante/ppu/commit/b766b13f80238359cb748521b3b33f422f92c84c))


## v0.12.4 (2024-12-02)

### Bug Fixes

- Value for ACCESS_TOKEN_EXPIRE_MINUTES
  ([`1c318b6`](https://github.com/Focus-Sante/ppu/commit/1c318b60041b409e2f1bde578a4c006079696b31))


## v0.12.3 (2024-12-02)

### Bug Fixes

- Modified frontend to follow new discharge destinations
  ([`6689dc6`](https://github.com/Focus-Sante/ppu/commit/6689dc6dc0aee11178184eac310c84992e345e9a))


## v0.12.2 (2024-12-02)

### Bug Fixes

- Re-run ts types
  ([`3e6b38d`](https://github.com/Focus-Sante/ppu/commit/3e6b38ddab604f223f8bc7acaace55ac0791e8f9))


## v0.12.1 (2024-12-02)

### Bug Fixes

- Moved default token timing to 8 hours in the proper place
  ([`64301b4`](https://github.com/Focus-Sante/ppu/commit/64301b4235c9891116faa3c5d5fea25f063765ad))

### Testing

- Added locustfile for stress testing
  ([`28a07f8`](https://github.com/Focus-Sante/ppu/commit/28a07f84317de5e431cbc67d9113ca906378bdc1))


## v0.12.0 (2024-11-25)


## v0.11.0 (2024-11-22)

### Bug Fixes

- Allow real tokens to be used while disable auth is on for frontend tests
  ([`dff42c4`](https://github.com/Focus-Sante/ppu/commit/dff42c4707ea25f8c81ed26c294c47ce2cd168e9))

- Fix incorrect merge
  ([`01c6f95`](https://github.com/Focus-Sante/ppu/commit/01c6f954e550c3940306579e85997ed04a68f179))

- Translation string
  ([`761f404`](https://github.com/Focus-Sante/ppu/commit/761f4044143ca26a598d4c4783b6f499cabbc22b))

- Ts
  ([`ca416a7`](https://github.com/Focus-Sante/ppu/commit/ca416a70158dab45e73c0e4781e5236979a0292d))

### Build System

- Bumd version
  ([`ebf9040`](https://github.com/Focus-Sante/ppu/commit/ebf90401beb5c5b2795fd0f8a1e371ea338ab9d1))

### Chores

- Format
  ([`2399c6d`](https://github.com/Focus-Sante/ppu/commit/2399c6dfa538edc6825e827b2f3ddda5e68773a0))

### Documentation

- Update documentation to reflect changes to authentication mechanisms
  ([`45f35fd`](https://github.com/Focus-Sante/ppu/commit/45f35fd437c87a0ae50871e306f9ac66b9613313))

### Features

- Add EncounterService
  ([`615c700`](https://github.com/Focus-Sante/ppu/commit/615c70049c62b7ead06a66f1389018a61f9c6f3b))

- Add icons
  ([`105c127`](https://github.com/Focus-Sante/ppu/commit/105c12785740c904acabc38a343544cf0fe2fab7))

- Add Menu component
  ([`62b8752`](https://github.com/Focus-Sante/ppu/commit/62b8752e511cbb27f577a966b9a81301535813a7))

- Add PainModal component
  ([`c572f26`](https://github.com/Focus-Sante/ppu/commit/c572f26a4551cecfa24892660a216039fc02519d))

- Add PainSlider component
  ([`0857b1b`](https://github.com/Focus-Sante/ppu/commit/0857b1ba9cb7fb2f2822544e45bcfea75ed1c51a))

- New drawer menu
  ([`9e8308a`](https://github.com/Focus-Sante/ppu/commit/9e8308a62f1d28bbc1decc9665d7e99ebd128867))


## v0.10.0 (2024-11-21)

### Features

- Let patients reset the share_id associated with their encounter
  ([`8023483`](https://github.com/Focus-Sante/ppu/commit/802348320b53265d8a929e55064efd0748207278))

- Reset share_id on request of Patient
  ([`f30e82a`](https://github.com/Focus-Sante/ppu/commit/f30e82aaa9963d07eaae5ecdc4b71a1ee1de05bb))

### Testing

- Only send end date in encounter tests
  ([`bafbe0b`](https://github.com/Focus-Sante/ppu/commit/bafbe0be0e33b0e438be21de83869a0755d59282))

- Update test with millisecond timestamps
  ([`9c11277`](https://github.com/Focus-Sante/ppu/commit/9c11277262f7167ee41a2adaf0a3146a69edcf80))


## v0.9.0 (2024-11-18)

### Bug Fixes

- Remove TImingUpdate for Timing
  ([`22e9f83`](https://github.com/Focus-Sante/ppu/commit/22e9f83e9db75bcd2bacc37a8b328d427b4c69c1))

### Chores

- Fix types
  ([`1c81601`](https://github.com/Focus-Sante/ppu/commit/1c816017900c7b17f2d8df0b6381ecc9ce25ed53))

### Features

- Decouple encounter end with discharge object
  ([`d14a30e`](https://github.com/Focus-Sante/ppu/commit/d14a30e42faac95caa8bad1590d470cfea561e45))


## v0.8.1 (2024-11-13)

### Bug Fixes

- Increase patient/relative token to 8 hours
  ([`9a26187`](https://github.com/Focus-Sante/ppu/commit/9a26187083f4f79a03476b8687a304fd58bdee41))


## v0.8.0 (2024-11-13)

### Bug Fixes

- Update DischargeDisposition options to include better choices
  ([`5d82674`](https://github.com/Focus-Sante/ppu/commit/5d8267416ea639fc063d3c710216746597e890df))

### Features

- New endpoints for listing encounters and patients for service users
  ([`b862453`](https://github.com/Focus-Sante/ppu/commit/b862453e2e44af6b9add7b0420a3ee52f76b1756))


## v0.7.0 (2024-11-05)

### Features

- Make encounter_id shorter and simpler to read/use
  ([`d53b08a`](https://github.com/Focus-Sante/ppu/commit/d53b08a7e9c1ff03b358b28389d949faa9dadcdf))

- Update logo
  ([`78577ae`](https://github.com/Focus-Sante/ppu/commit/78577ae23f65f118d94b4e4edf70b58098ef982b))

- Update wording
  ([`c6b0803`](https://github.com/Focus-Sante/ppu/commit/c6b0803908ec3c95e199fbcc2f97bb666d3e6579))


## v0.6.3 (2024-10-25)

### Bug Fixes

- Avoid http redirect caused by trailing /
  ([`4debc6f`](https://github.com/Focus-Sante/ppu/commit/4debc6fdd0e68b6e8f049140cfab41a38d8a324c))


## v0.6.2 (2024-10-24)

### Bug Fixes

- Make caddy redirect for consignes work
  ([`471cd50`](https://github.com/Focus-Sante/ppu/commit/471cd5052d79d4d5f4b4687655da704aaca74514))


## v0.6.1 (2024-10-24)

### Bug Fixes

- Remove unused frontend import
  ([`2e4b449`](https://github.com/Focus-Sante/ppu/commit/2e4b4491207dad7ed39bf5eb9f8f83b2ba00b707))


## v0.6.0 (2024-10-24)

### Bug Fixes

- Eslint issue
  ([`bfcecbd`](https://github.com/Focus-Sante/ppu/commit/bfcecbdfe53a9e97f447bd3d55f4b9653923cd51))

- Remove dead code
  ([`002d5fb`](https://github.com/Focus-Sante/ppu/commit/002d5fbcfe5541210e8c4c50d3676c8e7b2823a8))

- Wording
  ([`66e1b12`](https://github.com/Focus-Sante/ppu/commit/66e1b12a4ff20834fc4a6c252f1dc80abf256dcb))

- **api**: Updateencounter now works
  ([`9baf441`](https://github.com/Focus-Sante/ppu/commit/9baf441f78ea889280c1e298adc9104b12263813))

### Build System

- Replace yarn by npm
  ([`fa98b27`](https://github.com/Focus-Sante/ppu/commit/fa98b272ff43923def56d6dca2c7941d7f957045))

- **prettier**: Increase printWidth to 120
  ([`968e500`](https://github.com/Focus-Sante/ppu/commit/968e500deb1b14f370b164e26221ab94cb3a668d))

### Code Style

- Prettier
  ([`ae43623`](https://github.com/Focus-Sante/ppu/commit/ae436237c3a482a0ee39c7e6391b00e62353a321))

### Features

- Add models
  ([`8585f6e`](https://github.com/Focus-Sante/ppu/commit/8585f6ee2fd923a0ad414d0aa52641c6e35922cd))

- Add VITE_API_URL variable
  ([`f87860b`](https://github.com/Focus-Sante/ppu/commit/f87860bdf501932f6af362a9deae742bb7b27d31))

- Api client
  ([`495446e`](https://github.com/Focus-Sante/ppu/commit/495446efbe506530e81ad0af89a2a519a75d9128))

- Doctor UI - login page, patient list & discharge form
  ([`b29dcc5`](https://github.com/Focus-Sante/ppu/commit/b29dcc53fdff485ac177bfcbb4631d4b12e073cc))

- Improve EncounterTable
  ([`0aa5778`](https://github.com/Focus-Sante/ppu/commit/0aa5778b9789f5c43f4f7256a5cd1c33c60c5e82))

- New models
  ([`9ff23a2`](https://github.com/Focus-Sante/ppu/commit/9ff23a2fa978f3216b776d0ea382aa9348540cd2))

- Put OIDC config in environment
  ([`260951a`](https://github.com/Focus-Sante/ppu/commit/260951acc2d1ec510366a03d269afb671f4964df))

- Ui improvements - responsiveness & features
  ([`772163a`](https://github.com/Focus-Sante/ppu/commit/772163abb59afaa646fb2e79c0614f2d76a1f5f4))

- Update components with new model
  ([`e70059f`](https://github.com/Focus-Sante/ppu/commit/e70059fca9581e4f15e54dea75e3eac8f69070f1))

- Update models
  ([`76a1051`](https://github.com/Focus-Sante/ppu/commit/76a105181ccacebdd5a181085497807751a6d730))

- Update models
  ([`25cae95`](https://github.com/Focus-Sante/ppu/commit/25cae952783b8147fd5c53f46110d8de2798dff0))

- Use the `lg` breakpoint to show the sidenav (previously `md`)
  ([`b716efd`](https://github.com/Focus-Sante/ppu/commit/b716efd84fd8515adefdcb3284ad382198c514b6))

- Wording
  ([`1e58f3d`](https://github.com/Focus-Sante/ppu/commit/1e58f3d0a0a3293cc9f298bffbc756e3fd5f7165))

- Wording
  ([`33edffc`](https://github.com/Focus-Sante/ppu/commit/33edffcfde0d344004b725f49af528473f0180de))

- **api**: Added getDischargeInstructions
  ([`955eda2`](https://github.com/Focus-Sante/ppu/commit/955eda22d5cb7b714db6c9578c44c6953372bcdd))

- **api**: Getview (practitioner)
  ([`d23e9ed`](https://github.com/Focus-Sante/ppu/commit/d23e9ed9c9c869b264de1106ce8c21067acc957b))


## v0.5.1 (2024-10-23)

### Bug Fixes

- Remove bind mount for config in docker compose
  ([`cd18899`](https://github.com/Focus-Sante/ppu/commit/cd18899ff41364c17384899c7f8cff231e09b240))


## v0.5.0 (2024-10-22)

### Bug Fixes

- Remove accents in organizations.yaml
  ([`fcbf9cd`](https://github.com/Focus-Sante/ppu/commit/fcbf9cdcfece022e8ce6bdfb83c5b611eeaaa218))

- Resolve broken get_patient_or_relative_or_practitioner
  ([`9d24f17`](https://github.com/Focus-Sante/ppu/commit/9d24f17e8c801f698aff739dafe17347e5fbae9c))

- **sms**: Improved NotificationService code
  ([`cc20a18`](https://github.com/Focus-Sante/ppu/commit/cc20a18defc0e7aded832f1e27ea64cdf0f9c40b))

### Features

- Add auth to new routes
  ([`1c81443`](https://github.com/Focus-Sante/ppu/commit/1c81443114381b212372ff5202486deea64ea908))

- Add wording
  ([`af4d30c`](https://github.com/Focus-Sante/ppu/commit/af4d30ca3e6d136fc24d8f334c5747f0056bd951))

- Create practitioner view for frontend usage
  ([`1a2a839`](https://github.com/Focus-Sante/ppu/commit/1a2a839d45ced7e6b6e7a049193034e1ce1d22b1))

- Enable accordion controls
  ([`3137035`](https://github.com/Focus-Sante/ppu/commit/313703541e3b4d5f044e146ef34aeac34a7475a8))

- Enable auth on all routes, including service (through SSO)
  ([`1d739d5`](https://github.com/Focus-Sante/ppu/commit/1d739d51c3edc017ad047f1de782669a457b7f83))

- Healthcare_service in healthcare_service
  ([`cdb1565`](https://github.com/Focus-Sante/ppu/commit/cdb15652f1c8842017a2d1408bc52b660fe4eafa))

- Inject subject details into encounter list endpoint
  ([`6424051`](https://github.com/Focus-Sante/ppu/commit/6424051184bc9a8f26cee82104e008e0f2d035b5))

- Move discharge_instruction in healthcare_service routes
  ([`6482e52`](https://github.com/Focus-Sante/ppu/commit/6482e52e9827a162b896f13a4c52f498f95e976b))

- Secure share_id generation ([#38](https://github.com/Focus-Sante/ppu/pull/38),
  [`c8a3c1d`](https://github.com/Focus-Sante/ppu/commit/c8a3c1d40c6dbc25cb38cb1d9d82c1c935b066ab))

* feat: Secure share_id generation

* Only override encounter_id after obtaining activities

- Update wording
  ([`48cefce`](https://github.com/Focus-Sante/ppu/commit/48cefcef793540bdd9ef22b81b2f6086003760d3))

- Wording
  ([`e2fe2d3`](https://github.com/Focus-Sante/ppu/commit/e2fe2d3f90181e132983ea2a6dfd1573154bd363))

- **auth**: Initial implementation of auth for PPU service
  ([`79c7a5e`](https://github.com/Focus-Sante/ppu/commit/79c7a5e9e4e4f34d102e87fe0b58a3f41e87ce71))

- **Footer**: Add a link
  ([`1d36f7e`](https://github.com/Focus-Sante/ppu/commit/1d36f7e46063914afb0c2f3901d71580726879ef))

- **MedicalCare**: Handle estimated wait time when plannedEndDate < now
  ([`f72b8dd`](https://github.com/Focus-Sante/ppu/commit/f72b8dde62f9d36652e3769751b5518eca2294bc))

- **ShareAccess**: Autoclose success notification
  ([`3fc4a71`](https://github.com/Focus-Sante/ppu/commit/3fc4a71b2bfc3cfae1ee0012c908070189a1f113))

- **ShareAccess**: Update wording
  ([`18f1493`](https://github.com/Focus-Sante/ppu/commit/18f149394f5f9ab62d2d1e8339a07301dd1fc57b))

- **sms**: Send SMS to patients depending on specific triggers, or when discharge_instructions are
  provided
  ([`4d3b37f`](https://github.com/Focus-Sante/ppu/commit/4d3b37fbc551e9a22560357ebc16a0450c9ab115))

- **Step1**: Remove pain questionnaire
  ([`6759bf5`](https://github.com/Focus-Sante/ppu/commit/6759bf5aad20d4c34e249ae02ab1732d5c360eb2))

- **Step3**: Handle cases when waitCount or plannedEndDate are falsy + handle default_wait_time.
  ([`3ce815c`](https://github.com/Focus-Sante/ppu/commit/3ce815c091b11de6997875e45191bbd418964657))

### Testing

- Add new tests for routes security
  ([`5f983f3`](https://github.com/Focus-Sante/ppu/commit/5f983f3ebdb94a7d8ab095deb6006b390dc56556))

- Change tests to reflect change in Encounter body
  ([`40a04ed`](https://github.com/Focus-Sante/ppu/commit/40a04edc29ec25484e6c917828c21038c347de4d))

- Fix failing tests
  ([`2d17f4c`](https://github.com/Focus-Sante/ppu/commit/2d17f4c5fe949b0302dbe2e8006e98e3fc33a211))

- Fix test for new auth method
  ([`39ded0a`](https://github.com/Focus-Sante/ppu/commit/39ded0a6e9d82adff1a6cc73853966132ea937e1))

- Improve/add more tests
  ([`38cc455`](https://github.com/Focus-Sante/ppu/commit/38cc455dbf3c51046adfd1ed8931d18879560da1))

- **sms**: Added number of sent SMS check in tests
  ([`f70c8b8`](https://github.com/Focus-Sante/ppu/commit/f70c8b82e6b7697eb805380173fc52a225f79b12))


## v0.4.0 (2024-10-21)


## v0.3.0 (2024-10-21)

### Features

- Add default_wait_time
  ([`5251b8d`](https://github.com/Focus-Sante/ppu/commit/5251b8d263cc9b9710c5f21ba419465897d7905a))


## v0.2.1 (2024-10-21)

### Bug Fixes

- Allow PATCH method in CORS settings
  ([`1c01ee4`](https://github.com/Focus-Sante/ppu/commit/1c01ee4c2d34429fbd63f6bae1f85c44e233afff))

PATCH is required to update Encounters through the API

- Move discharge_instructions into healthcare_service scope
  ([`7c9cf55`](https://github.com/Focus-Sante/ppu/commit/7c9cf5503331b8340314aecb86f0ead52b17498c))

### Features

- Serve discharge_instruction files directly from the backend
  ([`6e95cc3`](https://github.com/Focus-Sante/ppu/commit/6e95cc3c7bb99dcc7340588d9a14859740c4d1b0))


## v0.2.0 (2024-10-16)

### Features

- Use default env values
  ([`1d18097`](https://github.com/Focus-Sante/ppu/commit/1d18097f8c679ba22ef46e889ec84cb17ec59f60))


## v0.1.0 (2024-10-07)

### Bug Fixes

- Remove unwanted padding-left
  ([`37c3da0`](https://github.com/Focus-Sante/ppu/commit/37c3da0b6779600f84de266e4a3f3a0ce9255879))

- Typo
  ([`577d1aa`](https://github.com/Focus-Sante/ppu/commit/577d1aa0df8f763890c83257e4aaff2151311b7f))

- **webapp**: Step2 should be active when status is completed
  ([`9afa581`](https://github.com/Focus-Sante/ppu/commit/9afa5814fb41752e3bcfd3eae49c1def08aef790))

### Chores

- Add GH action for semantic-release
  ([`b7ba259`](https://github.com/Focus-Sante/ppu/commit/b7ba25968b657fcdb3fc711dc44d76c9bbff045c))

### Documentation

- Correct GH repo
  ([`1739f14`](https://github.com/Focus-Sante/ppu/commit/1739f143f51d70c1da7cf43a7301b76c535966d9))

### Features

- 1.0rc1
  ([`3bbf881`](https://github.com/Focus-Sante/ppu/commit/3bbf881a63b51105b5fc938e4fefeb5737638a5a))

- Add automatic versioning
  ([`e0cabb8`](https://github.com/Focus-Sante/ppu/commit/e0cabb8a8a02f2b502053dae5ee7c851db53bf1c))

- Share in nav + medical care queue + step2 states + remove lorem ipsum + discharge instructions
  ([`5b65c1d`](https://github.com/Focus-Sante/ppu/commit/5b65c1dc366999bffc8848e4e55ab45b4887c532))

- Static pages iterations
  ([`585bcee`](https://github.com/Focus-Sante/ppu/commit/585bcee4202a3a1455e4123cc2ed8038cc218460))

/home /journey /patient/:id

- Update navbar icons
  ([`e9fc6c1`](https://github.com/Focus-Sante/ppu/commit/e9fc6c163d61bf078d979d089d7dec3333454998))

- Update translation
  ([`3c5c025`](https://github.com/Focus-Sante/ppu/commit/3c5c0252c0502e13064f473be7b167e7d7074144))

- Vite boilerplate + static sign-in
  ([`d9cd5c4`](https://github.com/Focus-Sante/ppu/commit/d9cd5c431f509ce19187dac80d0896587e08ba2b))
