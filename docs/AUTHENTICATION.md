# Authentication Documentation

The system supports multiple authentication methods depending on the user type. In development mode, authentication can be bypassed using special headers.

## Authentication Methods

### 1. Patient & Relative Authentication

Patients and relatives authenticate through the `/login/access-token` endpoint:

```http
POST /api/v1/login/access-token

Query Parameters:
- id: The encounter_id (for patients) or share_id (for relatives)
- truncated_last_name: First 3 letters of the patient's last name
- birth_date: <PERSON><PERSON>'s birth date (YYYY-MM-DD format)

Response:
{
    "access_token": "jwt_token_here",
    "token_type": "bearer"
}
```

Use the returned token in subsequent requests:

```http
Authorization: Bearer jwt_token_here
```

### 2. Practitioner Authentication

Practitioners authenticate through Keycloak SSO. The backend validates the JWT bearer tokens issued by Keycloak.

```http
Authorization: Bearer keycloak_jwt_token
```

### 3. Service Authentication

Services (like "Urgences Chrono") authenticate through Keycloak SSO using client credentials flow. To obtain an access token:

```http
POST {keycloak_url}/realms/{realm}/protocol/openid-connect/token
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
client_id={your_client_id}
client_secret={your_client_secret}
```

The response will include an access token valid for one hour:

```json
{
  "access_token": "eyJ...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

Use this token in subsequent requests:

```http
Authorization: Bearer {access_token}
```

## Development Mode Authentication

In development, authentication can be bypassed using special headers. By default, requests are authenticated as a service user.

### Development Headers

```http
X-Dev-User-Id: "1"              # User ID (see below for specific values)
X-Dev-Org-Id: "demo-akihospit"  # Organization ID
X-Dev-Role: "SERVICE"           # Role (SERVICE, PRACTITIONER, PATIENT, or RELATIVE)
```

Important notes:

- These headers only work when `DISABLE_AUTH=true` (default in development)
- If a valid authentication token is provided, it takes precedence over the development headers
- This allows testing the frontend as a patient while using development mode for other services

### Example Development Headers

For a practitioner (user_id is the RPPS number):

```http
X-Dev-User-Id: "12345678"         # RPPS number
X-Dev-Org-Id: "demo-akihospit"
X-Dev-Role: "PRACTITIONER"
```

For a patient (user_id is the encounter_id):

```http
X-Dev-User-Id: "encounter_123"    # encounter_id
X-Dev-Org-Id: "demo-akihospit"
X-Dev-Role: "PATIENT"
```

For a relative (user_id is the share_id):

```http
X-Dev-User-Id: "share_456"        # share_id
X-Dev-Org-Id: "demo-akihospit"
X-Dev-Role: "RELATIVE"
```
