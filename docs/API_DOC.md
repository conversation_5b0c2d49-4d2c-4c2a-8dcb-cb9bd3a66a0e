# PPU API Documentation

This guide explains how to use the API to manage patient encounters in the hospital system. The API follows RESTful principles and can be explored using the Swagger UI provided by FastAPI at `/docs`.

## Authentication

All API endpoints require authentication. See [Authentication Documentation](./AUTHENTICATION.md) for detailed information about:

- Patient & Relative authentication through `/login/access-token`
- Practitioner authentication through Keycloak SSO
- Service authentication through Keycloak SSO
- Development mode authentication using X-Dev headers

## Basic Workflow

1. Create a Patient
2. Create an Encounter
3. Create Service Requests for IOA and Medical Care (required)
4. Optionally create additional Service Requests (e.g., X-rays, blood tests)
5. Manage Service Requests for the Encounter
6. Update Encounter details as needed
7. End the Encounter and/or Add Discharge Information

## Detailed Steps

### 1. Create a Patient

First, create a patient record:

- Endpoint: `POST /api/v1/patient`
- Body example:
  ```json
  {
    "name": {
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>"
    },
    "birth_date": "1990-01-01",
    "phone": "+33612345678"
  }
  ```
- The response will include a `patient_id` which you'll need for creating encounters.
- Note: While patients can be reused, there is currently limited functionality for this.

### Patient

Represents a patient in the system.

```json
{
  "patient_id": "string (PatientId)",
  "name": {
    "first_name": "string",
    "last_name": "string"
  },
  "birth_date": "string (YYYY-MM-DD)",
  "phone": "string | null",
  "contact": [
    {
      "relationship": ["relative"], // optional
      "telecom": [
        {
          "system": "sms",
          "value": "string"
        }
      ]
    }
  ]
}
```

**Fields:**

- `patient_id`: **Required**. Unique identifier for the patient.
- `name`: **Required**. Object containing the patient's first and last names.
  - `first_name`: **Required**. Patient's first name.
  - `last_name`: **Required**. Patient's last name.
- `birth_date`: **Required**. Patient's date of birth in `YYYY-MM-DD` format.
- `phone`: _Optional_. Patient's phone number (must start with `+33` if provided).
- `contact`: _Optional_. An array of contact persons for the patient.
  - `relationship`: _Optional_. Type of relationship (currently only `"relative"` supported). Defaults to empty if omitted.
  - `telecom`: **Required**. An array of communication methods for the contact.
    - `system`: **Required**. The type of communication (currently only `sms` is supported).
    - `value`: **Required**. The actual contact detail (phone number, must start with `+33`).

### 2. Create an Encounter

Using the `patient_id`, create an encounter:

- Endpoint: `POST /api/v1/encounter`
- Body example:
  ```json
  {
    "subject": "patient_id_here",
    "service_provider": "demo-akihospit",
    "service_type": "demo-akihospit-emergency",
    "identifier": [
      {
        "system": "https://www.demo-akihospit.com",
        "value": "unique_encounter_identifier"
      }
    ]
  }
  ```
- The `identifier` provided here is crucial. You'll use this to reference the encounter in future requests.
- Note: The `service_provider` should be one of the organizations defined in the `organizations.yaml` file.
- Note: The `service_type` should be one of the healthcare services defined in the `organizations.yaml` file.

### 3. Create Required Service Requests: IOA and Medical Care

After creating the encounter, you must create service requests for IOA (Infirmière d'orientation et d'accueil) and Medical Care. These are special activities that receive particular treatment by the frontend and are required for every encounter:

- Endpoint: `POST /api/v1/service_request`
- Body example for IOA:
  ```json
  {
    "encounter_id": "encounter_id_here",
    "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
    "status": "not-yet-started",
    "encounter_timing": {
      "planned_end_date": 1639012800000,
      "wait_count": 5
    }
  }
  ```
- Body example for Medical Care:
  ```json
  {
    "encounter_id": "encounter_id_here",
    "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
    "status": "not-yet-started",
    "encounter_timing": {
      "planned_end_date": 1639012800000,
      "wait_count": 3
    }
  }
  ```
- Note: `planned_end_date` is a Unix timestamp in milliseconds. Only include `planned_end_date` or `wait_count` if the values are known and should be displayed.
- Possible `status` values: "not-yet-started", "in-progress", "completed", "cancelled"
- Important: The `activity_definition_id` should be one of the service request types defined in the `organizations.yaml` file.

#### IOA Status Meanings:

- "not-yet-started": The nurse will see the patient soon, but the patient should not go to her desk yet. No wait time is shown for this status.
- "in-progress": The patient needs to go to the nurse's desk.
- "completed": The patient has seen the nurse, and she has assigned the patient to the proper service. The patient is now waiting for a doctor.

#### Medical Care Status Meanings:

- "not-yet-started": The patient is waiting to see the doctor. The estimated wait time and number of people ahead in the queue are displayed.
- "in-progress": The patient is being seen by the doctor or needs to go see the doctor.
- "completed": The patient has seen the doctor and the initial assessment is done. Note that this doesn't mean the patient has left the hospital, just that the first doctor visit is complete.

### 4. Create Optional Service Requests

You can create additional service requests for other activities such as X-rays or blood tests:

- Use the same endpoint as above (`POST /api/v1/service_request`)
- Body example for an X-ray:
  ```json
  {
    "encounter_id": "encounter_id_here",
    "activity_definition_id": "demo-akihospit-emergency-activity-xray",
    "status": "not-yet-started",
    "encounter_timing": {
      "planned_end_date": 1639015500000,
      "wait_count": 2
    }
  }
  ```
- Note: As with IOA and Medical Care, only include `planned_end_date` or `wait_count` if the values are known and should be displayed.

### 5. Manage Service Requests

To update any service request for an encounter:

- Use the same endpoint as above (`POST /api/v1/service_request`)
- Body example:
  ```json
  {
    "encounter_id": "encounter_id_here",
    "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
    "status": "in-progress",
    "encounter_timing": {
      "planned_end_date": 1639012800000,
      "wait_count": 1
    }
  }
  ```
- Remember to update both required (IOA and Medical Care) and any optional service requests as needed.

### 6. Update Encounter

To update encounter details:

- Endpoint: `PATCH /api/v1/encounter/{encounter_id}`
- Optional query parameter: `identifier=system|value` (use the identifier provided during encounter creation)
- Body example (only include fields you want to update):
  ```json
  {
    "service_type": "demo-akihospit-emergency-pediatriques",
    "planned_length": 240
  }
  ```
- The `planned_length` parameter specifies the estimated duration of the entire encounter in minutes

### 7. End Encounter and Manage Discharge Information

The encounter end time and discharge information are separate concerns. You can:

- End an encounter without providing discharge information
- Add discharge information while the encounter is still ongoing
- Add or update discharge information after the encounter has ended

#### To End an Encounter:

- Endpoint: `PATCH /api/v1/encounter/{encounter_id}`
- Optional query parameter: `identifier=system|value`
- Body example:
  ```json
  {
    "actual_period": {
      "end": 1639012800000
    }
  }
  ```
- Note: Only include the "end" field in actual_period, do not include "start"

#### To Add/Update Discharge Information:

- Endpoint: `PATCH /api/v1/encounter/{encounter_id}`
- Optional query parameter: `identifier=system|value`
- Body example:
  ```json
  {
    "discharge": {
      "disposition": "home",
      "instructions": [
        {
          "id": "TC",
          "name": "Head Trauma Instructions"
        }
      ]
    }
  }
  ```
- Or with destination information:
  ```json
  {
    "discharge": {
      "disposition": "same-hospital",
      "destination": {
        "main_destination": "Cardiology Department",
        "details": "Room 424"
      },
      "instructions": [
        {
          "id": "Platre",
          "name": "Cast Care Instructions"
        }
      ]
    }
  }
  ```
- Note: The instruction `id` must reference a valid document ID from the `/api/v1/healthcare_service/document_reference` endpoint. These are predefined PDF documents containing standardized discharge instructions.
- Note: When updating discharge information, you must send the complete discharge object, including any existing fields you want to keep.

## Notes

- All date fields in requests and responses are Unix timestamps in milliseconds.
- For service requests, `planned_end_date` and `wait_count` should only be included if the information is known and should be displayed.
- The encounter end time (actual_period.end) and discharge information are independent - you can set either one without the other.
- When updating discharge information, you must send the complete discharge object as patches are not supported for this field.
- Use the Swagger UI at `/docs` to explore all available endpoints and their required parameters.
- Remember to include proper authentication headers where required.
- Refer to the `organizations.yaml` file for the list of valid organizations, service request types (activity_definition_id), and discharge instructions.

This API allows you to manage the entire patient journey through the emergency department, from admission to discharge, by creating encounters and managing both required (IOA and Medical Care) and optional service requests as the patient progresses through various stages of care.
