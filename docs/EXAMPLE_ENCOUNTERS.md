# Example Encounters

This document provides examples of typical API call sequences for different patient encounter scenarios. Each example shows the exact API calls made, the request bodies, and important response data we need to keep.

## Example 1: Typical Emergency Department Visit

This example shows a standard visit where a patient completes both IOA and medical care normally.

### 1. Create Patient

```http:POST /api/v1/patient
{
  "name": {
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>"
  },
  "birth_date": "1990-01-01",
  "phone": "+33612345678",
  "contact": [
    {
      "relationship": ["relative"],
      "telecom": [
        {
          "system": "sms",
          "value": "+33787654321"
        }
      ]
    },
    {
      "relationship": ["relative"],
      "telecom": [
        {
          "system": "sms",
          "value": "+33698765432"
        }
      ]
    }
  ]
}
```

Keep from response: `patient_id`

### 2. Create Encounter

```http:POST /api/v1/encounter
{
  "subject": "patient_id_from_step_1",
  "service_provider": "demo-akihospit",
  "service_type": "demo-akihospit-emergency",
  "identifier": [
    {
      "system": "https://www.demo-akihospit.com",
      "value": "ENCOUNTER123"
    }
  ]
}
```

Keep from response: `encounter_id`

### 3. Create IOA Service Request

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id_from_step_2",
  "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
  "status": "not-yet-started",
  "encounter_timing": {
    "planned_end_date": 1639012800000,
    "wait_count": 5
  }
}
```

### 4. Update IOA to In Progress

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id_from_step_2",
  "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
  "status": "in-progress",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

### 5. Complete IOA and Create Medical Care Request

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id_from_step_2",
  "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
  "status": "completed",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id_from_step_2",
  "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
  "status": "not-yet-started",
  "encounter_timing": {
    "planned_end_date": 1639015500000,
    "wait_count": 3
  }
}
```

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "planned_length": 180
}
```

### 5.1 Update Encounter with Filière

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "service_type": "desired_filiere_value"
}
```

### 5.2 Update Encounter with both planned_length and filiere

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "planned_length": 180,
  "service_type": "desired_filiere_value"
}
```

### 6. Update Medical Care to In Progress

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id_from_step_2",
  "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
  "status": "in-progress",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

### 7. Complete Medical Care and End Encounter

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id_from_step_2",
  "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
  "status": "completed",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "actual_period": {
    "end": 1639019100000
  },
  "discharge": {
    "disposition": "home",
    "instructions": [
      {
        "id": "TC",
        "name": "Head Trauma Instructions"
      }
    ]
  }
}
```

## Example 2: Patient Leaves During IOA

This example shows a patient who leaves during the IOA process.

### 1-3. Same as Example 1

(Create patient, create encounter, create IOA service request)

### 4. Update IOA to In Progress

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id",
  "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
  "status": "in-progress",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

### 5. Cancel IOA and End Encounter

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "actual_period": {
    "end": 1639013400000
  },
  "discharge": {
    "disposition": "escaped"
  }
}
```

## Example 3: Adding Discharge Instructions During and After Visit

This example shows adding discharge instructions while the doctor is seeing the patient and updating them after the encounter ends.

### 1-5. Same as Example 1

(Create patient, create encounter, create/complete IOA, create medical care)

### 6. Update Medical Care to In Progress and Add Initial Discharge Instructions

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id",
  "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
  "status": "in-progress",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "planned_length": 240,
  "discharge": {
    "disposition": "home",
    "instructions": [
      {
        "id": "TC",
        "name": "Head Trauma Instructions"
      }
    ]
  }
}
```

### 7. Complete Medical Care and End Encounter

```http:POST /api/v1/service_request
{
  "encounter_id": "encounter_id",
  "activity_definition_id": "demo-akihospit-emergency-activity-medical-care",
  "status": "completed",
  "encounter_timing": {
    "planned_end_date": 0,
    "wait_count": 0
  }
}
```

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "actual_period": {
    "end": 1639019100000
  }
}
```

### 8. Update Discharge Instructions After Encounter End

```http:PATCH /api/v1/encounter/{encounter_id}
{
  "discharge": {
    "disposition": "home",
    "instructions": [
      {
        "id": "TC",
        "name": "Head Trauma Instructions"
      },
      {
        "id": "discharge-2",
        "name": "Return if fever develops"
      },
      {
        "id": "discharge-3",
        "name": "Follow up with primary care in 1 week"
      }
    ]
  }
}
```

Note: All timestamps in this document are Unix timestamps in milliseconds. The actual values should be replaced with current timestamps in your implementation.
