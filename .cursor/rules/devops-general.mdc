---
description: General DevOps guidelines
globs: deployment/*,packages/devtools/*
alwaysApply: false
---
# DevOps Guidelines (Terraform and Ansible)

## General

- **Infrastructure as Code:** All infrastructure is defined as code using Terraform, primarily for the development environment.
- **Configuration Management:** Use Ansible for server configuration.
- **Secrets Management:** Sensitive information is managed using Scaleway Secrets Manager. Some non-sensitive configuration values can be stored directly in the code.
- **Environments:** The project utilizes several environments (e.g., dev, uat, prod, infra). Terraform is currently focused on the `dev` environment.
- **Scaleway**: We use Scaleway as a cloud provider.

## Terraform

1. **Directory Structure:**

   - `deployment/terraform/infrastructure`: Defines core infrastructure resources (e.g., buckets, secrets, IAM) for the dev environment.
   - `deployment/terraform/application`: Defines application-specific resources (e.g., instances, DNS) for the dev environment.

2. **State Management:**

   - Infrastructure state for `dev` is stored in the repository (for bootstrapping purposes).
   - Application state for `dev` is stored remotely in Scaleway Object Storage buckets.

3. **Workflows:**
   - Use the CLI to apply Terraform changes to the `dev` environment: `./cli devops terraform {application|infrastructure} apply --env=dev`
   - To destroy the `dev` environment: `./cli devops terraform {application|infrastructure} destroy --env=dev`

## Ansible

- Ansible is used for configuring servers and deploying the application across relevant environments.
- The main playbook is in `deployment/ansible/playbook.yaml`.
- Use the CLI for running Ansible commands: `./cli devops ansible <command> --env=<target-env>`

## CLI

- **Usage:** The `cli` script provides a convenient way to run common development and deployment tasks.
- **Commands:** Use `./cli devops` to see available DevOps commands.
- **Environments**: Use the `--env` option to specify the target environment for commands (e.g., `./cli devops deploy --env dev`).

## Related Guidelines

- For Python guidelines, see [Python Guidelines](mdc:python-general.prompt.md)
- For project overview, see [Project Overview](mdc:project-specific.prompt.md)
