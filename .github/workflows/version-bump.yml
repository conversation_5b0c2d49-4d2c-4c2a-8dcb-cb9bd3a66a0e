name: Bump Version

on:
  push:
    branches:
      - master

jobs:
  bump-version:
    runs-on: ubuntu-latest
    
    permissions:
        actions: write
        contents: write
  
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
            fetch-depth: 0
      
      - name: Bump version using semantic-release
        uses: python-semantic-release/python-semantic-release@master
        with:
            github_token: ${{ secrets.GITHUB_TOKEN }}