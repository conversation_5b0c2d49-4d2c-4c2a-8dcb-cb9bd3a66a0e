---
applyTo: "packages/webapp/**,**/*.tsx,**/*.module.css,**/*.ts"
---

# General TypeScript Guidelines

## Style and Formatting

- **Code Style:** Follow the Airbnb JavaScript Style Guide.
- **Formatting:** Use Prettier for automatic code formatting.
- **File Extensions:** Use `.tsx` for files with JSX, and `.ts` for files without JSX.
- **Types:** Prefer interfaces over types for object shapes where appropriate. Use PascalCase for type names.
- **Imports:** Use absolute imports with the `@/` alias (e.g., `import { Button } from '@/components/Button';`)
- **Avoid `any`:** Use strong typing throughout the codebase. Use `unknown` when necessary.

## Project Structure

- **Components:** Reusable UI components in `packages/webapp/src/components`.
- **Pages:** Route components in `packages/webapp/src/pages`.
- **Models:** TypeScript interfaces and types in `packages/webapp/src/models`, typically manually defined to align with backend models and frontend needs.
- **Services:** API interaction modules in `packages/webapp/src/services`.
- **Hooks:** Custom React hooks in `packages/webapp/src/hooks`.
- **Stores:** Zustand state management in `packages/webapp/src/stores`.
- **Utilities:** Helper functions in `packages/webapp/src/helpers`.

## React Specifics

- **Use functional components** with hooks instead of class components.
- **Define interfaces or types** for component props using TypeScript.

## Antipatterns

- No class components
- No global state with `useContext` (prefer Zustand for global state)
- No relative imports (use absolute imports with `@` alias)
- No `.js` or `.jsx` extensions
