---
applyTo: "packages/webapp/**,**/*.tsx,**/*.css,**/*.ts"
---

# React Specific Guidelines

## Build Tools

- **Vite:** Used as our build tool and development server (see `vite.config.ts` and `package.json` scripts).

## Component Structure

- **Use functional components** with hooks instead of class components.
- **Each component** should generally be in its own directory with related files (e.g., `src/components/Button/Button.tsx`).
- **Use PascalCase** for component names (e.g., `PatientList`).
- **Define TypeScript interfaces or types** for all component props.

## Styling

- **Mantine UI:** Used for core UI components and hooks (see `src/providers/MantineProvider.tsx`).
- **CSS Modules:** Used for component-specific styling (e.g., `Button.module.css`).

## Data and State

- **API Interaction:** Use services in `src/services` directory (e.g., `src/services/view.service.ts`).
- **Authentication:** Implemented using custom logic in conjunction with Keycloak for SSO (see `src/stores/slices/auth.slice.ts` and `src/services/auth.service.ts`).
- **State Management:** Zustand for global state management (see `src/stores/`).

## Routing

- **TanStack Router:** Used for navigation, with route definitions in the `src/routes/` directory and a generated `src/routeTree.gen.ts`.

## Testing

- **Cypress:** For end-to-end tests.

## Related Guidelines

- For general TypeScript guidelines, see [TypeScript Guidelines](mdc:typescript-general.prompt.md)
