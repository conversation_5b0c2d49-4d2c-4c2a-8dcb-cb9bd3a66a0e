---
applyTo: "packages/backend/**,**/*.py"
---

# FastAPI Specific Guidelines

These guidelines apply specifically to the FastAPI backend, located in `packages/backend`.

## API Design

- **RESTful Principles:** Design the API following RESTful principles.
- **FHIR Compatibility:** Try to stay close to FHIR standards where applicable, but this is not a strict requirement.
- **Versioning:** Use API versioning (e.g., `/api/v1/`). The current version is defined in `packages/backend/server/config.py`.
- **Endpoints:** Endpoints are organized into modules within `packages/backend/server/api/routes`. Each module corresponds to a specific resource.
- **Request/Response Models:** Define request and response models using Python dataclasses. These models are located in `packages/backend/models`.
- **HTTP Methods:** Use appropriate HTTP methods for each operation (`GET`, `POST`, `PATCH`, `DELETE`).
- **Authentication:** Implemented in `packages/backend/server/api/deps.py`. Use dependency injection to enforce authentication on routes.

## Database

- **File-Based Database:** The project uses a simple file-based database for storing data. Each entity is stored as a separate JSON file within a designated directory.
- **Repository Pattern:** Use the repository pattern to abstract data access logic. Repositories are defined in `packages/backend/spi/repositories.py` and implemented in `packages/backend/impl/repositories`.
- **Data Serialization/Deserialization:** Use `orjson` for fast JSON serialization and deserialization. Use `dacite` for converting between dictionaries and dataclasses.

## Services

- **Business Logic:** Business logic is implemented in service classes located in `packages/backend/impl/services`.

## Configuration

- **Environment Variables:** Use environment variables for configuration that may vary between environments. These are defined in `packages/backend/server/config.py`.

## Documentation

- **Swagger UI:** FastAPI automatically generates interactive API documentation using Swagger UI. Access it at `/docs`.

## Testing

- **Test Framework:** Use `pytest` for writing and running tests.
- **Test Location:** Tests are located in `packages/backend/tests`.
- **Fixtures:** Use `pytest` fixtures (`conftest.py`) to manage test dependencies and setup.

## Related Guidelines

- For general Python guidelines, see [Python Guidelines](mdc:python-general.prompt.md)
