---
applyTo: "**"
---

# Project Overview

This project is a key component of the Urgences Chrono service, offering patients a personalized interface to monitor their emergency care journey in French hospitals. Patient records are sourced directly from hospitals via the Urgences Chrono service and made accessible to patients through an SMS link.

Key features include:

- The ability for patients to share their care journey with relatives.
- Provision of after-care documents, tailored to the patient's condition, for easy access on their mobile devices.

## Project Structure

The project follows a monorepo structure with the following main components:

- `packages/backend`: FastAPI backend application
- `packages/webapp`: React frontend application
- `packages/devtools`: Development tools and utilities
- `deployment`: Infrastructure as code (Terraform) and configuration management (Ansible)

## Key Technologies

- **Backend**: Python with FastAPI
- **Frontend**: TypeScript with React and Mantine UI
- **Infrastructure**: Terraform (for dev environment) and Ansible on Scaleway cloud
- **Authentication**: Keycloak for SSO
