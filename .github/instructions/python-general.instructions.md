---
applyTo: "packages/backend/**,**/*.py"
---

# General Python Guidelines

## Style and Formatting

- **Code Style:** Follow PEP 8.
- **Formatting:** Use `ruff` for automatic formatting.
- **Type Hinting:** Use type hints for all function arguments and return values.
- **Docstrings:** Write clear docstrings using Google format.
- **Error Handling:** Use specific exceptions. Use `HTTPException` for API errors.
- **Logging:** Use `logging` module, not `print()` statements. Standard logging practices are encouraged.
- **Asynchronous:** Use `async` and `await` for asynchronous operations.

## Project Structure

- **Backend Structure:**
  - `models`: Data models using Python dataclasses.
  - `repositories`: Data access logic.
  - `services`: Business logic.
  - `server/api`: FastAPI endpoints.
  - `spi`: Service Provider Interfaces.
  - `impl`: Implementations of SPIs.

## Dependency Management

- **Poetry:** Used for dependency management. Dependencies defined in `pyproject.toml`.

## Antipatterns

- No `print()` for debugging
- No bare `except:` clauses
- No relative imports
