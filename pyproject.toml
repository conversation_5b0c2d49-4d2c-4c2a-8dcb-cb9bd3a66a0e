[tool.poetry]
name = "ppu"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.111.0"
PyJWT = "^2.9.0"
overrides = "^7.7.0"
aiofiles = "^24.1.0"
orjson = "^3.10.3"
dacite = "^1.8.1"
async-lru = "^2.0.4"
cryptography = "^44.0.1"
requests = "^2.32.3"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
black = "^24.4.2"
click = "^8.1.7"
pyyaml = "^6.0.1"
mypy = "^1.10.0"
pytest = "^8.3.2"
scaleway = "^2.6.0"
setuptools = "^74.1.2"
types-requests = "^2.32.0.20240914"
python-semantic-release = "^9.9.0"
pytest-cov = "^6.0.0"
locust = "^2.32.3"
freezegun = "^1.5.1"
pytest-asyncio = "^0.26.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.semantic_release]
assets = []
build_command_env = []
commit_message = "{version}\n\nAutomatically generated by python-semantic-release"
commit_parser = "angular"
logging_use_named_masks = false
major_on_zero = true
allow_zero_version = true
no_git_verify = false
tag_format = "v{version}"
version_variables = ["packages/backend/version.py:__version__"]

[tool.semantic_release.branches.main]
match = "(main|master)"
prerelease_token = "rc"
prerelease = false

[tool.semantic_release.changelog]
template_dir = "templates"
changelog_file = "CHANGELOG.md"
exclude_commit_patterns = []

[tool.semantic_release.changelog.environment]
block_start_string = "{%"
block_end_string = "%}"
variable_start_string = "{{"
variable_end_string = "}}"
comment_start_string = "{#"
comment_end_string = "#}"
trim_blocks = false
lstrip_blocks = false
newline_sequence = "\n"
keep_trailing_newline = false
extensions = []
autoescape = true

[tool.semantic_release.commit_author]
env = "GIT_COMMIT_AUTHOR"
default = "semantic-release <semantic-release>"

[tool.semantic_release.commit_parser_options]
allowed_tags = ["build", "chore", "ci", "docs", "feat", "fix", "perf", "style", "refactor", "test"]
minor_tags = ["feat"]
patch_tags = ["fix", "perf"]
default_bump_level = 0

[tool.semantic_release.remote]
name = "origin"
type = "github"
ignore_token_for_push = false
insecure = false

[tool.semantic_release.publish]
dist_glob_patterns = ["dist/*"]
upload_to_vcs_release = true
