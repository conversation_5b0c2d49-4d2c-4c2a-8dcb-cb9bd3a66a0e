from pathlib import Path
from packages.backend.impl.module import BackendModuleConfiguration


SECRET_KEY = "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 8

# This need to be dynamically filled for the right env.
SSO_JWKS_URL = "https://sso-recette.urgenceschrono.net/realms/ucrecette/protocol/openid-connect/certs"


config = BackendModuleConfiguration(
    API_V1_STR="/api/v1",
    ENV="local",
    DB_PATH=Path(".db"),
    ASSET_PATH=Path("config"),
    SECRET_KEY=SECRET_KEY,
    ALGORITHM=ALGORITHM,
    ACCESS_TOKEN_EXPIRE_MINUTES=ACCESS_TOKEN_EXPIRE_MINUTES,
    SSO_JWKS_URL=SSO_JWKS_URL,
)
