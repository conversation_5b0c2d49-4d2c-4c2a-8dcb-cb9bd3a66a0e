import asyncio
from contextlib import asynccontextmanager
import logging
import logging.config  # Import the config module
import traceback
from typing import Callable, Coroutine, List
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from packages.backend.impl.module import BackendModule, ScheduledTask
from packages.backend.server.api.deps import _module
from packages.backend.server.api.main import api_router
from packages.backend.server.config import config

from packages.backend.version import __version__

# ==============================================
# Logging Configuration
# ==============================================

LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "()": "uvicorn.logging.DefaultFormatter",
            "fmt": "%(levelprefix)s %(asctime)s | %(message)s",
            "use_colors": None,
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
        "access": {
            "()": "uvicorn.logging.AccessFormatter",
            "fmt": '%(levelprefix)s %(asctime)s | %(client_addr)s - "%(request_line)s" %(status_code)s',
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "default": {
            "formatter": "default",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stderr",
        },
        "access": {
            "formatter": "access",
            "class": "logging.StreamHandler",
            "stream": "ext://sys.stdout",
        },
    },
    "loggers": {
        "": {"handlers": ["default"], "level": "INFO"},  # Root logger
        "uvicorn.error": {"level": "INFO"},  # Keep uvicorn error logs
        "uvicorn.access": {
            "handlers": ["access"],
            "level": "INFO",
            "propagate": False,
        },  # Configure access logs
        "task_scheduler": {
            "handlers": ["default"],
            "level": "INFO",
            "propagate": False,
        },  # Keep custom logger
    },
}

# Apply the logging configuration globally
logging.config.dictConfig(LOGGING_CONFIG)

# ==============================================
# Background tasks
# ==============================================

_BACKGROUND_TASKS: List[asyncio.Task] = []
_TASK_SCHEDULER_LOGGER = logging.getLogger("task_scheduler")


@asynccontextmanager
async def lifespan(app: FastAPI):
    mod: BackendModule = app.dependency_overrides.get(_module, _module)()
    for task in mod.scheduled_tasks():

        def create_task_loop(
            task: ScheduledTask,
        ) -> Callable[[], Coroutine[None, None, None]]:
            async def loop() -> None:
                while True:
                    _TASK_SCHEDULER_LOGGER.info(f"Running {task.name}...")
                    try:
                        await task.run()
                        _TASK_SCHEDULER_LOGGER.info(f"Task {task.name} DONE")
                    except Exception:
                        _TASK_SCHEDULER_LOGGER.error(
                            f"Cannot run {task.name}: {traceback.format_exc()}"
                        )
                    await asyncio.sleep(task.interval_ms / 1_000.0)

            return loop

        _BACKGROUND_TASKS.append(asyncio.create_task(create_task_loop(task)()))

    yield
    for backgroup_task in _BACKGROUND_TASKS:
        backgroup_task.cancel()


app = FastAPI(lifespan=lifespan, version=__version__)

origins = [
    "http://localhost:3000",
    "http://localhost:5173",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["Authorization", "Content-Type"],
)

app.include_router(api_router, prefix=config.API_V1_STR)


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
