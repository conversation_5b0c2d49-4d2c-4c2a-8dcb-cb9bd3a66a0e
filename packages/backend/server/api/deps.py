from contextlib import AsyncExitStack
from functools import lru_cache
import os
from fastapi import Depends, HTTPException, Security, Request
from fastapi.security import (
    HTTPAuthorizationCredentials,
    HTTPBasicCredentials,
    HTTPBearer,
    HTTPBasic,
)
from starlette.status import HTTP_403_FORBIDDEN, HTTP_401_UNAUTHORIZED
from typing import Annotated, Optional

from packages.backend.models.organization import OrganizationId
from packages.backend.server.config import config
from packages.backend.impl.module import BackendModule
from packages.backend.models.authentication import Role, User


bearer_auth = HTTPBearer(auto_error=False)
basic_auth = HTTPBasic(auto_error=False)


@lru_cache()
def _module() -> BackendModule:
    return BackendModule(
        exit_stack=AsyncExitStack(),
        config=config,
    )


HTTPServerModule = Annotated[BackendModule, Depends(_module)]

_DISABLE_AUTH = os.environ.get("DISABLE_AUTH", "true").lower() == "true"


async def authorize(
    request: Request,
    bearer: Optional[HTTPAuthorizationCredentials] = Security(bearer_auth),
    basic: Optional[HTTPBasicCredentials] = Security(basic_auth),
) -> User:
    # Authorization can either stem from a bearer token or from an API key given
    # with a basic authentication (user/password) scheme.  We reuse as much as
    # possible the code from FastAPI since it has been vetted.
    if bearer:
        user = (
            await _module()
            .authentication_service()
            .get_user_from_bearer_token(bearer.credentials)
        )
    elif basic:
        user = (
            await _module()
            .authentication_service()
            .get_principal_from_api_key(
                basic.username,  # type:ignore
                basic.password,  # type:ignore
            )
        )
    elif _DISABLE_AUTH:
        dev_user_id = request.headers.get("X-Dev-User-Id", "1")
        dev_org_id = request.headers.get("X-Dev-Org-Id", "demo-akihospit")
        dev_role = request.headers.get("X-Dev-Role", Role.SERVICE.value)

        return User(
            user_id=dev_user_id,
            organization_id=OrganizationId(dev_org_id),
            role=Role(dev_role),
        )
    else:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED, detail="Not authenticated"
        )

    if user is None:
        raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Not authenticated")

    return user


CurrentUser = Annotated[User, Depends(authorize)]


async def get_practitioner(current_user: CurrentUser) -> User:
    if current_user.role != Role.PRACTITIONER or current_user.organization_id is None:
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail="Not authorized")

    return current_user


CurrentPractitioner = Annotated[User, Depends(get_practitioner)]


async def get_service(current_user: CurrentUser) -> User:
    if current_user.role != Role.SERVICE:
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail="Not authorized")

    return current_user


CurrentService = Annotated[User, Depends(get_service)]


async def get_practitioner_or_service(
    current_user: CurrentUser,
) -> User:
    try:
        return await get_practitioner(current_user)
    except HTTPException:
        return await get_service(current_user)


CurrentPractitionerOrService = Annotated[User, Depends(get_practitioner_or_service)]


async def get_patient(current_user: CurrentUser) -> User:
    if current_user.role != Role.PATIENT:
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail="Not authorized")

    return current_user


CurrentPatient = Annotated[User, Depends(get_patient)]


async def get_relative(current_user: CurrentUser) -> User:
    if current_user.role != Role.RELATIVE:
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail="Not authorized")

    return current_user


CurrentRelative = Annotated[User, Depends(get_relative)]


async def get_patient_or_relative(current_user: CurrentUser) -> User:
    try:
        return await get_patient(current_user)
    except HTTPException:
        return await get_relative(current_user)


CurrentPatientOrRelative = Annotated[User, Depends(get_patient_or_relative)]


async def get_patient_or_relative_or_practitioner(current_user: CurrentUser) -> User:
    try:
        return await get_patient(current_user)
    except HTTPException:
        try:
            return await get_relative(current_user)
        except HTTPException:
            return await get_practitioner(current_user)


CurrentPatientOrRelativeOrPractitioner = Annotated[
    User, Depends(get_patient_or_relative_or_practitioner)
]


async def get_patient_or_relative_or_service(current_user: CurrentUser) -> User:
    try:
        return await get_patient(current_user)
    except HTTPException:
        try:
            return await get_relative(current_user)
        except HTTPException:
            return await get_service(current_user)


CurrentPatientOrRelativeOrService = Annotated[
    User, Depends(get_patient_or_relative_or_service)
]
