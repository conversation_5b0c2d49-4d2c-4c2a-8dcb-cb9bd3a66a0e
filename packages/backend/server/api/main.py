from fastapi import APIRouter

from packages.backend.server.api.routes import (
    login,
    patient,
    encounter,
    service_request,
    organization,
    questionnaire,
    questionnaire_response,
    view,
    healthcare_service,
    events,
)

api_router = APIRouter()
api_router.include_router(login.router, tags=["Login"])
api_router.include_router(patient.router, prefix="/patient", tags=["Patient"])
api_router.include_router(
    encounter.router,
    prefix="/encounter",
    tags=["Encounter"],
)
api_router.include_router(
    service_request.router,
    prefix="/service_request",
    tags=["Service request"],
)
api_router.include_router(
    organization.router,
    prefix="/organization",
    tags=["Organization"],
)
api_router.include_router(
    healthcare_service.router, prefix="/healthcare_service", tags=["Healthcare service"]
)
api_router.include_router(
    healthcare_service.router_document_reference, tags=["Discharge instructions"]
)
api_router.include_router(
    healthcare_service.router, prefix="/healthcare_service", tags=["Healthcare service"]
)
api_router.include_router(
    healthcare_service.router_document_reference, tags=["Discharge instructions"]
)
api_router.include_router(
    questionnaire.router,
    prefix="/questionnaire",
    tags=["Questionnaire"],
)
api_router.include_router(
    questionnaire_response.router,
    prefix="/questionnaire-response",
    tags=["Questionnaire Response"],
)
api_router.include_router(view.router, prefix="/view", tags=["View"])
api_router.include_router(events.router, prefix="/events", tags=["Events"])
