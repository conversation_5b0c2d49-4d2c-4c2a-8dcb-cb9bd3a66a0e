from typing import List
from fastapi import APIRouter

from packages.backend.models.organization import (
    Organization,
    OrganizationId,
)
from packages.backend.server.api.deps import (
    CurrentPractitionerOrService,
    HTTPServerModule,
)


router = APIRouter()


@router.get("")
async def list_organization(
    mod: HTTPServerModule, user: CurrentPractitionerOrService
) -> List[Organization]:
    """
    List all organizations.

    Permissions:
        - PRACTITIONER
        - SERVICE
    """
    return await mod.organization_repository().list_organization()


@router.get("/{organization_id}")
async def get_organization_by_id(
    organization_id: OrganizationId,
    mod: HTTPServerModule,
    user: CurrentPractitionerOrService,
) -> Organization:
    """
    Get an organization by its ID.

    Permissions:
        - PRACTITIONER
        - SERVICE
    """
    return await mod.organization_repository().get_organization_by_id(organization_id)
