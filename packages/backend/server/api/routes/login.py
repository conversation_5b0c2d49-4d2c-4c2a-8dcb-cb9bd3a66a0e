from fastapi import APIRouter, HTTPException
from starlette.status import HTTP_401_UNAUTHORIZED

from packages.backend.models.authentication import Token
from packages.backend.models.common import ISODate
from packages.backend.models.encounter import EncounterId, ShareId
from packages.backend.server.api.deps import HTTPServerModule


router = APIRouter()


@router.post("/login/access-token")
async def login_for_access_token(
    id: EncounterId | ShareId,
    truncated_last_name: str,
    birth_date: ISODate,
    mod: HTTPServerModule,
) -> Token:
    """
    Login for access token.
    Only for Patient and Relatives.

    Permissions:
        - Public access (no authentication required)
    """
    user = await mod.authentication_service().authenticate_user(
        id=id,
        truncated_last_name=truncated_last_name,
        birth_date=birth_date,
    )
    if not user:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return await mod.authentication_service().create_access_token(
        encounter_id=EncounterId(user.user_id), role=user.role
    )
