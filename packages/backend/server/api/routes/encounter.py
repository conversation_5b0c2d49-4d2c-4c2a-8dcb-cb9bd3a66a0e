from typing import List, Optional
from fastapi import APIRouter, HTTPException

from packages.backend.models.authentication import Role
from packages.backend.models.common import Identifier
from packages.backend.models.encounter import Encounter, EncounterId
from packages.backend.models.organization import OrganizationId
from packages.backend.server.api.deps import (
    CurrentPatient,
    CurrentPractitionerOrService,
    CurrentService,
    HTTPServerModule,
)
from packages.backend.spi.repositories import PutEncounterRequest, PatchEncounterRequest


router = APIRouter()


@router.post("")
async def create_encounter(
    user: CurrentService,
    put_encounter_request: PutEncounterRequest,
    mod: HTTPServerModule,
) -> Encounter:
    """
    Create a new encounter.
    You need to create the patient first.

    Permissions:
        - SERVICE only
    """
    return await mod.encounter_service().create_encounter(put_encounter_request)


@router.get("")
async def get_encounters(
    mod: HTTPServerModule,
    user: CurrentPractitionerOrService,
    identifier: Optional[str] = None,
    organization_id: Optional[OrganizationId] = None,
) -> List[Encounter]:
    """
    Obtain a list of encounters.
    - For practitioners: returns encounters for their organization
    - For services: returns all encounters, optionally filtered by organization_id
    An encounter Identifier can be provided in the format `system|value` to obtain a specific encounter.

    Permissions:
        - PRACTITIONER (limited to their organization)
        - SERVICE
    """
    if identifier:
        try:
            identifier_system = identifier.split("|")[0]
            identifier_value = identifier.split("|")[1]
        except IndexError:
            raise HTTPException(status_code=400, detail="Invalid identifier")

        return [
            await mod.encounter_service().get_encounter_by_identifier(
                Identifier(system=identifier_system, value=identifier_value)
            )
        ]

    # If the call is made by a practitioner, we ignore the potential organization_id given
    if user.role == Role.PRACTITIONER:
        return await mod.encounter_repository().list_encounters(
            organization_id=user.organization_id
        )

    if user.role == Role.SERVICE:
        return await mod.encounter_repository().list_encounters(
            organization_id=organization_id
        )

    raise HTTPException(status_code=500, detail="Shouldn't happen")


@router.patch("")
async def update_encounter_by_identifier(
    user: CurrentPractitionerOrService,
    encounter: PatchEncounterRequest,
    mod: HTTPServerModule,
    identifier: str,
) -> Encounter:
    """
    Update an encounter by its identifier.
    The identifier must be provided in the format `system|value`.
    As a PATCH operation, only updated fields must be provided.

    Permissions:
        - PRACTITIONER
        - SERVICE
    """
    try:
        identifier_system = identifier.split("|")[0]
        identifier_value = identifier.split("|")[1]
    except IndexError:
        raise HTTPException(status_code=400, detail="Invalid identifier")

    found_encounter = await mod.encounter_service().get_encounter_by_identifier(
        Identifier(system=identifier_system, value=identifier_value)
    )
    return await mod.encounter_service().update_encounter(found_encounter, encounter)


@router.patch("/{encounter_id}")
async def update_encounter(
    user: CurrentPractitionerOrService,
    encounter_id: EncounterId,
    encounter: PatchEncounterRequest,
    mod: HTTPServerModule,
) -> Encounter:
    """
    Update an encounter by its ID.
    As a PATCH operation, only updated fields must be provided.

    Permissions:
        - PRACTITIONER
        - SERVICE
    """
    return await mod.encounter_service().update_encounter(encounter_id, encounter)


@router.get("/{encounter_id}")
async def get_encounter_by_id(
    user: CurrentPractitionerOrService,
    encounter_id: EncounterId,
    mod: HTTPServerModule,
) -> Encounter:
    """
    Get an encounter by its ID.

    Permissions:
        - PRACTITIONER
        - SERVICE
    """
    return await mod.encounter_repository().get_encounter_by_id(id=encounter_id)


@router.post("/{encounter_id}/$reset-share")
async def reset_share_id(
    encounter_id: EncounterId, user: CurrentPatient, mod: HTTPServerModule
) -> Encounter:
    """
    Reset the share_id of an encounter.
    This invalidates existing relative sessions and generates a new share_id.

    Permissions:
        - PATIENT only
    """
    return await mod.encounter_service().reset_share_id(encounter_id)
