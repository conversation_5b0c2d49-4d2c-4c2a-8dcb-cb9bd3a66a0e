from typing import List
from fastapi import APIRouter

from packages.backend.models.questionnaire import Questionnaire
from packages.backend.server.api.deps import (
    CurrentPractitioner,
    HTTPServerModule,
)


router = APIRouter()


@router.get("")
async def list_questionnaire(
    mod: HTTPServerModule, user: CurrentPractitioner
) -> List[Questionnaire]:
    """
    List all questionnaires.

    Permissions:
        - PRACTITIONER only
    """
    return await mod.questionnaire_repository().list_questionnaires()
