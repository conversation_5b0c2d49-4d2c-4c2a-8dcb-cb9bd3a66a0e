from typing import List, Optional
from fastapi import APIRouter, HTTPException

from packages.backend.models.common import Identifier
from packages.backend.models.encounter import EncounterId
from packages.backend.models.service_request import ServiceRequest
from packages.backend.server.api.deps import (
    CurrentPractitionerOrService,
    CurrentService,
    HTTPServerModule,
)
from packages.backend.spi.repositories import PutServiceRequestRequest


router = APIRouter()


@router.post("")
async def put_service_request(
    mod: HTTPServerModule,
    user: CurrentService,
    put_service_request_request: PutServiceRequestRequest,
    identifier: Optional[str] = None,
) -> ServiceRequest:
    """
    Idempotent operation to create or update a new service request.
    Note that if a service request which is cancelled or completed can't be updated
    and a new service request should be created instead.

    Provide either an encounter ID or an identifier to match an existing encounter.
    If an identifier is provided, the system and value must be provided in the format `system|value`.
    If both are provided, the identifier will be ignored.

    Permissions:
        - SERVICE only
    """
    if identifier and not put_service_request_request.encounter_id:
        try:
            identifier_system = identifier.split("|")[0]
            identifier_value = identifier.split("|")[1]
        except IndexError:
            raise HTTPException(status_code=400, detail="Invalid identifier")

        found_encounter = await mod.encounter_service().get_encounter_by_identifier(
            Identifier(system=identifier_system, value=identifier_value)
        )

        put_service_request_request.encounter_id = found_encounter.encounter_id

    if put_service_request_request.encounter_id is None:
        raise HTTPException(
            status_code=400,
            detail="Encounter ID or matching Identifier must be provided when creating a service request",
        )

    service_request = await mod.service_request_repository().put_service_request(
        put_service_request_request
    )

    await mod.notification_service().service_request_notification(service_request)

    return service_request


@router.get("")
async def list_service_request(
    user: CurrentPractitionerOrService,
    mod: HTTPServerModule,
    encounter_id: Optional[EncounterId] = None,
    identifier: Optional[str] = None,
    latest: bool = True,
) -> List[ServiceRequest]:
    """
    Get all service requests for a given encounter.

    Permissions:
        - PRACTITIONER
        - SERVICE
    """
    if identifier and not encounter_id:
        try:
            identifier_system = identifier.split("|")[0]
            identifier_value = identifier.split("|")[1]
        except IndexError:
            raise HTTPException(status_code=400, detail="Invalid identifier")

        found_encounter = await mod.encounter_service().get_encounter_by_identifier(
            Identifier(system=identifier_system, value=identifier_value)
        )

        encounter_id = found_encounter.encounter_id

    if encounter_id is None:
        raise HTTPException(
            status_code=400,
            detail="Encounter ID or matching Identifier must be provided when listing service requests",
        )
    return await mod.service_request_repository().list_service_requests(
        encounter_id, latest=latest
    )
