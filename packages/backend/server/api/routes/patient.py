from fastapi import APIRouter
from typing import List, Optional
from packages.backend.models.organization import OrganizationId

from packages.backend.models.patient import Patient, PatientId
from packages.backend.server.api.deps import CurrentService, HTTPServerModule, CurrentPatientOrRelativeOrService
from packages.backend.spi.repositories import PutPatientRequest


router = APIRouter()


@router.post("")
async def put_patient(
    put_patient_request: PutPatientRequest, mod: HTTPServerModule, user: CurrentService
) -> Patient:
    """
    Create a new patient.
    Required to create an encounter.

    Permissions:
        - SERVICE only
    """
    return await mod.patient_repository().create_patient(put_patient_request)


@router.get("/{patient_id}")
async def get_patient_by_id(
    patient_id: PatientId, user: CurrentPatientOrRelativeOrService, mod: HTTPServerModule
) -> Patient:
    """
    Get a patient by its ID.

    Permissions:
        - SERVICE
        - PATIENT
        - RELATIVE

    TODO: secure this endpoint
    """
    return await mod.patient_repository().get_patient_by_id(patient_id)


@router.get("")
async def list_patients(
    mod: HTTPServerModule,
    user: CurrentService,
    organization_id: Optional[OrganizationId] = None,
) -> List[Patient]:
    """
    Get all patients, optionally filtered by organization.
    If organization_id is provided, returns only patients that have encounters in that organization.

    Permissions:
        - SERVICE only
    """
    return await mod.patient_repository().list_patients(organization_id)
