from fastapi import APIRouter

from packages.backend.models.authentication import Role
from packages.backend.models.view import PatientView, PractitionerView
from packages.backend.server.api.deps import (
    CurrentPatientOrRelativeOrPractitioner,
    HTTPServerModule,
)


router = APIRouter()


@router.get("")
async def get_user_view(
    user: CurrentPatientOrRelativeOrPractitioner, mod: HTTPServerModule
) -> PatientView | PractitionerView:
    """
    Get the patient view for the current user.
    This view contains all the information required to refresh the frontend.

    Permissions:
        - PATIENT
        - RELATIVE
        - PRACTITIONER
    """
    if user.role == Role.PRACTITIONER:
        return await mod.view_service().get_practitioner_view(user)
    return await mod.view_service().get_patient_view(user)
