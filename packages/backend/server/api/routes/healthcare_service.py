from typing import List
from fastapi import APIRouter, HTTPException
from fastapi.responses import FileResponse
import os

from packages.backend.models.organization import (
    OrganizationId,
    HealthcareServiceId,
    HealthcareService,
)
from packages.backend.server.api.deps import HTTPServerModule
from packages.backend.models.document_reference import DocumentReference


router = APIRouter()
router_document_reference = APIRouter()


@router.get("/{organization_id}")
async def get_healthcare_service(
    organization_id: OrganizationId, mod: HTTPServerModule
) -> List[HealthcareService]:
    """
    List all healthcare services.

    Permissions:
        - Public access (no authentication required)
    """
    return await mod.organization_repository().get_healthcare_service(organization_id)


@router_document_reference.get("/document_reference")
async def get_discharge_instructions(
    healthcare_service_id: HealthcareServiceId, mod: HTTPServerModule
) -> List[DocumentReference]:
    """
    Get discharge instructions for a given healthcare service.

    Permissions:
        - Public access (no authentication required)
    """
    return await mod.organization_repository().get_discharge_instructions(
        healthcare_service_id
    )


@router_document_reference.get("/document_reference/{instruction_file}")
async def get_discharge_instruction_file(instruction_file: str):
    """
    Get a discharge instruction file.

    Permissions:
        - Public access (no authentication required)
    """
    file_path = f"assets/{instruction_file}"
    if os.path.exists(file_path):
        return FileResponse(
            file_path, media_type="application/pdf", filename=f"{instruction_file}.pdf"
        )
    else:
        raise HTTPException(
            status_code=404, detail="Discharge instruction file not found"
        )
