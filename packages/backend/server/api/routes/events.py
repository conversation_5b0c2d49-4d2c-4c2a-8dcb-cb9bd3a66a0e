from datetime import date
from typing import Annotated, List, Optional

from fastapi import APIRouter, Query, HTTPException, status

from packages.backend.models.event import Event, EventType, UnauthLogPayload
from packages.backend.models.encounter import Encounter, EncounterId, ShareId
from packages.backend.models.patient import PatientId
from packages.backend.models.authentication import (
    Role,
)
from packages.backend.server.api.deps import (
    HTTPServerModule,
    CurrentService,
    CurrentPatientOrRelative,
)

router = APIRouter()


@router.get(
    "/",
    response_model=List[Event],
)
async def search_events(
    mod: HTTPServerModule,
    user: CurrentService,
    start_date: Annotated[
        Optional[date],
        Query(description="Start date for filtering events (inclusive)."),
    ] = None,
    end_date: Annotated[
        Optional[date], Query(description="End date for filtering events (inclusive).")
    ] = None,
    event_type: Annotated[
        Optional[EventType], Query(description="Filter by event type.")
    ] = None,
    patient_id: Annotated[
        Optional[PatientId], Query(description="Filter by patient ID.")
    ] = None,
    encounter_id: Annotated[
        Optional[EncounterId], Query(description="Filter by encounter ID.")
    ] = None,
) -> List[Event]:
    """
    Search for events based on specified criteria, including an optional date range.

    Requires SERVICE role.
    """
    events = await mod.event_repository().list_events(
        start_date=start_date,
        end_date=end_date,
        event_type=event_type,
        patient_id=patient_id,
        encounter_id=encounter_id,
    )
    return events


async def _resolve_encounter(
    raw_id: str, mod: HTTPServerModule
) -> tuple[EncounterId, Optional[PatientId]]:
    typed_id = ShareId(raw_id) if raw_id.startswith("SH-") else EncounterId(raw_id)
    try:
        encounter = await mod.encounter_repository().get_encounter_by_id(typed_id)
        return encounter.encounter_id, encounter.subject
    except HTTPException as e:
        if e.status_code != 404:
            pass
    except Exception:
        pass
    return EncounterId(raw_id), None


@router.post("/log/login_page_viewed", status_code=status.HTTP_204_NO_CONTENT)
async def log_login_page_viewed_event(
    payload: UnauthLogPayload,  # Unauthenticated, gets ID from body
    mod: HTTPServerModule,
) -> None:
    """Logs LOGIN_PAGE_VIEWED event. Requires encounter_id in body. Role inferred from ID."""
    actual_id, patient_id = await _resolve_encounter(payload.encounter_id, mod)
    role = Role.RELATIVE if payload.encounter_id.startswith("SH-") else Role.PATIENT

    await mod.event_repository().log_login_page_viewed(
        encounter_id=actual_id,
        patient_id=patient_id,
        role=role,
    )


@router.post("/log/accepted_tos", status_code=status.HTTP_204_NO_CONTENT)
async def log_accepted_tos_event(
    payload: UnauthLogPayload,
    mod: HTTPServerModule,
) -> None:
    """Logs ACCEPTED_TOS event. Requires encounter_id in body. Role inferred from ID."""
    actual_id, patient_id = await _resolve_encounter(payload.encounter_id, mod)
    role = Role.RELATIVE if payload.encounter_id.startswith("SH-") else Role.PATIENT

    await mod.event_repository().log_accepted_tos(
        encounter_id=actual_id,
        patient_id=patient_id,
        role=role,
    )


@router.post("/log/sent_share_link", status_code=status.HTTP_204_NO_CONTENT)
async def log_sent_share_link_event(
    user: CurrentPatientOrRelative,
    mod: HTTPServerModule,
) -> None:
    """Logs SENT_SHARE_LINK event initiated by an authenticated Patient or Relative."""
    encounter_id = EncounterId(user.user_id)
    encounter: Encounter = await mod.encounter_repository().get_encounter_by_id(
        encounter_id
    )
    patient_id = encounter.subject
    await mod.event_repository().log_sent_share_link(
        encounter_id=encounter_id, patient_id=patient_id, role=user.role
    )
