from typing import List
from fastapi import APIRouter

from packages.backend.models.patient import PatientId
from packages.backend.models.questionnaire import QuestionnaireId, QuestionnaireResponse
from packages.backend.server.api.deps import (
    CurrentPatient,
    CurrentPractitioner,
    HTTPServerModule,
)
from packages.backend.spi.repositories import PutQuestionnaireResponseRequest


router = APIRouter()


@router.post("")
async def add_questionnaire_response(
    user: CurrentPatient,
    put_questionnaire_response_request: PutQuestionnaireResponseRequest,
    mod: HTTPServerModule,
) -> None:
    """
    Add a questionnaire response.

    Permissions:
        - PATIENT only
    """
    await mod.questionnaire_response_repository().put_question_response(
        put_questionnaire_response_request
    )


@router.get("")
async def get_questionnaire_response(
    questionnaire_id: QuestionnaireId,
    patient_id: PatientId,
    mod: HTTPServerModule,
    practitioner: CurrentPractitioner,
) -> List[QuestionnaireResponse]:
    """
    Get questionnaire responses for a given patient and questionnaire.

    Permissions:
        - PRACTITIONER only
    """
    return await mod.questionnaire_response_repository().get_question_responses(
        patient_id=patient_id, questionnaire_id=questionnaire_id
    )
