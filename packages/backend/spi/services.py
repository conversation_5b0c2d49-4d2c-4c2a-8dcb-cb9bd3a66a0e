from abc import ABC, abstractmethod
from typing import Any, Optional

from packages.backend.models.authentication import Role, Token, User
from packages.backend.models.common import ISODate, Identifier
from packages.backend.models.encounter import Encounter, EncounterId, ShareId
from packages.backend.models.view import PatientView, PractitionerView
from packages.backend.spi.repositories import PatchEncounterRequest, PutEncounterRequest


class ViewService(ABC):
    """
    Backend for frontend.
    View service provides an aggregated view of the encounter's data.
    """

    @abstractmethod
    async def get_patient_view(self, user: User) -> PatientView:
        """
        Get an encounter by its ID or a share ID. If a share ID is
        provided, some service requests are filtered out.
        """
        ...

    @abstractmethod
    async def get_practitioner_view(self, user: User) -> PractitionerView:
        """
        Get a practitioner view containing all encounters, patients and healthcare services.
        """
        ...


class EncounterService(ABC):
    """
    A service to manage encounters.
    """

    @abstractmethod
    async def create_encounter(
        self, put_encounter_request: PutEncounterRequest
    ) -> Encounter:
        """
        Check if subject and organization exists before creating the encounter.
        """
        ...

    @abstractmethod
    async def update_encounter(
        self, encounter: Encounter | EncounterId, patch_request: PatchEncounterRequest
    ) -> Encounter:
        """
        Update an encounter:
        - Add an end date if discharge information has been added
        """
        ...

    @abstractmethod
    async def get_encounter_by_identifier(self, identifier: Identifier) -> Encounter:
        """
        Returns the encounter matching the identifier requested by the upstream system.
        """
        ...

    @abstractmethod
    async def reset_share_id(self, encounter_id: EncounterId) -> Encounter:
        """
        Reset the share_id of an encounter. Only the patient can do this.
        This invalidates existing relative sessions and generates a new share_id.
        """
        ...

    @abstractmethod
    async def check_and_send_delayed_sms(self) -> None:
        """
        Checks for encounters that require a delayed post-discharge SMS
        and sends it if the conditions are met.
        """
        ...


class AuthenticationService(ABC):
    """
    A service to manage authentication.
    """

    @abstractmethod
    async def get_user_from_bearer_token(self, token: str) -> Optional[User]:
        """
        Get the user from a bearer token.
        """
        ...

    @abstractmethod
    async def get_principal_from_api_key(
        self, email: str, api_key: str
    ) -> Optional[User]:
        """
        Authorizes a request from an API key.

        Returns:
            The user if authentication succeeds, `None` otherwise.
        """
        ...

    @abstractmethod
    async def authenticate_user(
        self,
        id: EncounterId | ShareId,
        truncated_last_name: str,
        birth_date: ISODate,
    ) -> User:
        """
        Authenticate the user from the encounter ID, the truncated last name and the birth date.
        """
        ...

    @abstractmethod
    async def create_access_token(self, encounter_id: EncounterId, role: Role) -> Token:
        """
        Create a token for a user. A user is a patient or a close relative.
        """
        ...

    @abstractmethod
    async def authenticate_practitioner(
        self, payload: dict[str, Any]
    ) -> Optional[User]:
        """
        Authenticate a practitioner with a token and return the practitioner ID.
        """
        ...


class ArchivalService(ABC):
    """
    A service to manage archival of old data
    """

    @abstractmethod
    async def archive_old_data(self, days_to_keep: int) -> None:
        """
        Archive all data older than the given number of days.
        This also anonymizes the data.
        """
        ...
