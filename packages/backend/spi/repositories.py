from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional, Literal
from datetime import date

from packages.backend.models.activity_definition import ActivityDefinition
from packages.backend.models.common import (
    ISODate,
    Identifier,
    Period,
    Timing,
    UnixTimestamp,
)
from packages.backend.models.document_reference import DocumentReference
from packages.backend.models.encounter import Discharge, Encounter, EncounterId, ShareId
from packages.backend.models.organization import (
    HealthcareService,
    HealthcareServiceId,
    Organization,
    OrganizationId,
)
from packages.backend.models.patient import HumanName, Patient, PatientId, Contact
from packages.backend.models.questionnaire import (
    QuestionResponseItem,
    Questionnaire,
    QuestionnaireId,
    QuestionnaireResponse,
)
from packages.backend.models.service_request import (
    ActivityDefinitionId,
    ServiceRequest,
    ServiceRequestId,
    ServiceRequestStatus,
)
from packages.backend.models.event import Event, EventType
from packages.backend.models.event import TextType
from packages.backend.models.authentication import Role


@dataclass
class PutServiceRequestRequest:
    activity_definition_id: ActivityDefinitionId
    encounter_timing: Timing
    status: ServiceRequestStatus
    # Optional for when updated with an identifier
    encounter_id: Optional[EncounterId] = None


class ServiceRequestRepository(ABC):
    """
    Service request repository.
    """

    @abstractmethod
    async def put_service_request(
        self, put_service_request_request: PutServiceRequestRequest
    ) -> ServiceRequest:
        """
        Idempotent operation to create or update a new service request.
        Note that if a service request which is cancelled or completed can't be updated
        and a new service request should be created instead.
        """
        ...

    @abstractmethod
    async def list_service_requests(
        self, encounter_id: EncounterId, latest: bool = True
    ) -> List[ServiceRequest]:
        """
        Get all service requests for a given encounter.
        """
        ...

    @abstractmethod
    async def archive_service_request(
        self, encounter_id: EncounterId, service_request_id: ServiceRequestId
    ) -> None:
        """
        Archive a service request.
        """
        ...


@dataclass
class PutEncounterRequest:
    subject: PatientId
    service_provider: OrganizationId
    service_type: HealthcareServiceId
    identifier: list[Identifier]
    start: Optional[UnixTimestamp] = None
    planned_length: Optional[int] = None

    def __post_init__(self):
        assert len(self.identifier) > 0, "At least one identifier must be provided"


@dataclass
class PatchEncounterRequest:
    actual_period: Optional[Period] = None
    planned_length: Optional[int] = None
    subject_consent_date: Optional[UnixTimestamp] = None
    service_type: Optional[HealthcareServiceId] = None
    discharge: Optional[Discharge] = None


class EncounterRepository(ABC):
    """
    Encounter repository.
    """

    @abstractmethod
    async def create_encounter(
        self, put_encounter_request: PutEncounterRequest
    ) -> Encounter:
        """
        Create a new encounter in the database.
        """
        ...

    @abstractmethod
    async def save_encounter(self, encounter: Encounter) -> None:
        """
        Save the record without any modification.
        """
        ...

    @abstractmethod
    async def get_encounter_by_id(self, id: EncounterId | ShareId) -> Encounter:
        """
        Get an encounter by its ID.
        At least one of the two parameters must be provided.
        """
        ...

    @abstractmethod
    async def list_encounters(
        self,
        organization_id: Optional[OrganizationId] = None,
        healthcare_service_id: Optional[HealthcareServiceId] = None,
    ) -> list[Encounter]:
        """
        Get all encounters.
        """
        ...

    @abstractmethod
    async def archive_encounter(self, id: EncounterId) -> None:
        """
        Archive an encounter.
        """
        ...

    @abstractmethod
    async def generate_share_id(self, encounter_id: EncounterId) -> ShareId:
        """
        Generate a new share ID for an encounter.
        """
        ...


class OrganizationRepository(ABC):
    """
    Organization service.
    """

    @abstractmethod
    async def get_organization_by_id(
        self, organization_id: OrganizationId
    ) -> Organization:
        """
        Get an organization by its ID.
        """
        ...

    @abstractmethod
    async def list_organization(self) -> List[Organization]:
        """
        List all organizations.
        """
        ...

    @abstractmethod
    async def get_healthcare_service(
        self, organization_id: OrganizationId
    ) -> List[HealthcareService]:
        """
        Get all healthcare services for a given organization.
        """
        ...

    @abstractmethod
    async def get_activity_definition(
        self, orgnization_id: OrganizationId, healthcare_service_id: HealthcareServiceId
    ) -> List[ActivityDefinition]:
        """
        Get all activity definitions for a given healthcare service in an organization.
        """
        ...

    @abstractmethod
    async def get_discharge_instructions(
        self, healthcare_service_id: HealthcareServiceId
    ) -> List[DocumentReference]:
        """
        Get discharge instructions for a given healthcare service.
        """
        ...

    @abstractmethod
    async def get_activity_definition_by_id(
        self, activity_definition_id: ActivityDefinitionId
    ) -> ActivityDefinition:
        """
        Find an activity definition by its ID.
        """
        ...


class QuestionnaireRepository(ABC):
    @abstractmethod
    async def list_questionnaires(self) -> List[Questionnaire]:
        """
        List all questionnaires available.
        """
        ...

    @abstractmethod
    async def get_questionnaire_by_id(
        self, questionnaire_id: QuestionnaireId
    ) -> Questionnaire:
        """
        Get a questionnaire by its ID.
        """
        ...


@dataclass
class PutQuestionnaireResponseRequest:
    questionnaire_id: QuestionnaireId
    subject: PatientId
    item: List[QuestionResponseItem]


class QuestionnaireResponseRepository(ABC):
    @abstractmethod
    async def put_question_response(
        self, put_question_response_request: PutQuestionnaireResponseRequest
    ) -> QuestionnaireResponse:
        """
        Put a question response.
        """
        ...

    @abstractmethod
    async def get_question_responses(
        self, patient_id: PatientId, questionnaire_id: QuestionnaireId
    ) -> List[QuestionnaireResponse]:
        """
        Get all question responses for a given questionnaire.
        """
        ...

    @abstractmethod
    async def archive_question_responses(self, patient_id: PatientId) -> None:
        """
        Archive all question responses for a given patient.
        """
        ...


@dataclass
class PutPatientRequest:
    name: HumanName
    birth_date: ISODate
    phone: Optional[str] = None
    contact: Optional[List[Contact]] = None


class PatientRepository(ABC):
    @abstractmethod
    async def create_patient(self, put_patient_request: PutPatientRequest) -> Patient:
        """
        Create a new patient in the database.
        """
        ...

    @abstractmethod
    async def get_patient_by_id(self, patient_id: PatientId) -> Patient:
        """
        Get patient by ID.
        """
        ...

    # @abstractmethod
    # async def anonymize_patient(self, patient_id: PatientId) -> Patient:
    #     """
    #     Anonymize a patient.
    #     """
    #     ...

    # @abstractmethod
    # async def archive_patient(self, patient_id: PatientId) -> None:
    #     """
    #     Archive a patient.
    #     """
    #     ...

    @abstractmethod
    async def list_patients(
        self, organization_id: Optional[OrganizationId] = None
    ) -> List[Patient]:
        """
        Get all patients, optionally filtered by organization.
        """
        ...


class EventRepository(ABC):
    @abstractmethod
    async def put_event(self, event: Event) -> Event:
        """Stores an event by appending it to the appropriate daily file."""
        pass

    @abstractmethod
    async def list_events(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        encounter_id: Optional[EncounterId] = None,
        patient_id: Optional[PatientId] = None,
        event_type: Optional[EventType] = None,
    ) -> List[Event]:
        """List events, optionally filtering by date range and other criteria."""
        ...

    @abstractmethod
    async def log_encounter_created(
        self, encounter_id: EncounterId, patient_id: PatientId
    ) -> None:
        """Logs an ENCOUNTER_CREATED event."""
        ...

    @abstractmethod
    async def log_text_message_sent(
        self,
        encounter_id: EncounterId,
        patient_id: PatientId,
        role: Role,
        recipient: str,
        text: str,
        text_type: TextType,
    ) -> None:
        """Logs a TEXT_MESSAGE_SENT event."""
        ...

    @abstractmethod
    async def log_connexion_attempt(
        self,
        encounter_id: EncounterId,
        patient_id: PatientId,
        role: Role,
        success: bool,
        failure_reason: Optional[str] = None,
    ) -> None:
        """Logs a CONNEXION_ATTEMPT event."""
        ...

    @abstractmethod
    async def log_login_page_viewed(
        self, encounter_id: EncounterId, patient_id: Optional[PatientId], role: Role
    ) -> None:
        """Logs a LOGIN_PAGE_VIEWED event. Role is inferred from ID format."""
        ...

    @abstractmethod
    async def log_sent_share_link(
        self, encounter_id: EncounterId, patient_id: PatientId, role: Role
    ) -> None:
        """Logs a SENT_SHARE_LINK event. Requires role and patient_id."""
        ...

    @abstractmethod
    async def log_accepted_tos(
        self, encounter_id: EncounterId, patient_id: Optional[PatientId], role: Role
    ) -> None:
        """Logs an ACCEPTED_TOS event. Role is inferred from ID format."""
        ...
