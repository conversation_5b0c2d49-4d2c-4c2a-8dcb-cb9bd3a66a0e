import os
import requests
from xml.etree.ElementTree import Element, SubElement, tostring
import logging

API_URL = "https://sms.capitolemobile.com/api/sendsms/xml"


class CapitoleMobile:
    def send_sms(self, phone: str, content: str) -> None:
        username = os.environ.get("CAPITOLE_MOBILE_USERNAME")
        password = os.environ.get("CAPITOLE_MOBILE_PASSWORD")

        if not username or not password:
            logging.info(f"Would have sent SMS to {phone} with content: {content}")
            return

        sms = Element("SMS")

        auth = SubElement(sms, "authentification")
        SubElement(auth, "username").text = username
        SubElement(auth, "password").text = password

        message = SubElement(sms, "message")
        SubElement(message, "text").text = content
        SubElement(message, "sender").text = "MonParcours"
        SubElement(message, "id").text = "MonParcours"
        SubElement(message, "long").text = "yes"

        recipients = SubElement(sms, "recipients")
        SubElement(recipients, "gsm").text = phone

        xml_data = tostring(sms, encoding="unicode")

        response = None
        try:
            response = requests.post(API_URL, data={"XML": xml_data})
            response.raise_for_status()
            logging.info(response.text)
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to send SMS via Capitole Mobile: {e}")
            if response:
                logging.error(f"Response text: {response.text}")
