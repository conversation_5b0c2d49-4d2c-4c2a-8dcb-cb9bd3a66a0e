from dataclasses import asdict
from pathlib import Path
from typing import List
import uuid

import aiofiles.os
from dacite import from_dict
import orjson
from overrides import override
from packages.backend.impl.utils.time_u import timestamp_now
from packages.backend.models.encounter import EncounterId
from packages.backend.models.service_request import ServiceRequest, ServiceRequestId
from packages.backend.spi.repositories import (
    PutServiceRequestRequest,
    ServiceRequestRepository,
)


class ServiceRequestRepositoryImpl(ServiceRequestRepository):
    def __init__(self, collection_path: Path):
        self._collection_path = collection_path

    @override
    async def put_service_request(
        self, put_service_request_request: PutServiceRequestRequest
    ) -> ServiceRequest:
        assert put_service_request_request.encounter_id is not None
        service_request_id = ServiceRequestId(uuid.uuid4().hex)
        service_request = ServiceRequest(
            service_request_id=service_request_id,
            activity_definition_id=put_service_request_request.activity_definition_id,
            authored_on=timestamp_now(),
            encounter=put_service_request_request.encounter_id,
            encounter_timing=put_service_request_request.encounter_timing,
            status=put_service_request_request.status,
        )
        path = (
            self._collection_path
            / f"{put_service_request_request.encounter_id}-{service_request_id}.json"
        )
        data = orjson.dumps(asdict(service_request))
        async with aiofiles.open(path, "wb") as f:
            await f.write(data)

        return service_request

    @override
    async def list_service_requests(
        self, encounter_id: EncounterId, latest: bool = True
    ) -> List[ServiceRequest]:
        path = self._collection_path
        files = path.glob(f"{encounter_id}-*.json")
        responses: List[ServiceRequest] = []
        for file in files:
            async with aiofiles.open(file, "rb") as f:
                data = await f.read()
            data_as_dict = orjson.loads(data)
            responses.append(from_dict(ServiceRequest, data_as_dict))

        # Keep the latest service request by activity definition
        if latest:
            if not responses:  # Handle empty list case
                return []

            # Sort by creation time, newest first
            responses = sorted(responses, key=lambda x: x.authored_on, reverse=True)

            # Build a new list containing only the latest for each activity
            latest_responses = []
            seen_activities = set()
            for response in responses:
                if response.activity_definition_id not in seen_activities:
                    latest_responses.append(response)
                    seen_activities.add(response.activity_definition_id)
            return latest_responses  # Return the new filtered list
        else:
            # If not filtering for latest, sort chronologically (oldest first)
            responses = sorted(responses, key=lambda x: x.authored_on)
            return responses

    @override
    async def archive_service_request(
        self, encounter_id: EncounterId, service_request_id: ServiceRequestId
    ) -> None:
        path = self._collection_path / f"{encounter_id}-{service_request_id}.json"
        archived_path = self._collection_path / "archived"
        archived_path.mkdir(exist_ok=True)
        archived_path_file = archived_path / f"{encounter_id}-{service_request_id}.json"

        await aiofiles.os.rename(path, archived_path_file)
