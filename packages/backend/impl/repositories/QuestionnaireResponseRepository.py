from dataclasses import asdict
from pathlib import Path
from typing import List

import asyncio
import aiofiles.os
from dacite import from_dict
import orjson
from overrides import override

from packages.backend.impl.utils.time_u import timestamp_now
from packages.backend.models.patient import PatientId
from packages.backend.models.questionnaire import QuestionnaireId, QuestionnaireResponse
from packages.backend.spi.repositories import (
    PutQuestionnaireResponseRequest,
    QuestionnaireResponseRepository,
)


class QuestionnaireResponseRepositoryImpl(QuestionnaireResponseRepository):
    def __init__(self, collection_path: Path) -> None:
        self._collection_path = collection_path

    @override
    async def put_question_response(
        self, put_question_response_request: PutQuestionnaireResponseRequest
    ) -> QuestionnaireResponse:
        questionnaire_response = QuestionnaireResponse(
            questionnaire_id=put_question_response_request.questionnaire_id,
            authored=timestamp_now(),
            subject=put_question_response_request.subject,
            item=put_question_response_request.item,
        )

        path = (
            self._collection_path
            / f"{questionnaire_response.subject}-{questionnaire_response.questionnaire_id}-{questionnaire_response.authored}.json"  # noqa
        )
        data = orjson.dumps(asdict(questionnaire_response))
        async with aiofiles.open(path, "wb") as f:
            await f.write(data)

        return questionnaire_response

    @override
    async def get_question_responses(
        self, patient_id: PatientId, questionnaire_id: QuestionnaireId
    ) -> List[QuestionnaireResponse]:
        path = self._collection_path / f"{patient_id}-{questionnaire_id}-*.json"
        files = path.glob("*.json")
        responses = []
        for file in files:
            async with aiofiles.open(file, "rb") as f:
                data = await f.read()
            data_as_dict = orjson.loads(data)
            responses.append(from_dict(QuestionnaireResponse, data_as_dict))

        return responses

    @override
    async def archive_question_responses(self, patient_id: PatientId) -> None:

        archive_path = self._collection_path / "archive"
        archive_path.mkdir(exist_ok=True)

        path = self._collection_path / f"{patient_id}-*.json"
        files = path.glob("*.json")

        await asyncio.gather(
            *[aiofiles.os.rename(file, archive_path / file.name) for file in files]
        )
