import aiofiles.os
import hashlib
from dataclasses import asdict
from pathlib import Path
from typing import Optional, cast, Dict
from dacite import from_dict
from fastapi import HTTPException
import orjson
from overrides import override
from packages.backend.impl.utils.time_u import timestamp_now
from packages.backend.models.common import Period
from packages.backend.models.encounter import Encounter, EncounterId, ShareId
from packages.backend.models.organization import HealthcareServiceId, OrganizationId
from packages.backend.spi.repositories import EncounterRepository, PutEncounterRequest
import secrets
import logging

_LOGGER = logging.getLogger(__name__)


class EncounterRepositoryImpl(EncounterRepository):
    def __init__(self, collection_path: Path):
        self._collection_path = collection_path
        self._share_id_map: Dict[ShareId, EncounterId] | None = None

    async def _ensure_share_id_map_loaded(self) -> None:
        if self._share_id_map is not None:
            return
        await self._rebuild_share_id_map_from_scratch()

    async def _rebuild_share_id_map_from_scratch(self) -> None:
        """
        Completely rebuild the share_id map by scanning all encounter files
        This is a recovery mechanism for when the map might be inconsistent
        """
        _LOGGER.info("Rebuilding share_id map from scratch...")
        self._share_id_map = {}
        # Load all encounters and build the map
        for path in self._collection_path.glob("*.json"):
            async with aiofiles.open(path, "r") as f:
                data = await f.read()
            data_as_dict = orjson.loads(data)
            try:
                encounter = from_dict(Encounter, data_as_dict)
            except Exception as e:
                _LOGGER.error(f"Error parsing encounter file {path}: {e}")
                continue  # Skip this file if it's malformed
            if encounter.share_id:
                self._share_id_map[encounter.share_id] = encounter.encounter_id

    @override
    async def save_encounter(self, encounter: Encounter) -> None:
        path = self._collection_path / f"{encounter.encounter_id}.json"
        data = orjson.dumps(asdict(encounter))
        async with aiofiles.open(path, "wb") as f:
            await f.write(data)
        # Update in-memory share_id map
        await self._ensure_share_id_map_loaded()
        if encounter.share_id:
            assert self._share_id_map is not None
            self._share_id_map[encounter.share_id] = encounter.encounter_id

    @override
    async def list_encounters(
        self,
        organization_id: Optional[OrganizationId] = None,
        healthcare_service_id: Optional[HealthcareServiceId] = None,
    ) -> list[Encounter]:
        encounters = []
        for path in self._collection_path.glob("*.json"):
            async with aiofiles.open(path, "r") as f:
                data = await f.read()
            data_as_dict = orjson.loads(data)
            try:
                encounter = from_dict(Encounter, data_as_dict)
            except Exception as e:
                _LOGGER.error(f"Error parsing encounter file {path} during list: {e}")
                continue  # Skip malformed files
            if organization_id and encounter.service_provider != organization_id:
                continue

            if (
                healthcare_service_id
                and encounter.service_type != healthcare_service_id
            ):
                continue
            encounters.append(encounter)
        return encounters

    @override
    async def create_encounter(
        self, put_encounter_request: PutEncounterRequest
    ) -> Encounter:
        encounter_id = await self._generate_unique_encounter_id(
            put_encounter_request.service_provider
        )
        share_id = await self.generate_share_id(encounter_id)

        if not put_encounter_request.start:
            put_encounter_request.start = timestamp_now()

        encounter = Encounter(
            encounter_id=encounter_id,
            share_id=share_id,
            subject=put_encounter_request.subject,
            service_provider=put_encounter_request.service_provider,
            service_type=put_encounter_request.service_type,
            actual_period=Period(start=put_encounter_request.start),
            planned_length=put_encounter_request.planned_length,
            identifier=put_encounter_request.identifier,
        )
        await self.save_encounter(encounter)
        return encounter

    @override
    async def generate_share_id(self, encounter_id: EncounterId) -> ShareId:
        await self._ensure_share_id_map_loaded()
        assert self._share_id_map is not None

        # Remove old share_id mapping if it exists
        old_share_id = next(
            (
                share_id
                for share_id, enc_id in self._share_id_map.items()
                if enc_id == encounter_id
            ),
            None,
        )
        if old_share_id:
            self._share_id_map.pop(old_share_id)

        # Generate a new share_id using a random component to ensure rotation
        random_salt = secrets.token_hex(8)
        hash_input = f"{encounter_id}-{random_salt}"
        hash_object = hashlib.sha256(hash_input.encode())
        share_id = ShareId(f"SH-{hash_object.hexdigest()[:16]}")

        # Save the new mapping in memory
        self._share_id_map[share_id] = encounter_id
        return share_id

    @override
    async def get_encounter_by_id(self, id: EncounterId | ShareId) -> Encounter:
        encounter_id = await self._id_to_encounter_id(id)
        path = self._collection_path / f"{encounter_id}.json"
        if not path.exists():
            raise HTTPException(status_code=404, detail="Encounter not found")

        async with aiofiles.open(path, "r") as f:
            data = await f.read()

        data_as_dict = orjson.loads(data)
        return from_dict(Encounter, data_as_dict)

    async def _id_to_encounter_id(self, id: EncounterId | ShareId) -> EncounterId:
        if isinstance(id, str) and id.startswith("SH-"):
            await self._ensure_share_id_map_loaded()
            assert self._share_id_map is not None
            encounter_id = self._share_id_map.get(cast(ShareId, id))

            # If the share_id is not found, try rebuilding the entire map from scratch
            if not encounter_id:
                _LOGGER.warning(
                    f"Share ID {id} not found in map. Attempting to rebuild map..."
                )
                self._share_id_map = None
                await self._ensure_share_id_map_loaded()
                assert self._share_id_map is not None

                # Check again after rebuilding
                encounter_id = self._share_id_map.get(cast(ShareId, id))

                # If still not found after rebuilding, it's truly a 404
                if not encounter_id:
                    _LOGGER.error(
                        "Share ID not found even after rebuilding map: %s", id
                    )
                    _LOGGER.debug("Current map contents: %s", self._share_id_map)
                    raise HTTPException(status_code=404, detail="Invalid share ID")
                else:
                    _LOGGER.debug(f"Share ID {id} found after rebuilding map!")

            return EncounterId(encounter_id)

        return EncounterId(id)

    @override
    async def archive_encounter(self, id: EncounterId) -> None:
        path = self._collection_path / f"{id}.json"
        archived_path = self._collection_path / "archived"
        archived_path.mkdir(exist_ok=True)

        archived_file_path = archived_path / f"{id}.json"
        await aiofiles.os.rename(path, archived_file_path)

    async def _generate_unique_encounter_id(
        self, organization_id: OrganizationId
    ) -> EncounterId:
        while True:
            random_part = secrets.token_hex(4)  # 8 hex characters
            encounter_id = EncounterId(f"{organization_id}-{random_part}")

            # Check for collision
            path = self._collection_path / f"{encounter_id}.json"
            if not path.exists():
                return encounter_id
