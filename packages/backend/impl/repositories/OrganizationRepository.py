from collections import defaultdict
from pathlib import Path
from typing import Dict, List

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from overrides import override
import yaml

from packages.backend.models.activity_definition import (
    ActivityDefinition,
    ActivityDefinitionId,
    SendNotification,
)
from packages.backend.models.organization import (
    HealthcareService,
    HealthcareServiceId,
    Organization,
    OrganizationId,
)
from packages.backend.models.questionnaire import QuestionnaireId
from packages.backend.spi.repositories import OrganizationRepository
from packages.backend.models.document_reference import (
    DocumentReference,
    Reference,
    Content,
    Attachment,
)


class OrganizationRepositoryImpl(OrganizationRepository):
    def __init__(self, collection_path: Path) -> None:
        self._collection_path = collection_path
        self._load_organization_config()

    def _load_organization_config(self) -> None:
        with open(self._collection_path, "r", encoding="utf-8") as file:
            data = yaml.safe_load(file)

        self._organizations: List[Organization] = []
        self._healthcare_service: Dict[OrganizationId, List[HealthcareService]] = (
            defaultdict(list)
        )

        # This will hold discharge instructions as a dict mapping HealthcareServiceId to a list of discharge instructions
        self._discharge_instructions: Dict[HealthcareServiceId, List[Attachment]] = (
            defaultdict(list)
        )

        for org in data["organization"]:
            # Create Organization instance
            organization = Organization(
                organization_id=org["id"],
                name=org["name"],
                description=org["description"],
                patient_session_timeout_minutes=org["patient_session_timeout_minutes"],
            )
            self._organizations.append(organization)

            # Create HealthcareService instances
            for service in org["healthcare_service"]:
                activities = [
                    ActivityDefinition(
                        activity_definition_id=activity["id"],
                        shareable=activity["shareable"],
                        name=activity["name"],
                        description=activity["description"],
                        default_wait_time=activity.get("default_wait_time", None),
                        send_notification=(
                            [
                                SendNotification(
                                    trigger=notification["trigger"],
                                    message=notification["message"],
                                )
                                for notification in activity.get(
                                    "send_notification", []
                                )
                            ]
                            if activity.get("send_notification")
                            else None
                        ),
                    )
                    for activity in service.get("activity_definition", [])
                ]

                questionnaires = [
                    QuestionnaireId(q["id"]) for q in service.get("questionnaire", [])
                ]

                healthcare_service = HealthcareService(
                    healthcare_service_id=service["id"],
                    name=service["name"],
                    description=service["description"],
                    provided_by=org["id"],
                    activity_definition=activities,
                    questionnaire=questionnaires,
                    offered_in=service.get("offered_in", None),
                )
                self._healthcare_service[org["id"]].append(healthcare_service)

                discharge_instructions = [
                    Attachment(name=instruction["name"], id=instruction["id"])
                    for instruction in service.get("discharge_instructions", [])
                ]
                self._discharge_instructions[service["id"]] = discharge_instructions

    @override
    async def list_organization(self) -> List[Organization]:
        return self._organizations

    @override
    async def get_organization_by_id(
        self, organization_id: OrganizationId
    ) -> Organization:
        organization = next(
            (
                org
                for org in self._organizations
                if org.organization_id == organization_id
            ),
            None,
        )
        if not organization:
            raise HTTPException(status_code=404, detail="Organization not found")

        return organization

    @override
    async def get_healthcare_service(
        self, organization_id: OrganizationId
    ) -> List[HealthcareService]:
        return self._healthcare_service.get(organization_id, [])

    @override
    async def get_activity_definition(
        self, orgnization_id: OrganizationId, healthcare_service_id: HealthcareServiceId
    ) -> List[ActivityDefinition]:
        healthcare_service = next(
            (
                service
                for service in self._healthcare_service.get(orgnization_id, [])
                if service.healthcare_service_id == healthcare_service_id
            ),
            None,
        )
        if not healthcare_service:
            raise HTTPException(status_code=404, detail="Healthcare service not found")

        # Start with this service's activities
        activities = healthcare_service.activity_definition.copy()

        # Recursively get activities from parent services
        current_service = healthcare_service
        while current_service.offered_in:
            parent_service = next(
                (
                    service
                    for org_services in self._healthcare_service.values()
                    for service in org_services
                    if service.healthcare_service_id == current_service.offered_in
                ),
                None,
            )
            if parent_service:
                activities.extend(parent_service.activity_definition)
                current_service = parent_service
            else:
                break

        return activities

    @override
    async def get_discharge_instructions(
        self, healthcare_service_id: HealthcareServiceId
    ) -> List[DocumentReference]:
        attachments = self._discharge_instructions.get(healthcare_service_id, [])

        instructions = [
            DocumentReference(
                id=attachment.id,
                description=attachment.name,
                author=[
                    Reference(reference=f"HealthcareService/{healthcare_service_id}")
                ],
                content=[
                    Content(
                        attachment=Attachment(id=attachment.id, name=attachment.name)
                    )
                ],
            )
            for attachment in attachments
        ]
        return instructions

    @override
    async def get_activity_definition_by_id(
        self, activity_definition_id: ActivityDefinitionId
    ) -> ActivityDefinition:
        for organization in self._organizations:
            for service in self._healthcare_service.get(
                organization.organization_id, []
            ):
                for activity_definition in service.activity_definition:
                    if (
                        activity_definition.activity_definition_id
                        == activity_definition_id
                    ):
                        return activity_definition

        raise HTTPException(status_code=404, detail="Activity definition not found")
