from pathlib import Path
from typing import Dict, List

from fastapi import HTTPException
from overrides import override
import yaml
from packages.backend.models.questionnaire import (
    AnswerOption,
    Questionnaire,
    QuestionnaireId,
    QuestionnaireItem,
)
from packages.backend.spi.repositories import QuestionnaireRepository


class QuestionnaireRepositoryImpl(QuestionnaireRepository):
    def __init__(self, collection_path: Path) -> None:
        self._collection_path = collection_path
        self._load_questionnaire()

    def _load_questionnaire(self) -> None:
        self._questionnaires: Dict[QuestionnaireId, Questionnaire] = {}
        with open(self._collection_path, "r", encoding="utf-8") as file:
            data = yaml.safe_load(file)

        for questionnaire in data["questionnaire"]:
            self._questionnaires[QuestionnaireId(questionnaire["id"])] = Questionnaire(
                questionnaire_id=QuestionnaireId(questionnaire["id"]),
                name=questionnaire["name"],
                description=questionnaire["description"],
                item=[
                    QuestionnaireItem(
                        link_id=i["link_id"],
                        text=i["text"],
                        type=i["type"],
                        answer_option=[AnswerOption(**j) for j in i["answer_option"]],
                        initial=[AnswerOption(**j) for j in i["initial"]],
                    )
                    for i in questionnaire["item"]
                ],
            )

    @override
    async def list_questionnaires(self) -> List[Questionnaire]:
        return [x for x in self._questionnaires.values()]

    @override
    async def get_questionnaire_by_id(
        self, questionnaire_id: QuestionnaireId
    ) -> Questionnaire:
        questionnaire = self._questionnaires.get(questionnaire_id)
        if questionnaire is None:
            raise HTTPException(status_code=404, detail="Questionnaire not found")

        return questionnaire
