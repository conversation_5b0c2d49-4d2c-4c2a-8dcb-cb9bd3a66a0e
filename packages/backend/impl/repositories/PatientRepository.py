from dataclasses import asdict
from pathlib import Path
import uuid
from typing import List, Optional

import aiofiles.os
from dacite import from_dict
from fastapi import HTTPException
import orjson
from overrides import override
from packages.backend.models.patient import Patient, PatientId
from packages.backend.models.organization import OrganizationId
from packages.backend.spi.repositories import PatientRepository, PutPatientRequest
from packages.backend.models.encounter import Encounter


class PatientRepositoryImpl(PatientRepository):
    def __init__(self, collection_path: Path):
        self._collection_path = collection_path

    @override
    async def create_patient(self, put_patient_request: PutPatientRequest) -> Patient:
        patient_id = PatientId(uuid.uuid4().hex)
        patient = Patient(
            patient_id=patient_id,
            name=put_patient_request.name,
            birth_date=put_patient_request.birth_date,
            phone=put_patient_request.phone,
            contact=put_patient_request.contact or [],
        )

        path = self._collection_path / f"{patient_id}.json"
        data = orjson.dumps(
            asdict(patient),
            option=orjson.OPT_NON_STR_KEYS | orjson.OPT_PASSTHROUGH_DATACLASS,
        )
        async with aiofiles.open(path, "wb") as f:
            await f.write(data)

        return patient

    @override
    async def get_patient_by_id(self, patient_id: PatientId) -> Patient:
        path = self._collection_path / f"{patient_id}.json"
        if not path.exists():
            raise HTTPException(status_code=404, detail="Patient not found")

        async with aiofiles.open(path, "r") as f:
            data = await f.read()

        data_as_dict = orjson.loads(data)
        return from_dict(Patient, data_as_dict)

    # @override
    # async def anonymize_patient(self, patient_id: PatientId) -> Patient:
    #     path = self._collection_path / f"{patient_id}.json"
    #     async with aiofiles.open(path, "r") as f:
    #         data = await f.read()

    #     data_as_dict = orjson.loads(data)
    #     patient = from_dict(Patient, data_as_dict)

    #     anonymous_patient = Patient(
    #         patient_id=patient.patient_id,
    #         name=HumanName(first_name="", last_name=""),
    #         birth_date=ISODate("0000-00-00"),
    #         phone="+**********",
    #     )

    #     data = orjson.dumps(asdict(anonymous_patient))
    #     async with aiofiles.open(path, "wb") as f:
    #         await f.write(data)

    #     return anonymous_patient

    # @override
    # async def archive_patient(self, patient_id: PatientId) -> None:
    #     path = self._collection_path / f"{patient_id}.json"
    #     archived_path = self._collection_path / "archived"
    #     archived_path.mkdir(exist_ok=True)
    #     archived_path_file = archived_path / f"{patient_id}.json"

    #     await aiofiles.os.rename(path, archived_path_file)

    @override
    async def list_patients(
        self, organization_id: Optional[OrganizationId] = None
    ) -> List[Patient]:
        # Get all JSON files in the collection path (except those in archived folder)
        patient_files = [
            f
            for f in self._collection_path.glob("*.json")
            if f.is_file() and not str(f).endswith("/archived")
        ]

        patients = []
        for path in patient_files:
            async with aiofiles.open(path, "r") as f:
                data = await f.read()
            data_as_dict = orjson.loads(data)
            patient = from_dict(Patient, data_as_dict)

            # If no organization filter, include all patients
            if organization_id is None:
                patients.append(patient)
                continue

            # Check if patient has encounters in the specified organization
            patient_encounters = await self._get_patient_encounters(patient.patient_id)
            if any(
                enc.service_provider == organization_id for enc in patient_encounters
            ):
                patients.append(patient)

        return patients

    async def _get_patient_encounters(self, patient_id: PatientId) -> List[Encounter]:
        """Helper method to get all encounters for a patient."""
        encounters_path = self._collection_path.parent / "encounters"
        if not encounters_path.exists():
            return []

        encounters = []
        for path in encounters_path.glob("*.json"):
            if not path.is_file() or str(path).endswith("/archived"):
                continue

            async with aiofiles.open(path, "r") as f:
                data = await f.read()
            encounter_dict = orjson.loads(data)
            encounter = from_dict(Encounter, encounter_dict)

            if encounter.subject == patient_id:
                encounters.append(encounter)

        return encounters
