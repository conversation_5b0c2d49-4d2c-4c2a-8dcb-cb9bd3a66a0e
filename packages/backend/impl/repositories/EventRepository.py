from dataclasses import asdict
from pathlib import Path
from typing import List, Optional, Dict, Literal, Tuple
import datetime
import asyncio
import os
from datetime import date, timedelta

import aiofiles
from dacite import from_dict, Config
import orjson
from overrides import override
import logging

from packages.backend.models.encounter import EncounterId
from packages.backend.models.patient import PatientId
from packages.backend.models.authentication import Role
from packages.backend.models.event import (
    Event,
    EventType,
    TextMessage,
    TextType,
    ConnexionAttemptDetails,
)
from packages.backend.spi.repositories import EventRepository
from packages.backend.impl.utils.time_u import timestamp_now


_LOGGER = logging.getLogger(__name__)

_FILE_LOCKS: Dict[Path, asyncio.Lock] = {}
_LOCK = asyncio.Lock()


class EventRepositoryImpl(EventRepository):
    def __init__(self, collection_path: Path) -> None:
        self._collection_path = collection_path
        self._collection_path.mkdir(parents=True, exist_ok=True)

    def _get_file_path(self, date: datetime.date) -> Path:
        """Generates the file path for a given date using .jsonl extension."""
        # Using .jsonl convention for JSON Lines format
        return self._collection_path / f"{date.isoformat()}.jsonl"

    async def _get_lock(self, file_path: Path) -> asyncio.Lock:
        async with _LOCK:
            if file_path not in _FILE_LOCKS:
                _FILE_LOCKS[file_path] = asyncio.Lock()
            return _FILE_LOCKS[file_path]

    @override
    async def put_event(self, event: Event) -> Event:
        """Appends a single event as a JSON line to the daily event file."""
        # Convert timestamp from ms to seconds for date calculation
        event_date = datetime.datetime.fromtimestamp(
            event.timestamp / 1000, tz=datetime.timezone.utc
        ).date()
        file_path = self._get_file_path(event_date)
        _LOGGER.debug(f"Appending event to {file_path}")

        # Convert single event to JSON bytes, followed by a newline
        event_dict = asdict(event)
        # Use default=str for potential non-serializable types if models evolve, though less likely here
        json_line = orjson.dumps(event_dict) + b"\n"

        file_lock = await self._get_lock(file_path)
        async with file_lock:
            try:
                # Open in append binary mode ('ab')
                async with aiofiles.open(file_path, "ab") as f:
                    await f.write(json_line)
                _LOGGER.debug(f"Successfully appended event to {file_path}")
            except Exception as e:
                _LOGGER.error(f"Failed to append event to {file_path}: {e}")
                raise  # Re-raise after logging

        return event

    async def _read_and_filter_file(
        self,
        file_path: Path,
        encounter_id: Optional[EncounterId],
        patient_id: Optional[PatientId],
        event_type: Optional[EventType],
    ) -> List[Event]:
        """Reads a single .jsonl file, parses events, and applies filters."""
        events: List[Event] = []
        # Configure dacite to handle enums from string values
        dacite_config = Config(
            type_hooks={EventType: EventType, Role: Role, TextType: TextType}
        )

        try:
            async with aiofiles.open(file_path, "rb") as f:
                async for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    data_as_dict = None
                    try:
                        data_as_dict = orjson.loads(line)
                        # Use the config with type_hooks here
                        event = from_dict(Event, data_as_dict, config=dacite_config)

                        if (
                            encounter_id is not None
                            and event.encounter_id != encounter_id
                        ):
                            continue
                        if patient_id is not None and event.patient_id != patient_id:
                            continue
                        if event_type is not None and event.event_type != event_type:
                            continue
                        events.append(event)
                    except orjson.JSONDecodeError as json_err:
                        _LOGGER.warning(
                            f"Skipping invalid JSON line in {file_path}: {json_err} - Line: {line[:100]}..."
                        )
                        continue
                    except Exception as parse_err:  # This includes DaciteError
                        _LOGGER.warning(
                            f"Skipping event due to parsing error in {file_path}: {parse_err} - Data: {repr(data_as_dict)}"
                        )
                        continue
        except FileNotFoundError:
            _LOGGER.debug(f"Event file not found: {file_path}")
            return []
        except Exception as e:
            _LOGGER.error(f"Failed to read or process event file {file_path}: {e}")
            return []
        return events

    @override
    async def list_events(
        self,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        encounter_id: Optional[EncounterId] = None,
        patient_id: Optional[PatientId] = None,
        event_type: Optional[EventType] = None,
    ) -> List[Event]:
        """Lists events, optionally filtering by date range and other criteria."""

        all_events: List[Event] = []
        file_paths_to_read: List[Path] = []

        def get_date_from_filename(filename: str) -> Optional[date]:
            try:
                return date.fromisoformat(filename.split(".")[0])
            except ValueError:
                _LOGGER.warning(f"Could not parse date from filename: {filename}")
                return None

        # Handle case with no dates provided first
        if start_date is None and end_date is None:
            _LOGGER.debug(
                f"No start or end date provided. Listing all event files in {self._collection_path}"
            )
            try:
                for f_name in os.listdir(self._collection_path):
                    if f_name.endswith(".jsonl"):
                        file_paths_to_read.append(self._collection_path / f_name)
            except Exception as e:
                _LOGGER.error(f"Error listing files in {self._collection_path}: {e}")
                return []
        else:
            # 1. Find actual min/max dates from existing files if dates are involved
            actual_dates_found: List[date] = []
            try:
                for f_name in os.listdir(self._collection_path):
                    if f_name.endswith(".jsonl"):
                        f_date = get_date_from_filename(f_name)
                        if f_date:
                            actual_dates_found.append(f_date)
            except Exception as e:
                _LOGGER.error(
                    f"Error listing or parsing files in {self._collection_path}: {e}"
                )
                return []

            if not actual_dates_found:
                _LOGGER.debug(
                    f"No event files found in {self._collection_path} to apply date filters to."
                )
                return []

            min_date_found = min(actual_dates_found)
            max_date_found = max(actual_dates_found)

            # 2. Determine effective date range
            effective_start_date = start_date if start_date else min_date_found
            effective_end_date = end_date if end_date else max_date_found

            # Adjust effective dates based on actual data available
            effective_start_date = max(effective_start_date, min_date_found)
            effective_end_date = min(effective_end_date, max_date_found)

            # 3. Check if the range is valid
            if effective_start_date > effective_end_date:
                _LOGGER.debug(
                    f"Effective start date {effective_start_date.isoformat()} is after effective end date {effective_end_date.isoformat()}. No events to list."
                )
                return []

            # 4. Generate file paths for the effective date range
            current_date = effective_start_date
            while current_date <= effective_end_date:
                file_paths_to_read.append(self._get_file_path(current_date))
                current_date += timedelta(days=1)

            _LOGGER.debug(
                f"Identified {len(file_paths_to_read)} potential event files to read between {effective_start_date.isoformat()} and {effective_end_date.isoformat()}"
            )

        if not file_paths_to_read:
            _LOGGER.debug("No event files to process based on criteria.")
            return []

        # Read and filter events from all identified files
        tasks = [
            self._read_and_filter_file(fp, encounter_id, patient_id, event_type)
            for fp in file_paths_to_read
        ]
        results = await asyncio.gather(*tasks)
        for result_list in results:
            all_events.extend(result_list)

        # Sort combined results by timestamp (descending)
        all_events.sort(key=lambda x: x.timestamp, reverse=True)

        _LOGGER.debug(
            f"Returning {len(all_events)} events after processing {len(file_paths_to_read)} file(s)."
        )
        return all_events

    @override
    async def log_encounter_created(
        self, encounter_id: EncounterId, patient_id: PatientId
    ) -> None:
        """Logs an ENCOUNTER_CREATED event."""
        event = Event(
            timestamp=timestamp_now(),
            event_type=EventType.ENCOUNTER_CREATED,
            encounter_id=encounter_id,
            patient_id=patient_id,
        )
        await self.put_event(event)
        _LOGGER.debug(f"Logged ENCOUNTER_CREATED for encounter {encounter_id}")

    @override
    async def log_text_message_sent(
        self,
        encounter_id: EncounterId,
        patient_id: PatientId,
        role: Role,
        recipient: str,
        text: str,
        text_type: TextType,
    ) -> None:
        """Logs a TEXT_MESSAGE_SENT event."""
        event = Event(
            timestamp=timestamp_now(),
            event_type=EventType.TEXT_MESSAGE_SENT,
            encounter_id=encounter_id,
            patient_id=patient_id,
            role=role,
            text_message_details=TextMessage(
                text=text, recipient=recipient, text_type=text_type
            ),
        )
        await self.put_event(event)
        _LOGGER.debug(
            f"Logged TEXT_MESSAGE_SENT ({text_type.name}) for encounter {encounter_id} to {recipient}"
        )

    @override
    async def log_connexion_attempt(
        self,
        encounter_id: EncounterId,
        patient_id: PatientId,
        role: Role,
        success: bool,
        failure_reason: Optional[str] = None,
    ) -> None:
        """Logs a CONNEXION_ATTEMPT event."""
        event = Event(
            timestamp=timestamp_now(),
            event_type=EventType.CONNEXION_ATTEMPT,
            encounter_id=encounter_id,
            patient_id=patient_id,
            role=role,
            connexion_details=ConnexionAttemptDetails(
                success=success, failure_reason=failure_reason
            ),
        )
        await self.put_event(event)
        _LOGGER.debug(
            f"Logged CONNEXION_ATTEMPT ({'success' if success else 'failure'}) for encounter {encounter_id} as {role.name}"
        )

    @override
    async def log_login_page_viewed(
        self, encounter_id: EncounterId, patient_id: Optional[PatientId], role: Role
    ) -> None:
        """Logs a LOGIN_PAGE_VIEWED event."""
        event = Event(
            timestamp=timestamp_now(),
            event_type=EventType.LOGIN_PAGE_VIEWED,
            encounter_id=encounter_id,
            patient_id=patient_id,  # Can be None if encounter lookup failed
            role=role,
        )
        await self.put_event(event)
        _LOGGER.debug(
            f"Logged LOGIN_PAGE_VIEWED for encounter {encounter_id} as {role.name}"
        )

    @override
    async def log_sent_share_link(
        self, encounter_id: EncounterId, patient_id: PatientId, role: Role
    ) -> None:
        """Logs a SENT_SHARE_LINK event."""
        event = Event(
            timestamp=timestamp_now(),
            event_type=EventType.SENT_SHARE_LINK,
            encounter_id=encounter_id,
            patient_id=patient_id,
            role=role,
        )
        await self.put_event(event)
        _LOGGER.debug(
            f"Logged SENT_SHARE_LINK for encounter {encounter_id} by {role.name}"
        )

    @override
    async def log_accepted_tos(
        self, encounter_id: EncounterId, patient_id: Optional[PatientId], role: Role
    ) -> None:
        """Logs an ACCEPTED_TOS event."""
        event = Event(
            timestamp=timestamp_now(),
            event_type=EventType.ACCEPTED_TOS,
            encounter_id=encounter_id,
            patient_id=patient_id,
            role=role,
        )
        await self.put_event(event)
        _LOGGER.debug(
            f"Logged ACCEPTED_TOS for encounter {encounter_id} as {role.name}"
        )
