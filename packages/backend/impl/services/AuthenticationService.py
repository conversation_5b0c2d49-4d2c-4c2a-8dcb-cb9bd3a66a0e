from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, Literal
from fastapi import HTTPException
import jwt
from jwt import PyJWKClient
from overrides import override
from packages.backend.impl.utils.time_u import timestamp_now
from packages.backend.models.authentication import Role, Token, User
from packages.backend.models.common import ISODate
from packages.backend.models.encounter import Encounter, EncounterId, ShareId
from packages.backend.models.event import ConnexionAttemptDetails, Event, EventType
from packages.backend.models.organization import OrganizationId
from packages.backend.spi.repositories import (
    EncounterRepository,
    OrganizationRepository,
    PatientRepository,
    EventRepository,
)
from packages.backend.spi.services import AuthenticationService


class AuthenticationServiceImpl(AuthenticationService):
    def __init__(
        self,
        encounter_respository: EncounterRepository,
        patient_repository: PatientRepository,
        organization_repository: OrganizationRepository,
        event_repository: EventRepository,
        secret_key: str,
        algorithm: str,
        sso_jwks_url: str,
        access_token_expire_minutes: int,
    ):
        self._encounter_respository = encounter_respository
        self._patient_repository = patient_repository
        self._organization_repository = organization_repository
        self._event_repository = event_repository
        self._secret_key = secret_key
        self._algorithm = algorithm
        self._access_token_expire_minutes = access_token_expire_minutes
        self._sso_jwks_url = sso_jwks_url

    async def _is_patient_session_expired(self, encounter: Encounter) -> bool:
        """Checks if the patient/relative session has expired based on discharge time.

        Returns True if the session has expired, False otherwise.
        """
        if encounter.actual_period.end:
            organization = await self._organization_repository.get_organization_by_id(
                encounter.service_provider
            )
            timeout_minutes = organization.patient_session_timeout_minutes
            discharge_time_ms = encounter.actual_period.end
            # Convert milliseconds to seconds for fromtimestamp
            discharge_time_sec = discharge_time_ms / 1000.0
            discharge_datetime = datetime.fromtimestamp(
                discharge_time_sec, timezone.utc
            )
            expiration_time = discharge_datetime + timedelta(minutes=timeout_minutes)

            if datetime.now(timezone.utc) > expiration_time:
                return True  # Session has expired

        return False  # Session is not expired (or no end date)

    @override
    async def get_user_from_bearer_token(self, token: str) -> Optional[User]:
        try:
            # We try to authenticate the user using the token generated PPU.
            payload = jwt.decode(token, self._secret_key, algorithms=[self._algorithm])
            encounter_id: EncounterId | None = payload.get("sub")
            role_as_value = payload.get("role")
            role = Role(role_as_value) if role_as_value is not None else None
            if (encounter_id is None) or (role is None):
                return None

            # Check that the encounter exists
            encounter = await self._encounter_respository.get_encounter_by_id(
                encounter_id
            )
            if encounter is None:
                return None

            # Check session timeout only for patient/relative roles
            if role == Role.PATIENT or role == Role.RELATIVE:
                # Call the check and raise if expired, but don't log connection attempt
                if await self._is_patient_session_expired(encounter):
                    raise HTTPException(
                        status_code=401,
                        detail="Patient session has expired due to discharge.",
                    )

            return User(user_id=encounter_id, role=role)

        except jwt.ExpiredSignatureError:
            # Handle expired token specifically if needed, otherwise fall through
            raise HTTPException(status_code=401, detail="Token has expired")

        except HTTPException as e:
            # Re-raise known HTTP exceptions (like the 401 for session timeout)
            raise e
        except Exception:
            # If the token is not valid, we try to authenticate the user or service using the SSO.
            try:
                try:
                    jwks_client = PyJWKClient(self._sso_jwks_url, timeout=5)
                except Exception as e:
                    raise HTTPException(
                        status_code=503,
                        detail=f"SSO authentication service is not available: {str(e)}",
                    )

                signing_key = jwks_client.get_signing_key_from_jwt(token)
                payload = jwt.decode(
                    token,
                    signing_key.key,
                    algorithms=["RS256"],
                    options={"verify_signature": True, "verify_aud": False},
                )

                if payload.get("client_id") == "ppu":
                    # This is a service token
                    return User(
                        user_id="ppu_service", role=Role.SERVICE, organization_id=None
                    )
                else:
                    # This is a practitioner token
                    practitioner = await self.authenticate_practitioner(payload)
                    if practitioner is None:
                        return None

                    return User(
                        user_id=practitioner.user_id,
                        role=Role.PRACTITIONER,
                        organization_id=practitioner.organization_id,
                    )
            except Exception:
                return None

    @override
    async def get_principal_from_api_key(
        self, email: str, api_key: str
    ) -> Optional[User]:
        # We do not implement this method for the moment
        return None

    @override
    async def authenticate_user(
        self,
        id: EncounterId | ShareId,
        truncated_last_name: str,
        birth_date: ISODate,
    ) -> User:
        encounter = await self._encounter_respository.get_encounter_by_id(id)
        patient = await self._patient_repository.get_patient_by_id(encounter.subject)

        # Determine role enum early for logging purposes
        role: Role = Role.RELATIVE if id == encounter.share_id else Role.PATIENT

        if (
            patient.name.last_name[:3].upper() != truncated_last_name.upper()
            or patient.birth_date != birth_date
        ):
            # Log credential mismatch failure
            await self._event_repository.log_connexion_attempt(
                encounter_id=encounter.encounter_id,
                patient_id=encounter.subject,
                role=role,
                success=False,
                failure_reason="Name or birth date mismatch",
            )
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Check session timeout (applies implicitly to patient/relative here)
        if await self._is_patient_session_expired(encounter):
            # Log connexion attempt failure specifically for session timeout *during login*
            await self._event_repository.log_connexion_attempt(
                encounter_id=encounter.encounter_id,
                patient_id=encounter.subject,
                role=role,
                success=False,
                failure_reason="Session expired due to discharge",
            )
            # Raise the exception after logging
            raise HTTPException(
                status_code=401,
                detail="Session expired due to discharge.",
            )

        # Determine role and user_id based on the input id
        user_id: EncounterId | ShareId
        if id == encounter.share_id:
            user_id = encounter.share_id
        else:
            user_id = encounter.encounter_id

        # Log the successful connexion attempt
        await self._event_repository.log_connexion_attempt(
            encounter_id=encounter.encounter_id,
            patient_id=encounter.subject,
            role=role,
            success=True,
        )

        # Return the User object
        return User(user_id=user_id, role=role)

    @override
    async def create_access_token(self, encounter_id: EncounterId, role: Role) -> Token:
        expires_delta = timedelta(minutes=self._access_token_expire_minutes)
        data: Dict[str, Any] = {"sub": encounter_id, "role": role.value}
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + expires_delta
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(
            to_encode,
            self._secret_key,
            algorithm=self._algorithm,
        )
        return Token(access_token=encoded_jwt, token_type="bearer")

    @override
    async def authenticate_practitioner(
        self, payload: Dict[str, Any]
    ) -> Optional[User]:
        """
        This is currently more of a proof of concept than a real implementation.
        We need full details of how the attributes will be setup in the SSO before making a solid implementation.
        """
        rpps = payload.get("rpps")
        finess = payload.get("finess")

        if not rpps or not finess:
            return None

        return User(
            user_id=rpps,
            role=Role.PRACTITIONER,
            organization_id=OrganizationId(finess),
        )
