import logging
from typing import List, Optional
from packages.backend.models.encounter import EncounterId
from packages.backend.models.service_request import ServiceRequest
from packages.backend.models.activity_definition import (
    ActivityDefinition,
    SendNotification,
)
from packages.backend.spi.repositories import (
    OrganizationRepository,
    PatientRepository,
    EncounterRepository,
    ServiceRequestRepository,
)


class NotificationService:
    def __init__(
        self,
        sms_provider,
        email_provider,
        organization_repository: OrganizationRepository,
        patient_repository: PatientRepository,
        encounter_repository: EncounterRepository,
        service_request_repository: ServiceRequestRepository,
    ):
        self.sms_provider = sms_provider
        self.email_provider = email_provider
        self._organization_repository = organization_repository
        self._patient_repository = patient_repository
        self._encounter_repository = encounter_repository
        self._service_request_repository = service_request_repository

    async def service_request_notification(
        self, service_request: ServiceRequest
    ) -> None:
        activity_definition = await self._get_activity_definition(service_request)
        if not activity_definition.send_notification:
            return

        previous_requests = await self._get_previous_requests(service_request)
        notifications = (
            activity_definition.send_notification
            if isinstance(activity_definition.send_notification, list)
            else [activity_definition.send_notification]
        )
        notification = self._get_applicable_notification(
            service_request, previous_requests, notifications
        )

        if notification:
            await self.send_sms_to_patient(
                service_request.encounter, notification.message
            )

    async def _get_activity_definition(
        self, service_request: ServiceRequest
    ) -> ActivityDefinition:
        activity_definition = (
            await self._organization_repository.get_activity_definition_by_id(
                service_request.activity_definition_id
            )
        )
        if activity_definition.send_notification is not None:
            activity_definition.send_notification = (
                [activity_definition.send_notification]
                if isinstance(activity_definition.send_notification, SendNotification)
                else activity_definition.send_notification
            )
        return activity_definition

    async def _get_previous_requests(
        self, service_request: ServiceRequest
    ) -> List[ServiceRequest]:
        """Get all previous service requests for the same activity in this encounter."""
        previous_service_requests = (
            await self._service_request_repository.list_service_requests(
                service_request.encounter, latest=False
            )
        )
        return [
            sr
            for sr in previous_service_requests
            if sr.service_request_id != service_request.service_request_id
            and sr.activity_definition_id == service_request.activity_definition_id
        ]

    def _get_applicable_notification(
        self,
        service_request: ServiceRequest,
        previous_requests: List[ServiceRequest],
        notifications: Optional[List[SendNotification]],
    ) -> Optional[SendNotification]:
        if not notifications:
            return None
        for notification in notifications:
            if self._should_send_notification(
                service_request, previous_requests, notification
            ):
                return notification
        return None

    def _should_send_notification(
        self,
        service_request: ServiceRequest,
        previous_requests: List[ServiceRequest],
        notification: SendNotification,
    ) -> bool:
        """
        Determine if a notification should be sent based on the current service request
        and all previous service requests for the same activity.

        A notification should be sent if:
        1. The current service request meets the trigger condition, AND
        2. No previous service request has already triggered this notification
        """
        # Check if the current service request meets the trigger condition
        if isinstance(notification.trigger, int):
            # For numeric triggers (wait_count), send if wait_count <= trigger
            current_meets_condition = (
                service_request.encounter_timing.wait_count <= notification.trigger
                and service_request.encounter_timing.wait_count != 0
            )

            # Check if any previous request has already triggered this notification
            for prev_req in previous_requests:
                if (
                    prev_req.encounter_timing.wait_count <= notification.trigger
                    and prev_req.encounter_timing.wait_count != 0
                ):
                    # A previous request already triggered this notification
                    return False

            return current_meets_condition

        elif isinstance(notification.trigger, str):
            # For string triggers (status), send if status matches
            current_meets_condition = service_request.status == notification.trigger

            # Check if any previous request has already triggered this notification
            for prev_req in previous_requests:
                if prev_req.status == notification.trigger:
                    # A previous request already triggered this notification
                    return False

            return current_meets_condition

        return False

    async def send_sms_to_patient(
        self, encounter_id: EncounterId, content: str
    ) -> None:
        encounter = await self._encounter_repository.get_encounter_by_id(encounter_id)
        patient = await self._patient_repository.get_patient_by_id(encounter.subject)
        if patient.phone:
            await self.send_sms(patient.phone, content)
        else:
            logging.warning(
                f"Skipping SMS to patient {patient.patient_id} (encounter {encounter_id}): No phone number."
            )

    async def send_sms(self, phone: str, content: str) -> None:
        self.sms_provider.send_sms(phone, content)

    async def send_email(self, email: str, content: str) -> None:
        pass
