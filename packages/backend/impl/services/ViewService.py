import asyncio
from typing import cast

from packages.backend.models.authentication import Role, User
from packages.backend.models.encounter import EncounterId
from packages.backend.models.view import PatientView, PractitionerView
from packages.backend.spi.repositories import (
    EncounterRepository,
    OrganizationRepository,
    QuestionnaireRepository,
    ServiceRequestRepository,
    PatientRepository,
)
from packages.backend.spi.services import ViewService


class ViewServiceImpl(ViewService):
    def __init__(
        self,
        encounter_repository: EncounterRepository,
        organization_respository: OrganizationRepository,
        service_request_repository: ServiceRequestRepository,
        questionnaire_repository: QuestionnaireRepository,
        patient_repository: PatientRepository,
    ) -> None:
        self._encounter_repository = encounter_repository
        self._organization_respository = organization_respository
        self._service_request_repository = service_request_repository
        self._questionnaire_repository = questionnaire_repository
        self._patient_repository = patient_repository

    async def get_patient_view(self, user: User) -> PatientView:
        encounter_id = cast(EncounterId, user.user_id)
        encounter = await self._encounter_repository.get_encounter_by_id(
            id=encounter_id
        )

        # Get the organization
        organization = await self._organization_respository.get_organization_by_id(
            encounter.service_provider
        )

        # Get the healthcare services
        healthcare_services = (
            await self._organization_respository.get_healthcare_service(
                encounter.service_provider
            )
        )

        # Get the activity definition
        activity_definitions = []
        if encounter.service_type:
            activity_definitions = (
                await self._organization_respository.get_activity_definition(
                    orgnization_id=encounter.service_provider,
                    healthcare_service_id=encounter.service_type,
                )
            )
        else:
            for healthcare_service in healthcare_services:
                activity_definitions.extend(
                    await self._organization_respository.get_activity_definition(
                        orgnization_id=encounter.service_provider,
                        healthcare_service_id=healthcare_service.healthcare_service_id,
                    )
                )

        # Get the service requests
        service_requests = await self._service_request_repository.list_service_requests(
            encounter.encounter_id
        )

        # Filter some information about the patient if the user is a close relative
        if user.role == Role.RELATIVE:
            shareable_activities = [
                a.activity_definition_id for a in activity_definitions if a.shareable
            ]
            service_requests = [
                s
                for s in service_requests
                if s.activity_definition_id in shareable_activities
            ]

        # Get the questionnaires
        questionnaires = []
        if not user.role == Role.RELATIVE:
            questionnaires = await asyncio.gather(
                *[
                    self._questionnaire_repository.get_questionnaire_by_id(q)
                    for s in healthcare_services
                    for q in s.questionnaire
                ]
            )

        if user.role == Role.RELATIVE:
            encounter.encounter_id = cast(EncounterId, encounter.share_id)

        return PatientView(
            encounter=encounter,
            organization=organization,
            healthcare_service=healthcare_services,
            service_request=service_requests,
            questionnaire=questionnaires,
        )

    async def get_practitioner_view(self, user: User) -> PractitionerView:
        assert user.organization_id is not None
        encounters = await self._encounter_repository.list_encounters(
            organization_id=user.organization_id
        )

        organization = await self._organization_respository.get_organization_by_id(
            user.organization_id
        )

        healthcare_services = (
            await self._organization_respository.get_healthcare_service(
                user.organization_id
            )
        )

        patients = [
            await self._patient_repository.get_patient_by_id(e.subject)
            for e in encounters
        ]

        return PractitionerView(
            encounters=encounters,
            patients=patients,
            organization=organization,
            healthcare_service=healthcare_services,
        )
