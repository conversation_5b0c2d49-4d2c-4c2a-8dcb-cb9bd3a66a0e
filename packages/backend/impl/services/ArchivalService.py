from datetime import datetime
from overrides import override
from packages.backend.spi.repositories import (
    EncounterRepository,
    PatientRepository,
    QuestionnaireResponseRepository,
    ServiceRequestRepository,
)
from packages.backend.spi.services import ArchivalService


class ArchivalServiceImpl(ArchivalService):
    def __init__(
        self,
        encounter_repository: EncounterRepository,
        patient_repository: PatientRepository,
        questionnaire_response_repository: QuestionnaireResponseRepository,
        service_request_repository: ServiceRequestRepository,
    ):
        self._encounter_respository = encounter_repository
        self._patient_repository = patient_repository
        self._questionnaire_response_repository = questionnaire_response_repository
        self._service_request_repository = service_request_repository

    @override
    async def archive_old_data(self, days_to_keep: int = 3) -> None:
        # Get all encounters older than days_to_keep
        now = datetime.now()
        all_encounters = await self._encounter_respository.list_encounters()
        old_encounters = [
            encounter
            for encounter in all_encounters
            if encounter.actual_period
            and encounter.actual_period.start
            and (now - datetime.fromtimestamp(encounter.actual_period.start / 1e3)).days
            > days_to_keep
        ]

        for encounter in old_encounters:
            # Archive patient, disabled as we have no need for it now
            # await self._patient_repository.anonymize_patient(encounter.subject)
            # await self._patient_repository.archive_patient(encounter.subject)

            # Archive service requests
            service_requests = (
                await self._service_request_repository.list_service_requests(
                    encounter.encounter_id, latest=False
                )
            )
            for service_request in service_requests:
                await self._service_request_repository.archive_service_request(
                    encounter.encounter_id, service_request.service_request_id
                )

            # Archive questionnaire responses
            await self._questionnaire_response_repository.archive_question_responses(
                encounter.subject
            )

            # Archive encounter
            await self._encounter_respository.archive_encounter(encounter.encounter_id)
