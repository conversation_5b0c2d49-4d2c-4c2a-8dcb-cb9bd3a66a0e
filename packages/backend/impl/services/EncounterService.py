from starlette.exceptions import HTTPException
from overrides import override
import os
from datetime import datetime, timedelta, timezone
import logging
from zoneinfo import ZoneInfo
from packages.backend.impl.utils.time_u import timestamp_now
from packages.backend.impl.services.NotificationService import NotificationService
from packages.backend.models.common import Identifier
from packages.backend.models.encounter import Encounter, EncounterId
from packages.backend.models.event import TextType
from packages.backend.spi.repositories import (
    EncounterRepository,
    OrganizationRepository,
    PatientRepository,
    PutEncounterRequest,
    PatchEncounterRequest,
)
from packages.backend.spi.services import EncounterService
from packages.backend.spi.repositories import EventRepository
from packages.backend.models.authentication import Role


_LOGGER = logging.getLogger(__name__)

POST_DISCHARGE_PATIENT_SMS = "Merci de prendre 1mn pour répondre à un rapide questionnaire de satisfaction : https://forms.gle/utMiEhQnZnpFcrdj8"
POST_DISCHARGE_RELATIVE_SMS = "Merci de prendre 1mn pour répondre à un rapide questionnaire de satisfaction : https://forms.gle/DYW2rtW8hvgxdCoL8"


class EncounterServiceImpl(EncounterService):
    def __init__(
        self,
        encounter_repository: EncounterRepository,
        patient_repository: PatientRepository,
        organization_repository: OrganizationRepository,
        notification_service: NotificationService,
        event_repository: EventRepository,
    ):
        self._encounter_repository = encounter_repository
        self._patient_repository = patient_repository
        self._organization_repository = organization_repository
        self._notification_service = notification_service
        self._event_repository = event_repository
        self._base_url = os.getenv("PPU_BASE_URL", "https://ppu.urgenceschrono.com")

    @override
    async def create_encounter(
        self, put_encounter_request: PutEncounterRequest
    ) -> Encounter:
        patient = await self._patient_repository.get_patient_by_id(
            put_encounter_request.subject
        )
        await self._organization_repository.get_organization_by_id(
            put_encounter_request.service_provider
        )
        encounter = await self._encounter_repository.create_encounter(
            put_encounter_request
        )

        # Send SMS to patient if phone number exists
        chu_name = "CHU de Limoges" if encounter.service_provider == "chu-limoges" else "CH Saint Camille"
        content = f"Pour suivre l'avancée de votre parcours aux urgences du {chu_name}, cliquez sur ce lien: {self._base_url}/sign-in/{encounter.encounter_id}"
        if patient.phone:
            try:
                await self._notification_service.send_sms(
                    phone=patient.phone,
                    content=content,
                )
                await self._event_repository.log_text_message_sent(
                    encounter_id=encounter.encounter_id,
                    patient_id=patient.patient_id,
                    role=Role.PATIENT,
                    recipient=patient.phone,
                    text=content,
                    text_type=TextType.LOGIN_SMS,
                )
                _LOGGER.debug(
                    f"Sent initial SMS to patient {patient.phone} for encounter {encounter.encounter_id}"
                )
            except Exception as e:
                _LOGGER.error(
                    f"Failed to send initial SMS to patient {patient.phone} for encounter {encounter.encounter_id}: {e}"
                )
        else:
            _LOGGER.debug(
                f"Patient {patient.patient_id} has no phone number, skipping initial SMS."
            )

        # Send SMS to relatives using the share_id
        relative_sms_content = f"Suivez le parcours aux urgences de votre proche: {self._base_url}/sign-in/{encounter.share_id}"
        for contact in patient.contact:
            for telecom in contact.telecom:
                if telecom.system == "sms":
                    try:
                        await self._notification_service.send_sms(
                            phone=telecom.value,
                            content=relative_sms_content,
                        )
                        await self._event_repository.log_text_message_sent(
                            encounter_id=encounter.encounter_id,
                            patient_id=patient.patient_id,
                            role=Role.RELATIVE,
                            recipient=telecom.value,
                            text=relative_sms_content,
                            text_type=TextType.LOGIN_SMS,
                        )
                        _LOGGER.debug(
                            f"Sent share link SMS to relative {telecom.value} for encounter {encounter.encounter_id}"
                        )
                    except Exception as e:
                        _LOGGER.error(
                            f"Failed to send SMS to relative {telecom.value} for encounter {encounter.encounter_id}: {e}"
                        )

        await self._event_repository.log_encounter_created(
            encounter_id=encounter.encounter_id,
            patient_id=patient.patient_id,
        )
        return encounter

    @override
    async def update_encounter(
        self, encounter: Encounter | EncounterId, patch_request: PatchEncounterRequest
    ) -> Encounter:
        # If encounter is an EncounterId, fetch the Encounter object
        if not isinstance(encounter, Encounter):
            encounter = await self._encounter_repository.get_encounter_by_id(encounter)

        # Store existing instruction IDs before applying the patch
        existing_instruction_ids = set()
        if encounter.discharge and encounter.discharge.instructions:
            existing_instruction_ids = {
                instr.id for instr in encounter.discharge.instructions
            }

        # TEMP: if the patch_request has actual_period.end set to 0, we delete it to mitigate against a potential bug
        if patch_request.actual_period and patch_request.actual_period.end == 0:
            del patch_request.actual_period.end
            if not patch_request.actual_period.start:
                del patch_request.actual_period

        # Update only fields present in the patch_request
        for field, value in patch_request.__dict__.items():
            if value is not None:
                if field == "actual_period" and hasattr(encounter, field):
                    # For actual_period, only update the fields that are present in the patch
                    current_period = getattr(encounter, field)
                    for period_field, period_value in value.__dict__.items():
                        if period_value is not None:
                            setattr(current_period, period_field, period_value)
                elif field == "discharge" and hasattr(encounter, field):
                    # Merge discharge info smartly
                    current_discharge = getattr(encounter, field)
                    if current_discharge is None:
                        # If no existing discharge, just set it
                        setattr(encounter, field, value)
                    else:
                        # If discharge exists, update its fields
                        for discharge_field, discharge_value in value.__dict__.items():
                            if discharge_value is not None:
                                # Special handling for instructions list: append new ones
                                if discharge_field == "instructions" and isinstance(
                                    discharge_value, list
                                ):
                                    # Ensure instructions is initialized
                                    if (
                                        not hasattr(current_discharge, "instructions")
                                        or getattr(current_discharge, "instructions")
                                        is None
                                    ):
                                        setattr(current_discharge, "instructions", [])
                                    # Append only if not None or empty
                                    if discharge_value:
                                        # Create a set of existing instruction IDs for quick lookup
                                        existing_ids = {
                                            instr.id
                                            for instr in current_discharge.instructions
                                        }
                                        # Append only new instructions
                                        current_discharge.instructions.extend(
                                            [
                                                instr
                                                for instr in discharge_value
                                                if instr.id not in existing_ids
                                            ]
                                        )

                                else:
                                    # For other discharge fields, overwrite if new value provided
                                    setattr(
                                        current_discharge,
                                        discharge_field,
                                        discharge_value,
                                    )

                else:
                    # For other top-level fields, overwrite if new value provided
                    setattr(encounter, field, value)

        # --- SMS Sending Logic ---
        # Check if the patch included discharge instructions
        patch_discharge_instructions = (
            patch_request.discharge.instructions if patch_request.discharge else None
        )

        if patch_discharge_instructions:
            # Get IDs from the instructions in the patch
            patch_instruction_ids = {instr.id for instr in patch_discharge_instructions}

            # Determine if there are any *new* instructions in this patch
            new_instruction_ids = patch_instruction_ids - existing_instruction_ids

            # Send SMS only if there are new instructions AND the encounter now actually has instructions
            if (
                new_instruction_ids
                and encounter.discharge
                and encounter.discharge.instructions
            ):
                final_instructions = (
                    encounter.discharge.instructions
                )  # Use the final state after patch

                patient = await self._patient_repository.get_patient_by_id(
                    encounter.subject
                )
                # Send SMS only if patient has a phone number
                if patient.phone:
                    # Use spaces instead of newlines and remove bullet points
                    instruction_parts = []
                    # Iterate over the *final* instructions in the encounter after the update
                    for instruction in final_instructions:
                        instr_name = getattr(instruction, "name", "Instruction")
                        instruction_parts.append(
                            f"{instr_name}: {self._base_url}/consignes/{instruction.id}.pdf"
                        )

                    message = (
                        "Vos instructions de sortie sont disponibles: "
                        + " ".join(instruction_parts)
                    )

                    try:
                        await self._notification_service.send_sms(
                            phone=patient.phone,
                            content=message,
                        )
                        await self._event_repository.log_text_message_sent(
                            encounter_id=encounter.encounter_id,
                            patient_id=patient.patient_id,
                            role=Role.PATIENT,
                            recipient=patient.phone,
                            text=message,
                            text_type=TextType.POST_DISCHARGE_SMS,
                        )
                        _LOGGER.info(
                            f"Sent discharge instructions SMS (new instructions added) to patient {patient.phone} for encounter {encounter.encounter_id}"
                        )
                    except Exception as e:
                        _LOGGER.error(
                            f"Failed to send discharge instructions SMS to patient {patient.phone} for encounter {encounter.encounter_id}: {e}"
                        )
                else:
                    _LOGGER.info(
                        f"Patient {encounter.subject} has no phone number, skipping discharge instructions SMS."
                    )
            elif not new_instruction_ids:
                _LOGGER.debug(
                    f"Skipping discharge SMS for encounter {encounter.encounter_id}: Instructions in patch were not new."
                )

        await self._encounter_repository.save_encounter(encounter)
        return encounter

    @override
    async def get_encounter_by_identifier(self, identifier: Identifier) -> Encounter:
        all_encounters = await self._encounter_repository.list_encounters()
        matching_encounters = [
            encounter
            for encounter in all_encounters
            if encounter.identifier
            and any(
                id.value == identifier.value and id.system == identifier.system
                for id in encounter.identifier
            )
        ]

        if not matching_encounters:
            raise HTTPException(
                status_code=404,
                detail=f"No encounter found for identifier: {identifier}",
            )

        if len(matching_encounters) > 1:
            raise HTTPException(
                status_code=409,
                detail=f"Multiple encounters found for identifier: {identifier}",
            )

        return matching_encounters[0]

    @override
    async def reset_share_id(self, encounter_id: EncounterId) -> Encounter:
        encounter = await self._encounter_repository.get_encounter_by_id(encounter_id)

        # Generate new share_id and save
        new_share_id = await self._encounter_repository.generate_share_id(encounter_id)
        encounter.share_id = new_share_id

        await self._encounter_repository.save_encounter(encounter)
        return encounter

    async def _send_post_discharge_sms_to_patient(self, patient, encounter) -> bool:
        """Send post-discharge SMS to patient. Returns True if sent successfully or no phone (considered handled), False if failed."""
        if patient.phone:
            try:
                await self._notification_service.send_sms(
                    phone=patient.phone,
                    content=POST_DISCHARGE_PATIENT_SMS,
                )
                await self._event_repository.log_text_message_sent(
                    encounter_id=encounter.encounter_id,
                    patient_id=patient.patient_id,
                    role=Role.PATIENT,
                    recipient=patient.phone,
                    text=POST_DISCHARGE_PATIENT_SMS,
                    text_type=TextType.SATISFACTION_SURVEY,
                )
                _LOGGER.info(
                    f"Sent post-discharge SMS for encounter {encounter.encounter_id} to {patient.phone}"
                )
                return True
            except Exception as e:
                _LOGGER.error(
                    f"Failed to send post-discharge SMS to {patient.phone} for encounter {encounter.encounter_id}: {e}"
                )
                return False
        else:
            _LOGGER.debug(
                f"Patient {encounter.subject} has no phone number, skipping post-discharge SMS."
            )
            return True

    async def _send_post_discharge_sms_to_relatives(self, patient, encounter) -> None:
        """Send post-discharge survey SMS to all relatives with SMS contact."""
        for contact in getattr(patient, "contact", []):
            for telecom in getattr(contact, "telecom", []):
                if getattr(telecom, "system", None) == "sms":
                    try:
                        await self._notification_service.send_sms(
                            phone=telecom.value,
                            content=POST_DISCHARGE_RELATIVE_SMS,
                        )
                        await self._event_repository.log_text_message_sent(
                            encounter_id=encounter.encounter_id,
                            patient_id=patient.patient_id,  # Assuming patient context is available via `patient` variable in this scope
                            role=Role.RELATIVE,
                            recipient=telecom.value,
                            text=POST_DISCHARGE_RELATIVE_SMS,
                            text_type=TextType.SATISFACTION_SURVEY,
                        )
                        _LOGGER.info(
                            f"Sent post-discharge survey SMS to relative {telecom.value} for encounter {encounter.encounter_id}"
                        )
                    except Exception as e:
                        _LOGGER.error(
                            f"Failed to send post-discharge survey SMS to relative {telecom.value} for encounter {encounter.encounter_id}: {e}"
                        )

    async def check_and_send_delayed_sms(self) -> None:
        """Checks for recently discharged patients and sends a follow-up SMS, respecting quiet hours."""
        _LOGGER.debug("Running check for delayed post-discharge SMS...")
        encounters_processed = 0
        sms_sent_count = 0
        try:
            # Define the time threshold (e.g., 2 hours ago)
            # We only send the SMS if the patient was discharged at least 2 hours ago.
            min_discharge_delay = timedelta(hours=2)
            now = datetime.now(timezone.utc)  # Get current time once for efficiency
            paris_tz = ZoneInfo("Europe/Paris")  # Define Paris timezone

            # Get all encounters (potentially filter more in repo if performance becomes an issue)
            all_encounters = await self._encounter_repository.list_encounters()
            _LOGGER.debug(
                f"Checking {len(all_encounters)} total encounters for delayed SMS."
            )

            for encounter in all_encounters:
                encounters_processed += 1
                try:
                    # Check if the encounter is actually finished (has an end time)
                    # and if we haven't already sent the post-discharge SMS.
                    is_discharged = encounter.actual_period.end is not None
                    needs_sms_sending = encounter.post_discharge_sms_sent_at is None

                    if is_discharged and needs_sms_sending:
                        # Assert helps type checker know end is not None here
                        assert (
                            encounter.actual_period.end is not None
                        )  # Should always be true due to is_discharged check

                        # Convert the discharge timestamp (milliseconds) to a datetime object
                        discharge_timestamp_ms = encounter.actual_period.end
                        patient_discharge_time = datetime.fromtimestamp(
                            discharge_timestamp_ms / 1000, timezone.utc
                        )

                        # Check if 'discharge time + 2 hours' is earlier than the current time.
                        # i.e., has it been at least 2 hours since they were discharged?
                        if patient_discharge_time + min_discharge_delay < now:
                            _LOGGER.debug(
                                f"Encounter {encounter.encounter_id} ready for delayed SMS "
                                f"(Discharged at: {patient_discharge_time}, "
                                f"Delay needed: {min_discharge_delay}, Now: {now}). Processing..."
                            )

                            # Check for quiet hours in Paris time
                            now_paris = datetime.now(paris_tz)
                            quiet_start_hour = 23
                            quiet_end_hour = 8
                            is_quiet_hours = (
                                quiet_start_hour <= now_paris.hour
                                or now_paris.hour < quiet_end_hour
                            )

                            if is_quiet_hours:
                                _LOGGER.info(
                                    f"Current time in Paris ({now_paris.strftime('%H:%M:%S %Z')}) is within quiet hours ({quiet_start_hour}:00-{quiet_end_hour}:00). "
                                    f"Deferring post-discharge SMS for encounter {encounter.encounter_id}."
                                )
                                # Skip sending for now, will retry in the next scheduled run
                                continue  # Move to the next encounter in the loop

                            # --- Proceed with sending only if outside quiet hours ---
                            _LOGGER.debug(
                                f"Current time in Paris ({now_paris.strftime('%H:%M:%S %Z')}) is outside quiet hours. "
                                f"Proceeding with post-discharge SMS for encounter {encounter.encounter_id}."
                            )

                            patient = await self._patient_repository.get_patient_by_id(
                                encounter.subject
                            )

                            success = await self._send_post_discharge_sms_to_patient(
                                patient, encounter
                            )
                            if success:
                                encounter.post_discharge_sms_sent_at = timestamp_now()
                                await self._encounter_repository.save_encounter(
                                    encounter
                                )
                                sms_sent_count += 1
                            else:
                                # Don't mark as sent if failed to send to patient with phone
                                pass

                            # Always attempt to send to relatives regardless of patient SMS success
                            await self._send_post_discharge_sms_to_relatives(
                                patient, encounter
                            )
                except HTTPException as e:
                    # Handle cases like patient not found (404)
                    _LOGGER.error(
                        f"HTTP Error processing encounter {encounter.encounter_id} for delayed SMS: {e.detail}"
                    )
                except Exception as e:
                    # Catch other potential errors during processing of a single encounter
                    _LOGGER.error(
                        f"Error processing encounter {encounter.encounter_id} for delayed SMS: {e}",
                        exc_info=True,
                    )

        except Exception as e:
            # Catch errors during the overall task execution (e.g., repository call)
            _LOGGER.error(
                f"Error in check_and_send_delayed_sms task: {e}", exc_info=True
            )

        _LOGGER.debug(
            f"Finished check for delayed post-discharge SMS. Processed: {encounters_processed}, Sent: {sms_sent_count}. (Note: count reflects successful sends to patients with phone numbers)"
        )
