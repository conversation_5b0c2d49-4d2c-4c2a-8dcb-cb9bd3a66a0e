from contextlib import Async<PERSON><PERSON>t<PERSON>tack, contextmanager
from contextvars import ContextV<PERSON>

from dataclasses import dataclass
from functools import lru_cache
import os
from pathlib import Path
from typing import Awaitable, Callable, Iterator, Literal, Optional, Sequence

from packages.backend.impl.repositories.EncounterRepository import (
    EncounterRepositoryImpl,
)
from packages.backend.impl.repositories.EventRepository import EventRepositoryImpl
from packages.backend.impl.repositories.OrganizationRepository import (
    OrganizationRepositoryImpl,
)
from packages.backend.impl.repositories.PatientRepository import PatientRepositoryImpl
from packages.backend.impl.repositories.QuestionnaireRepository import (
    QuestionnaireRepositoryImpl,
)
from packages.backend.impl.repositories.QuestionnaireResponseRepository import (
    QuestionnaireResponseRepositoryImpl,
)
from packages.backend.impl.repositories.ServiceRequestRepository import (
    ServiceRequestRepositoryImpl,
)
from packages.backend.impl.services.ArchivalService import ArchivalServiceImpl
from packages.backend.impl.services.AuthenticationService import (
    AuthenticationServiceImpl,
)
from packages.backend.impl.services.EncounterService import EncounterServiceImpl
from packages.backend.impl.services.NotificationService import NotificationService
from packages.backend.impl.services.ViewService import ViewServiceImpl
from packages.backend.impl.sms.CapitoleMobile import CapitoleMobile
from packages.backend.models.common import Principal
from packages.backend.spi.repositories import (
    EncounterRepository,
    EventRepository,
    OrganizationRepository,
    PatientRepository,
    QuestionnaireRepository,
    QuestionnaireResponseRepository,
    ServiceRequestRepository,
)
from packages.backend.spi.services import (
    ArchivalService,
    AuthenticationService,
    EncounterService,
    ViewService,
)


@dataclass
class BackendModuleConfiguration:
    API_V1_STR: str
    ENV: Literal["local", "dev", "staging", "prod"]
    DB_PATH: Path
    ASSET_PATH: Path
    SECRET_KEY: str
    ALGORITHM: str
    SSO_JWKS_URL: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int

    def __post_init__(self) -> None:
        os.makedirs(self.DB_PATH, exist_ok=True)


@dataclass
class RequestContext:
    principal: Optional[Principal] = None


class RequestContextProvider:
    def __init__(self) -> None:
        self._ctx_var: ContextVar[Optional[RequestContext]] = ContextVar(
            "request_context", default=None
        )

    @contextmanager
    def set(self, ctx: RequestContext) -> Iterator[None]:
        token = self._ctx_var.set(ctx)
        try:
            yield
        finally:
            self._ctx_var.reset(token)

    def get(self) -> RequestContext:
        value = self._ctx_var.get()
        assert value is not None
        return value


@dataclass
class ScheduledTask:
    """
    A task that is scheduled to run periodically.
    """

    #: The name of the task.  Used for reporting.
    name: str

    #: Interval between two successive invocations of the task.
    #: This interval runs between the end of the previous invocation and the
    #: beginning of the next invocation.
    interval_ms: int

    #: The callback that runs the task.
    run: Callable[[], Awaitable[None]]


class BackendModule:
    def __init__(
        self,
        exit_stack: AsyncExitStack,
        config: BackendModuleConfiguration,
    ) -> None:
        self._exit_stack = exit_stack
        self._config = config

    @lru_cache
    def request_context_provider(self) -> RequestContextProvider:
        return RequestContextProvider()

    # Repositories
    def _get_collection_path(self, collection_name: str) -> Path:
        collection_path = self._config.DB_PATH / collection_name
        collection_path.mkdir(parents=True, exist_ok=True)
        return collection_path

    @lru_cache
    def patient_repository(self) -> PatientRepository:
        collection_path = self._get_collection_path("patients")
        return PatientRepositoryImpl(collection_path=collection_path)

    @lru_cache
    def service_request_repository(self) -> ServiceRequestRepository:
        collection_path = self._get_collection_path("service_requests")
        return ServiceRequestRepositoryImpl(collection_path=collection_path)

    @lru_cache
    def encounter_repository(self) -> EncounterRepository:
        collection_path = self._get_collection_path("encounters")
        return EncounterRepositoryImpl(collection_path=collection_path)

    @lru_cache
    def organization_repository(self) -> OrganizationRepository:
        collection_path = self._config.ASSET_PATH / "organizations.yaml"
        return OrganizationRepositoryImpl(collection_path=collection_path)

    @lru_cache
    def questionnaire_repository(self) -> QuestionnaireRepository:
        collection_path = self._config.ASSET_PATH / "questionnaires.yaml"
        return QuestionnaireRepositoryImpl(collection_path=collection_path)

    @lru_cache
    def questionnaire_response_repository(self) -> QuestionnaireResponseRepository:
        collection_path = self._get_collection_path("questionnaire_responses")
        return QuestionnaireResponseRepositoryImpl(collection_path=collection_path)

    @lru_cache
    def event_repository(self) -> EventRepository:
        collection_path = self._get_collection_path("events")
        return EventRepositoryImpl(collection_path=collection_path)

    # Services
    @lru_cache
    def notification_service(self) -> NotificationService:
        return NotificationService(
            sms_provider=CapitoleMobile(),
            email_provider=None,
            organization_repository=self.organization_repository(),
            patient_repository=self.patient_repository(),
            encounter_repository=self.encounter_repository(),
            service_request_repository=self.service_request_repository(),
        )

    @lru_cache
    def encounter_service(self) -> EncounterService:
        return EncounterServiceImpl(
            encounter_repository=self.encounter_repository(),
            patient_repository=self.patient_repository(),
            organization_repository=self.organization_repository(),
            notification_service=self.notification_service(),
            event_repository=self.event_repository(),
        )

    @lru_cache
    def view_service(self) -> ViewService:
        return ViewServiceImpl(
            encounter_repository=self.encounter_repository(),
            service_request_repository=self.service_request_repository(),
            organization_respository=self.organization_repository(),
            questionnaire_repository=self.questionnaire_repository(),
            patient_repository=self.patient_repository(),
        )

    @lru_cache
    def authentication_service(self) -> AuthenticationService:
        return AuthenticationServiceImpl(
            encounter_respository=self.encounter_repository(),
            patient_repository=self.patient_repository(),
            organization_repository=self.organization_repository(),
            event_repository=self.event_repository(),
            secret_key=self._config.SECRET_KEY,
            algorithm=self._config.ALGORITHM,
            sso_jwks_url=self._config.SSO_JWKS_URL,
            access_token_expire_minutes=self._config.ACCESS_TOKEN_EXPIRE_MINUTES,
        )

    @lru_cache
    def archival_service(self) -> ArchivalService:
        return ArchivalServiceImpl(
            encounter_repository=self.encounter_repository(),
            service_request_repository=self.service_request_repository(),
            patient_repository=self.patient_repository(),
            questionnaire_response_repository=self.questionnaire_response_repository(),
        )

    def scheduled_tasks(self) -> Sequence[ScheduledTask]:
        return [
            ScheduledTask(
                name="archive_old_data",
                # Run every 10 minutes (10 * 60 * 1000 ms)
                interval_ms=10 * 60 * 1000,
                run=lambda: self.archival_service().archive_old_data(days_to_keep=3),
            ),
            ScheduledTask(
                name="send_post_discharge_sms",
                # Run every 10 minutes (10 * 60 * 1000 ms)
                interval_ms=10 * 60 * 1000,
                run=self.encounter_service().check_and_send_delayed_sms,
            ),
        ]
