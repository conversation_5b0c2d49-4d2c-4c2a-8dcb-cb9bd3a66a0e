from contextlib import AsyncExitStack
from pathlib import Path
import random
import string
from fastapi.testclient import TestClient
import pytest

from packages.backend.impl.module import BackendModule
from packages.backend.models.common import Identifier
from packages.backend.server.api.deps import _module
from packages.backend.server.main import app

client = TestClient(app)


@pytest.fixture()
def temp_db_path(tmp_path):
    return tmp_path / "test.db"


@pytest.fixture(autouse=True)
def backend_module(temp_db_path, monkeypatch):
    import packages.backend.server.config as config

    config.config.DB_PATH = Path(temp_db_path)

    def _test_module() -> BackendModule:
        return BackendModule(
            exit_stack=AsyncExitStack(),
            config=config.config,
        )

    # Replace the _module dependency with the test module
    app.dependency_overrides[_module] = _test_module

    # Use monkeypatch to replace the original _module function
    # Both are required.
    import packages.backend.server.api.deps as deps

    with monkeypatch.context() as m:
        m.setattr(deps, "_module", _test_module)
        yield _test_module()


patients = [
    {
        "name": {"first_name": "<PERSON>", "last_name": "Doe"},
        "birth_date": "2000-01-01",
        "phone": "+33612345678",
    },
    {
        "name": {"first_name": "TwoLetterLastName", "last_name": "Hi"},
        "birth_date": "2000-01-04",
        "phone": "+33612345678",
    },
    {
        "name": {"first_name": "OneLetterLastName", "last_name": "D"},
        "birth_date": "2000-12-01",
        "phone": "+33612345678",
    },
    pytest.param(
        {
            "name": {"first_name": "NoLastName", "last_name": ""},
            "birth_date": "3000-01-01",
            "phone": "+33612345678",
        },
        marks=pytest.mark.xfail(reason="Patient has no last name"),
    ),
    pytest.param(
        {
            "name": {"first_name": "", "last_name": "NoFirstName"},
            "birth_date": "1000-01-01",
            "phone": "+33612345678",
        },
        marks=pytest.mark.xfail(reason="Patient has no first name"),
    ),
]


@pytest.fixture
def patient_login_token(create_patient, create_encounter):
    response = client.post(
        "/api/v1/login/access-token",
        params={
            "id": create_encounter["encounter_id"],
            "truncated_last_name": create_patient["name"]["last_name"][:3],
            "birth_date": create_patient["birth_date"],
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json()

    return response.json()["access_token"]


@pytest.fixture
def relative_login_token(create_patient, create_encounter):
    response = client.post(
        "/api/v1/login/access-token",
        params={
            "id": create_encounter["share_id"],
            "truncated_last_name": create_patient["name"]["last_name"][:3],
            "birth_date": create_patient["birth_date"],
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json()

    return response.json()["access_token"]


@pytest.fixture
def service_headers():
    """Headers for authenticating as a service"""
    return {
        "X-Dev-User-Id": "1",
        "X-Dev-Org-Id": "demo-akihospit",
        "X-Dev-Role": "SERVICE",
    }


@pytest.fixture
def practitioner_headers():
    """Headers for authenticating as a practitioner"""
    return {
        "X-Dev-User-Id": "1",
        "X-Dev-Org-Id": "demo-akihospit",
        "X-Dev-Role": "PRACTITIONER",
    }


@pytest.fixture(params=patients)
def create_patient(service_headers, request):
    """Modified to use service authentication"""
    response = client.post(
        "/api/v1/patient", json=request.param, headers=service_headers
    )
    assert response.status_code == 200
    assert "patient_id" in response.json()
    assert response.json()["patient_id"]
    return response.json()


@pytest.fixture()
def create_encounter(create_patient, service_headers):
    response = client.post(
        "/api/v1/encounter",
        json={
            "subject": create_patient["patient_id"],
            "service_provider": "demo-akihospit",
            "service_type": "demo-akihospit-emergency",
            "identifier": [
                Identifier(
                    system="https://www.demo-akihospit.com",
                    value="".join(
                        random.choices(string.ascii_letters + string.digits, k=10)
                    ),
                ).__dict__
            ],
        },
        headers=service_headers,
    )
    assert response.status_code == 200
    assert "encounter_id" in response.json()
    assert response.json()["encounter_id"]
    assert response.json()["subject"] == create_patient["patient_id"]
    assert response.json()["service_type"] == "demo-akihospit-emergency"
    assert not response.json()["discharge"]
    return response.json()
