from fastapi.testclient import TestClient
from packages.backend.server.main import app

client = TestClient(app)


def test_list_questionnaires(practitioner_headers):
    """Test listing available questionnaires (requires practitioner auth)."""
    response = client.get("/api/v1/questionnaire", headers=practitioner_headers)
    assert response.status_code == 200
    questionnaires = response.json()
    assert isinstance(questionnaires, list)

    # Check structure if questionnaires exist
    if questionnaires:
        for questionnaire in questionnaires:
            assert "questionnaire_id" in questionnaire
            assert "name" in questionnaire
            # Add more checks based on expected Questionnaire model fields if needed


def test_list_questionnaires_unauthorized(service_headers):
    """Test that listing questionnaires is forbidden for services."""
    response = client.get("/api/v1/questionnaire", headers=service_headers)
    # Expecting 401 (Unauthorized) or 403 (Forbidden)
    assert response.status_code in [401, 403]


def test_list_questionnaires_no_auth():
    """Test that listing questionnaires requires authentication."""
    response = client.get("/api/v1/questionnaire")
    assert response.status_code == 401  # Unauthorized without token


# --- Questionnaire Response API Tests (Moved from test_questionnaire_response.py) ---


def test_add_questionnaire_response(
    patient_login_token, create_patient
):  # Added create_patient
    """Test adding a questionnaire response (requires patient auth)."""
    headers = {"Authorization": f"Bearer {patient_login_token}"}
    response_data = {
        "questionnaire_id": "douleur",  # Assuming 'douleur' questionnaire exists
        "authored": **********,  # Example timestamp
        "subject": create_patient["patient_id"],  # Use patient_id from fixture
        "item": [{"link_id": "douleur-intensite", "answer": [{"value_integer": 5}]}],
    }

    response = client.post(
        "/api/v1/questionnaire-response", json=response_data, headers=headers
    )
    # Expect 200 OK or 201 Created
    assert response.status_code in [200, 201]
    # Optional: Check response body structure if needed
    # assert "questionnaire_response_id" in response.json()


def test_get_questionnaire_response_for_patient(practitioner_headers, create_patient):
    """Test retrieving questionnaire responses for a patient (requires practitioner auth)."""
    # First, optionally add a response to ensure there's data to retrieve
    # (Could use the test_add_questionnaire_response logic or a dedicated fixture)

    response = client.get(
        # Use f-string for clarity
        f"/api/v1/questionnaire-response?questionnaire_id=douleur&patient_id={create_patient['patient_id']}",
        headers=practitioner_headers,
    )
    assert response.status_code == 200
    responses = response.json()
    assert isinstance(responses, list)

    # Check structure of retrieved responses
    for resp in responses:
        assert "questionnaire_id" in resp
        assert resp["questionnaire_id"] == "douleur"
        assert "authored" in resp
        assert "subject" in resp
        assert resp["subject"] == create_patient["patient_id"]
        assert "item" in resp
        assert isinstance(resp["item"], list)

        # Deeper check on item structure
        for item in resp["item"]:
            assert "link_id" in item
            assert "answer" in item
            assert isinstance(item["answer"], list)
            # Further checks based on expected answer types (e.g., value_integer)
            if item["answer"]:
                assert len(item["answer"]) > 0  # Ensure answer array isn't empty
                # Example check, adapt based on actual data model
                assert any(k.startswith("value_") for k in item["answer"][0])


def test_get_questionnaire_response_unauthorized(service_headers, create_patient):
    """Test that retrieving responses is forbidden for services."""
    response = client.get(
        f"/api/v1/questionnaire-response?questionnaire_id=douleur&patient_id={create_patient['patient_id']}",
        headers=service_headers,
    )
    # Expecting 401 (Unauthorized) or 403 (Forbidden)
    assert response.status_code in [401, 403]


def test_get_questionnaire_response_no_auth(create_patient):
    """Test that retrieving responses requires authentication."""
    response = client.get(
        f"/api/v1/questionnaire-response?questionnaire_id=douleur&patient_id={create_patient['patient_id']}",
    )
    assert response.status_code == 401  # Unauthorized
