from datetime import datetime, timed<PERSON><PERSON>
import random
import string
from fastapi.testclient import Test<PERSON>lient
from packages.backend.models.common import Identifier
from packages.backend.server.main import app
import pytest

client = TestClient(app)

# --- Fixtures (from test_encounter.py) ---


@pytest.fixture
def random_identifier():
    return Identifier(
        system="https://www.demo-akihospit.com",
        value="".join(random.choices(string.ascii_letters + string.digits, k=10)),
    ).__dict__


# --- Fixtures (from test_encounter_discharge.py) ---
# Helper fixture for creating service requests, needed by some tests
@pytest.fixture
def create_service_request_helper(service_headers):
    def _create_service_request(encounter_id, activity_id, status="not-yet-started"):
        response = client.post(
            "/api/v1/service_request",
            json={
                "encounter_id": encounter_id,
                "activity_definition_id": activity_id,
                "status": status,
                "encounter_timing": {"planned_end_date": 0, "wait_count": 0},
            },
            headers=service_headers,
        )
        assert response.status_code == 200
        return response.json()

    return _create_service_request


# --- API Tests (Moved from test_encounter.py) ---


def test_create_encounter(create_encounter):
    assert create_encounter["encounter_id"] is not None
    assert create_encounter["subject"] is not None
    assert create_encounter["service_provider"] is not None


def test_get_encounter_by_id(create_encounter, service_headers):
    response = client.get(
        f"/api/v1/encounter/{create_encounter['encounter_id']}",
        headers=service_headers,
    )
    assert response.status_code == 200
    assert response.json()["encounter_id"] == create_encounter["encounter_id"]
    assert response.json()["subject"] == create_encounter["subject"]
    assert response.json()["service_provider"] == create_encounter["service_provider"]


def test_get_encounter_by_identifier(create_encounter, service_headers):
    response = client.get(
        f"/api/v1/encounter?identifier={create_encounter['identifier'][0]['system']}|{create_encounter['identifier'][0]['value']}",
        headers=service_headers,
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["encounter_id"] == create_encounter["encounter_id"]
    assert response.json()[0]["subject"] == create_encounter["subject"]
    assert (
        response.json()[0]["service_provider"] == create_encounter["service_provider"]
    )


def test_get_encounter_by_identifier_with_multiple_encounters(
    create_patient, create_encounter, random_identifier, service_headers
):
    new_encounter = {
        "subject": create_patient["patient_id"],
        "service_provider": "demo-akihospit",
        "service_type": "demo-akihospit-emergency",
        "identifier": [random_identifier],
    }
    response = client.post(
        "/api/v1/encounter", json=new_encounter, headers=service_headers
    )
    assert response.status_code == 200

    response = client.get(
        f"/api/v1/encounter?identifier={create_encounter['identifier'][0]['system']}|{create_encounter['identifier'][0]['value']}",
        headers=service_headers,
    )
    assert response.status_code == 200
    assert len(response.json()) == 1
    assert response.json()[0]["encounter_id"] == create_encounter["encounter_id"]


def test_get_encounter_by_identifier_with_no_matching_encounters(
    create_patient, random_identifier, service_headers
):
    # Create a couple of encounters to ensure the repository isn't empty
    for _ in range(2):
        new_encounter = {
            "subject": create_patient["patient_id"],
            "service_provider": "demo-akihospit",
            "service_type": "demo-akihospit-emergency",
            "identifier": [random_identifier],
        }
        response = client.post(
            "/api/v1/encounter", json=new_encounter, headers=service_headers
        )
        assert response.status_code == 200

    response = client.get(
        "/api/v1/encounter?identifier=https://www.non-existent-system.com|non-existent-value",
        headers=service_headers,
    )
    assert response.status_code == 404


def test_get_unknown_encounter_by_id(service_headers):
    response = client.get(
        "/api/v1/encounter/unknown_encounter_id", headers=service_headers
    )
    assert response.status_code == 404


def test_update_encounter_patch_fields(create_encounter, service_headers):
    # Renamed from test_update_encounter to be more specific
    encounter_data = {"planned_length": 999}  # Patch only specific field

    response = client.patch(
        f"/api/v1/encounter/{create_encounter['encounter_id']}",
        json=encounter_data,
        headers=service_headers,
    )
    assert response.status_code == 200
    assert response.json()["planned_length"] == 999


def test_update_encounter_by_identifier(create_patient, service_headers):
    encounters = []
    for i in range(2):
        new_encounter = {
            "subject": create_patient["patient_id"],
            "service_provider": "demo-akihospit",
            "service_type": "demo-akihospit-emergency",
            "identifier": [
                Identifier(
                    system="https://www.demo-akihospit.com",
                    value=f"test-encounter-upd-{i}",  # Unique value
                ).__dict__
            ],
        }
        response = client.post(
            "/api/v1/encounter", json=new_encounter, headers=service_headers
        )
        assert response.status_code == 200
        encounters.append(response.json())

    update_data = {"planned_length": 123}

    response = client.patch(
        "/api/v1/encounter?identifier=https://www.demo-akihospit.com|test-encounter-upd-0",
        json=update_data,
        headers=service_headers,
    )
    assert response.status_code == 200
    assert response.json()["planned_length"] == 123


def test_update_encounter_by_identifier_not_found(service_headers):
    update_data = {"planned_length": 456}
    response = client.patch(
        "/api/v1/encounter?identifier=https://www.non-existent-system.com|non-existent-value",
        json=update_data,
        headers=service_headers,
    )
    assert response.status_code == 404


def test_update_encounter_by_identifier_multiple_matches(
    create_patient, service_headers
):
    identifier_dict = Identifier(
        system="https://www.demo-akihospit.com",
        value="duplicate-identifier-patch",
    ).__dict__

    for _ in range(2):
        new_encounter = {
            "subject": create_patient["patient_id"],
            "service_provider": "demo-akihospit",
            "service_type": "demo-akihospit-emergency",
            "identifier": [identifier_dict],
        }
        response = client.post(
            "/api/v1/encounter", json=new_encounter, headers=service_headers
        )
        assert response.status_code == 200

    update_data = {"planned_length": 789}
    response = client.patch(
        f"/api/v1/encounter?identifier={identifier_dict['system']}|{identifier_dict['value']}",
        json=update_data,
        headers=service_headers,
    )
    assert response.status_code == 409


def test_list_encounters(create_encounter, service_headers):
    response = client.get("/api/v1/encounter", headers=service_headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    assert any(
        encounter["encounter_id"] == create_encounter["encounter_id"]
        for encounter in response.json()
    )


# --- API Tests (Moved from test_encounter_discharge.py) ---


def test_end_encounter_with_ongoing_activity(
    create_encounter, create_service_request_helper, service_headers
):
    encounter_id = create_encounter["encounter_id"]

    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "in-progress",
    )
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "completed",
    )

    end_time = int(datetime.now().timestamp() * 1000)
    response = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "actual_period": {
                "end": end_time,
            }
        },
        headers=service_headers,
    )
    assert response.status_code == 200
    assert response.json()["actual_period"]["end"] == end_time


def test_end_encounter_with_no_activities(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    end_time = int(datetime.now().timestamp() * 1000)
    response = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "actual_period": {
                "end": end_time,
            }
        },
        headers=service_headers,
    )
    assert response.status_code == 200
    assert response.json()["actual_period"]["end"] == end_time


def test_add_discharge_to_ongoing_encounter(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    discharge_instructions = [{"id": "TC", "name": "Head Trauma Instructions"}]

    response = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "discharge": {
                "disposition": "home",
                "instructions": discharge_instructions,
            }
        },
        headers=service_headers,
    )
    assert response.status_code == 200
    assert response.json()["discharge"]["disposition"] == "home"
    assert response.json()["discharge"]["instructions"] == discharge_instructions


def test_add_discharge_after_encounter_end(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]

    # First end the encounter
    end_time = int(datetime.now().timestamp() * 1000)
    response_end = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "actual_period": {
                "end": end_time,
            }
        },
        headers=service_headers,
    )
    assert response_end.status_code == 200

    # Then add discharge information
    discharge_instructions = [
        {"id": "discharge-2", "name": "Follow up with specialist in 2 weeks"}
    ]
    response_discharge = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "discharge": {
                "disposition": "home",
                "destination": None,
                "instructions": discharge_instructions,
            }
        },
        headers=service_headers,
    )
    assert response_discharge.status_code == 200
    assert response_discharge.json()["discharge"]["disposition"] == "home"
    assert (
        response_discharge.json()["discharge"]["instructions"] == discharge_instructions
    )


def test_update_discharge_multiple_times(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]

    # Initial discharge
    initial_instructions = [{"id": "TC", "name": "Head Trauma Instructions"}]
    response_initial = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "discharge": {
                "disposition": "home",
                "instructions": initial_instructions,
            }
        },
        headers=service_headers,
    )
    assert response_initial.status_code == 200

    # Update discharge with destination
    response_dest = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "discharge": {
                "disposition": "same-hospital",
                "destination": {
                    "main_destination": "Cardiology Department",
                    "details": "Room 424",
                },
                "instructions": initial_instructions,  # Keep initial instructions
            }
        },
        headers=service_headers,
    )
    assert response_dest.status_code == 200
    assert response_dest.json()["discharge"]["disposition"] == "same-hospital"
    assert (
        response_dest.json()["discharge"]["destination"]["main_destination"]
        == "Cardiology Department"
    )

    # Update discharge instructions (append)
    updated_instructions = [{"id": "Platre", "name": "Cast Care Instructions"}]
    response_instr = client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "discharge": {
                # No need to resend disposition/destination if only updating instructions
                "instructions": updated_instructions,
            }
        },
        headers=service_headers,
    )
    assert response_instr.status_code == 200
    # Check that instructions were appended due to merge logic
    assert len(response_instr.json()["discharge"]["instructions"]) == 2
    assert {"TC", "Platre"} == {
        instr["id"] for instr in response_instr.json()["discharge"]["instructions"]
    }
    # Check that disposition/destination were preserved
    assert response_instr.json()["discharge"]["disposition"] == "same-hospital"
    assert (
        response_instr.json()["discharge"]["destination"]["main_destination"]
        == "Cardiology Department"
    )


def test_get_encounter_with_discharge(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]

    # Add discharge information
    discharge_instructions = [{"id": "discharge-5", "name": "Test instructions"}]
    client.patch(
        f"/api/v1/encounter/{encounter_id}",
        json={
            "discharge": {
                "disposition": "home",
                "instructions": discharge_instructions,
            }
        },
        headers=service_headers,
    )

    # Get encounter and verify discharge information
    response = client.get(f"/api/v1/encounter/{encounter_id}", headers=service_headers)
    assert response.status_code == 200
    assert response.json()["discharge"]["disposition"] == "home"
    assert response.json()["discharge"]["instructions"] == discharge_instructions
