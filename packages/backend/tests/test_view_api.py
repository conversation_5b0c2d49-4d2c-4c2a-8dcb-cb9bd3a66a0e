import pytest
from fastapi.testclient import TestClient
from packages.backend.server.main import app
from datetime import datetime, timedelta, timezone

client = TestClient(app)

# --- Fixtures (from test_relative_login.py, needed for view test) ---


@pytest.fixture
def relative_headers(relative_login_token):
    # Renamed from 'headers' to be more specific
    return {"Authorization": f"Bearer {relative_login_token}"}


# --- View API Tests (Moved from test_patient_login.py) ---


def test_patient_view_after_encounter(patient_login_token, create_encounter):
    headers = {"Authorization": f"Bearer {patient_login_token}"}
    response = client.get("/api/v1/view", headers=headers)
    assert response.status_code == 200
    assert "encounter" in response.json(), "Response should contain encounter data"
    assert (
        response.json()["encounter"]["encounter_id"] == create_encounter["encounter_id"]
    )


def test_patient_view_after_encounter_edit(
    patient_login_token, create_encounter, service_headers
):
    patient_headers = {"Authorization": f"Bearer {patient_login_token}"}

    # Create service request using service headers
    service_request_data = {
        "encounter_id": create_encounter["encounter_id"],
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "not-yet-started",
        "encounter_timing": {"planned_end_date": 0, "wait_count": 0},
    }
    response_create = client.post(
        "/api/v1/service_request",
        json=service_request_data,
        headers=service_headers,
    )
    assert response_create.status_code == 200
    assert "service_request_id" in response_create.json(), (
        "Response should contain the created service request ID"
    )

    # Verify the created service request (using real patient token)
    response_view1 = client.get("/api/v1/view", headers=patient_headers)
    assert response_view1.status_code == 200
    assert "service_request" in response_view1.json(), (
        "Response should contain service request data"
    )
    assert response_view1.json()["service_request"][0]["status"] == "not-yet-started"

    # Update the service request status using service headers
    # Get the original data and update the status
    update_data = service_request_data.copy()  # Start with original data
    update_data["status"] = "in-progress"
    # Note: Service Request update uses POST, not PATCH
    response_update = client.post(
        "/api/v1/service_request",
        json=update_data,
        headers=service_headers,
    )
    assert response_update.status_code == 200

    # Verify the updated service request (using real patient token)
    response_view2 = client.get("/api/v1/view", headers=patient_headers)
    assert response_view2.status_code == 200
    assert response_view2.json()["service_request"][0]["status"] == "in-progress"


@pytest.mark.parametrize(
    "invalid_token",
    [
        "",
        "invalid_token",
        "Bearer invalid_token",
    ],
)
def test_patient_view_invalid_token(invalid_token):
    headers = {"Authorization": invalid_token}
    response = client.get("/api/v1/view", headers=headers)
    assert response.status_code in [401, 403], "Invalid token should return 401 or 403"


# --- View API Tests (Moved from test_relative_login.py) ---


def test_relative_view_success(relative_headers, create_patient, create_encounter):
    # Uses the renamed fixture 'relative_headers'
    response = client.get("/api/v1/view", headers=relative_headers)
    assert response.status_code == 200, "Response should be 200"
    assert "service_request" in response.json(), (
        "Response should contain service_request"
    )
    # Basic check: ensure encounter details are present for the relative
    # Relative view uses share_id in the encounter_id field
    assert response.json()["encounter"]["encounter_id"] == create_encounter["share_id"]
    assert response.json()["encounter"]["subject"] == create_patient["patient_id"]


def test_relative_unable_view_non_sharable_activity(
    patient_login_token,
    relative_headers,
    create_patient,
    create_encounter,
    service_headers,
):
    patient_headers = {"Authorization": f"Bearer {patient_login_token}"}

    # Create two service requests (one sharable, one not)
    service_requests_data = [
        {
            "encounter_id": create_encounter["encounter_id"],
            "activity_definition_id": "demo-akihospit-emergency-activity-ioa",  # Sharable
            "status": "not-yet-started",
            "encounter_timing": {"planned_end_date": 0, "wait_count": 0},
        },
        {
            "encounter_id": create_encounter["encounter_id"],
            "activity_definition_id": "demo-akihospit-emergency-activity-cardiac-arrest",  # Not Sharable
            "status": "in-progress",
            "encounter_timing": {"planned_end_date": 0, "wait_count": 0},
        },
    ]

    created_sr_ids = []
    for request_data in service_requests_data:
        response = client.post(
            "/api/v1/service_request", json=request_data, headers=service_headers
        )
        assert response.status_code == 200, (
            f"Failed to create service request: {response.json()}"
        )
        created_sr_ids.append(response.json()["service_request_id"])

    # Test patient view (should see both)
    patient_response = client.get("/api/v1/view", headers=patient_headers)
    assert patient_response.status_code == 200
    patient_view_data = patient_response.json()
    assert len(patient_view_data["service_request"]) == 2, (
        "Patient should see 2 service requests"
    )
    patient_sr_ids = {
        sr["service_request_id"] for sr in patient_view_data["service_request"]
    }
    assert patient_sr_ids == set(created_sr_ids)

    # Test relative view (should see only the sharable one)
    relative_response = client.get("/api/v1/view", headers=relative_headers)
    assert relative_response.status_code == 200
    relative_view_data = relative_response.json()
    assert len(relative_view_data["service_request"]) == 1, (
        "Relative should see only 1 service request"
    )
    relative_visible_sr = relative_view_data["service_request"][0]
    assert (
        relative_visible_sr["activity_definition_id"]
        == "demo-akihospit-emergency-activity-ioa"
    ), "Relative should only see the sharable IOA activity"


# --- Session Timeout Tests ---


@pytest.mark.asyncio  # Ensure you have pytest-asyncio installed
async def test_patient_view_fails_after_session_timeout(
    patient_login_token,  # Use the original patient login token
    create_encounter,  # Fixture to create the encounter
    backend_module,  # Use the main backend module fixture
):
    # --- 1. Simulate Discharge by setting actual_period.end ---
    # Access repositories via the backend_module (call properties)
    encounter_repository = backend_module.encounter_repository()
    organization_repository = backend_module.organization_repository()

    # Get the created encounter
    encounter_id = create_encounter["encounter_id"]
    encounter = await encounter_repository.get_encounter_by_id(encounter_id)
    assert encounter is not None

    # Get the organization to find the timeout period
    org_id = encounter.service_provider
    organization = await organization_repository.get_organization_by_id(org_id)
    timeout_minutes = organization.patient_session_timeout_minutes

    # Set discharge time in the past, such that timeout_minutes + 1 have passed
    discharge_time_past = datetime.now(timezone.utc) - timedelta(
        minutes=timeout_minutes + 1
    )
    discharge_time_ms = int(discharge_time_past.timestamp() * 1000)

    # Update encounter in the repository
    encounter.actual_period.end = discharge_time_ms
    await encounter_repository.save_encounter(encounter)

    # --- 2. Attempt to access /view with the patient token ---
    headers = {"Authorization": f"Bearer {patient_login_token}"}
    response = client.get("/api/v1/view", headers=headers)

    # --- 3. Assert failure due to expired session ---
    assert response.status_code == 401, "Session should have expired"
    assert "expired due to discharge" in response.json()["detail"], (
        "Error detail should mention discharge expiration"
    )


@pytest.mark.asyncio
async def test_relative_view_fails_after_session_timeout(
    relative_headers,  # Use relative headers which contain the share_id token
    create_encounter,  # Fixture to create the encounter and share_id
    backend_module,  # Use the main backend module fixture
):
    # --- 1. Simulate Discharge ---
    # Access repositories via the backend_module (call properties)
    encounter_repository = backend_module.encounter_repository()
    organization_repository = backend_module.organization_repository()

    # Use share_id to get the encounter from the token's perspective
    # But we need the *actual* encounter_id to update the DB record
    encounter_id = create_encounter["encounter_id"]
    encounter = await encounter_repository.get_encounter_by_id(encounter_id)
    assert encounter is not None

    org_id = encounter.service_provider
    organization = await organization_repository.get_organization_by_id(org_id)
    timeout_minutes = organization.patient_session_timeout_minutes

    discharge_time_past = datetime.now(timezone.utc) - timedelta(
        minutes=timeout_minutes + 1
    )
    discharge_time_ms = int(discharge_time_past.timestamp() * 1000)

    encounter.actual_period.end = discharge_time_ms
    await encounter_repository.save_encounter(encounter)

    # --- 2. Attempt to access /view with the relative token ---
    # relative_headers already contains the correct token (bearer share_id)
    response = client.get("/api/v1/view", headers=relative_headers)

    # --- 3. Assert failure ---
    assert response.status_code == 401, "Session should have expired for relative"
    assert "expired due to discharge" in response.json()["detail"], (
        "Error detail should mention discharge expiration for relative"
    )
