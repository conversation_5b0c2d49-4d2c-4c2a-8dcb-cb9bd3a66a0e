from fastapi.testclient import TestClient
from packages.backend.server.main import app

client = TestClient(app)

# --- Healthcare Service API Tests (Moved from test_healthcare_service.py) ---


def test_get_healthcare_service_by_id(service_headers):
    """Test retrieving healthcare services for a specific known ID."""
    # Using a known valid service ID from demo data
    known_service_id = "demo-akihospit-emergency"
    response = client.get(
        f"/api/v1/healthcare_service/{known_service_id}",
        headers=service_headers,
    )
    assert response.status_code == 200
    services = response.json()
    assert isinstance(services, list)

    # If the service exists, the list should not be empty and have the correct structure
    if len(services) > 0:
        # Check structure of the first returned service (assuming ID maps uniquely or first is representative)
        service = services[0]
        assert "healthcare_service_id" in service
        assert service["healthcare_service_id"] == known_service_id  # Verify ID match
        assert "name" in service
        assert "organization_id" in service
    # else: The service ID might not exist in current test data,
    # but getting 200 with empty list is acceptable based on other tests.


def test_get_unknown_healthcare_service_by_id(service_headers):
    """Test retrieving a non-existent healthcare service by ID."""
    response = client.get(
        "/api/v1/healthcare_service/unknown-service-id",
        headers=service_headers,
    )
    # Endpoint returns 200 with empty list for unknown ID, not 404
    assert response.status_code == 200
    assert response.json() == []


# --- Discharge Instruction (Document Reference) API Tests ---
# Note: These might be better suited for a separate test_document_reference_api.py
# if the API surface grows, but keeping them here for now as they relate to HS.


def test_get_discharge_instructions_for_service():
    """Test retrieving discharge instructions linked to a healthcare service."""
    # Assuming 'demo-akihospit-emergency' has associated instructions
    response = client.get(
        "/api/v1/document_reference?healthcare_service_id=demo-akihospit-emergency"
    )
    assert response.status_code == 200
    instructions = response.json()
    assert isinstance(instructions, list)
    # Check structure if instructions exist
    if instructions:
        for instruction in instructions:
            assert "id" in instruction
            assert "description" in instruction  # Checking description field
            assert "content" in instruction
            assert isinstance(instruction["content"], list)
            assert len(instruction["content"]) > 0
            assert "attachment" in instruction["content"][0]
            assert "id" in instruction["content"][0]["attachment"]
            assert "name" in instruction["content"][0]["attachment"]


def test_get_discharge_instruction_file_by_id():
    """Test retrieving the actual file content of a discharge instruction."""
    # Hardcoding a known instruction ID from the demo data for chu-limoges
    known_instruction_attachment_id = "Platre"
    response = client.get(
        f"/api/v1/document_reference/{known_instruction_attachment_id}.pdf"
    )
    assert response.status_code == 200
    # Check Content-Type header for PDF
    assert response.headers["content-type"] == "application/pdf"
    # Check that response body is not empty
    assert response.content


def test_get_nonexistent_discharge_instruction_file():
    """Test retrieving a non-existent discharge instruction file."""
    response = client.get("/api/v1/document_reference/nonexistent-doc-id")
    assert response.status_code == 404
    # Update expected detail message based on actual API response
    assert response.json() == {"detail": "Discharge instruction file not found"}
