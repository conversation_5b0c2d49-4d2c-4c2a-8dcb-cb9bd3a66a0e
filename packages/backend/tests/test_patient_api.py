import pytest
from fastapi.testclient import TestClient
from packages.backend.server.main import app


client = TestClient(app)

# --- Fixtures (Copied from original test_patient.py) ---


@pytest.fixture
def valid_patient_payload():
    return {
        "name": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>"},
        "birth_date": "1990-01-15",
    }


@pytest.fixture
def patient_payload_with_phone(valid_patient_payload):
    payload = valid_patient_payload.copy()
    payload["phone"] = "+33612345678"
    return payload


@pytest.fixture
def patient_payload_with_contacts(patient_payload_with_phone):
    payload = patient_payload_with_phone.copy()
    payload["contact"] = [
        {
            "relationship": ["relative"],
            "telecom": [{"system": "sms", "value": "+33787654321"}],
        },
        {
            # No relationship field
            "telecom": [
                {"system": "sms", "value": "+33698765432"},
            ]
        },
    ]
    return payload


# Fixture needed for test_get_patient_by_id (originally used create_patient_with_contacts)
@pytest.fixture
def create_patient_with_contacts_for_api_test(
    patient_payload_with_contacts, service_headers
):
    response = client.post(
        "/api/v1/patient", json=patient_payload_with_contacts, headers=service_headers
    )
    assert response.status_code == 200
    return response.json()


# --- API Tests (Moved from original test_patient.py) ---


def test_create_patient_minimal(valid_patient_payload, service_headers):
    response = client.post(
        "/api/v1/patient", json=valid_patient_payload, headers=service_headers
    )
    assert response.status_code == 200, (
        f"Expected 200 OK, got {response.status_code} - {response.text}"
    )
    created_patient = response.json()
    assert "patient_id" in created_patient
    assert created_patient["name"] == valid_patient_payload["name"]
    assert created_patient["birth_date"] == valid_patient_payload["birth_date"]
    assert created_patient.get("phone") is None
    assert created_patient.get("contact") == []


def test_create_patient_with_phone(patient_payload_with_phone, service_headers):
    response = client.post(
        "/api/v1/patient", json=patient_payload_with_phone, headers=service_headers
    )
    assert response.status_code == 200, (
        f"Expected 200 OK, got {response.status_code} - {response.text}"
    )
    created_patient = response.json()
    assert created_patient.get("phone") == patient_payload_with_phone["phone"]
    assert created_patient.get("contact") == []


def test_create_patient_with_contacts(patient_payload_with_contacts, service_headers):
    response = client.post(
        "/api/v1/patient", json=patient_payload_with_contacts, headers=service_headers
    )
    assert response.status_code == 200, (
        f"Expected 200 OK, got {response.status_code} - {response.text}"
    )
    created_patient = response.json()
    assert created_patient.get("phone") == patient_payload_with_contacts["phone"]
    assert len(created_patient.get("contact", [])) == len(
        patient_payload_with_contacts["contact"]
    )
    # Check one contact structure
    if created_patient.get("contact"):
        contact1 = created_patient["contact"][0]
        # relationship may be missing or present
        assert "telecom" in contact1
        assert len(contact1["telecom"]) >= 1
        assert contact1["telecom"][0]["system"] == "sms"
        # Check the value matches one of the inputs
        assert contact1["telecom"][0]["value"] in [
            tc["value"]
            for c in patient_payload_with_contacts["contact"]
            for tc in c["telecom"]
        ]


def test_get_patient_by_id(create_patient_with_contacts_for_api_test, service_headers):
    # Renamed fixture usage to match the one defined in this file
    patient_id = create_patient_with_contacts_for_api_test["patient_id"]
    response = client.get(f"/api/v1/patient/{patient_id}", headers=service_headers)
    assert response.status_code == 200, "Response should be 200 OK"
    fetched_patient = response.json()
    assert fetched_patient["patient_id"] == patient_id
    assert fetched_patient["name"] == create_patient_with_contacts_for_api_test["name"]
    assert fetched_patient.get(
        "phone"
    ) == create_patient_with_contacts_for_api_test.get("phone")
    assert len(fetched_patient.get("contact", [])) == len(
        create_patient_with_contacts_for_api_test.get("contact", [])
    )


def test_get_unknown_patient_by_id(service_headers):
    response = client.get("/api/v1/patient/unknown_patient_id", headers=service_headers)
    assert response.status_code == 404, "Response should be 404 Not Found"
    assert "detail" in response.json(), "Response should contain error detail"
