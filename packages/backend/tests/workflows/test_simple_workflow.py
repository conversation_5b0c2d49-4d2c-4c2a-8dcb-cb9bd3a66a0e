import pytest
from fastapi.testclient import TestClient
from packages.backend.server.main import app

client = TestClient(app)


# Helper function to create service requests (can be moved to conftest if reused)
def create_service_request_helper(
    encounter_id, activity_id, status, wait_count=0, wait_time=0, headers=None
):
    response = client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": activity_id,
            "status": status,
            "encounter_timing": {
                "planned_end_date": wait_time,
                "wait_count": wait_count,
            },
        },
        headers=headers,
    )
    assert response.status_code == 200, (
        f"Failed to create SR: {activity_id} {status}. Status: {response.status_code}, Response: {response.text}"
    )
    return response.json()


# Helper function to check the /view endpoint
def check_view(headers, expected_activity_id, expected_status, expected_timing=None):
    response = client.get("/api/v1/view", headers=headers)
    assert response.status_code == 200, (
        f"View Check Failed: Expected 200, got {response.status_code}, Response: {response.text}"
    )

    actual_requests = response.json().get("service_request", [])

    # Find the *latest* request matching the activity_id
    matching_requests = [
        req
        for req in actual_requests
        if req.get("activity_definition_id") == expected_activity_id
    ]

    assert len(matching_requests) > 0, (
        f"Expected request {expected_activity_id} not found in view. Found: {actual_requests}"
    )

    # Check the most recent matching request
    target_request = matching_requests[-1]

    assert target_request.get("status") == expected_status, (
        f"Status Mismatch for {expected_activity_id}: Expected {expected_status}, Got {target_request.get('status')}"
    )

    if expected_timing:
        actual_timing = target_request.get("encounter_timing", {})
        for timing_key, timing_value in expected_timing.items():
            assert actual_timing.get(timing_key) == timing_value, (
                f"Timing Mismatch for {expected_activity_id} ({timing_key}): Expected {timing_value}, Got {actual_timing.get(timing_key)}"
            )


def test_simple_ioa_medical_care_workflow(
    patient_login_token,
    relative_login_token,
    create_encounter,  # Fixture from conftest
    service_headers,  # Fixture from conftest
):
    """Simulates a simple workflow: IOA -> Medical Care -> Completed."""
    headers_patient = {"Authorization": f"Bearer {patient_login_token}"}
    headers_relative = {"Authorization": f"Bearer {relative_login_token}"}
    encounter_id = create_encounter["encounter_id"]

    # Step 1: IOA Created
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        10,
        20,
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        {"wait_count": 10, "planned_end_date": 20},
    )
    check_view(
        headers_relative,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        {"wait_count": 10, "planned_end_date": 20},
    )

    # Step 2: IOA In Progress
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "in-progress",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-ioa", "in-progress")
    check_view(headers_relative, "demo-akihospit-emergency-activity-ioa", "in-progress")

    # Step 3: IOA Completed, Medical Care Created
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "completed",
        headers=service_headers,
    )
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-ioa", "completed")
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
    )
    check_view(headers_relative, "demo-akihospit-emergency-activity-ioa", "completed")
    # Check relative view for medical care (might not be visible yet)

    # Step 4: Medical Care In Progress
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "in-progress",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-medical-care", "in-progress"
    )
    # Check relative view

    # Step 5: Medical Care Completed
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "completed",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-medical-care", "completed"
    )
    # Check relative view (should likely see completed medical care)
    # Assuming medical care becomes sharable on completion based on current rules
    check_view(
        headers_relative, "demo-akihospit-emergency-activity-medical-care", "completed"
    )
