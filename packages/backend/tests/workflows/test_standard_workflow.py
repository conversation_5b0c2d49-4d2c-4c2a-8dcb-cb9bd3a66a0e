import logging
import pytest
from fastapi.testclient import TestClient
from packages.backend.impl.services.NotificationService import NotificationService
from packages.backend.server.main import app

client = TestClient(app)


# Helper function to create service requests (can be moved to conftest if reused)
def create_service_request_helper(
    encounter_id, activity_id, status, wait_count=0, wait_time=0, headers=None
):
    response = client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": activity_id,
            "status": status,
            "encounter_timing": {
                "planned_end_date": wait_time,
                "wait_count": wait_count,
            },
        },
        headers=headers,
    )
    assert response.status_code == 200, (
        f"Failed to create SR: {activity_id} {status}. Status: {response.status_code}, Response: {response.text}"
    )
    return response.json()


# Helper function to check the /view endpoint
def check_view(headers, expected_activity_id, expected_status, expected_timing=None):
    response = client.get("/api/v1/view", headers=headers)
    assert response.status_code == 200, (
        f"View Check Failed: Expected 200, got {response.status_code}, Response: {response.text}"
    )

    actual_requests = response.json().get("service_request", [])

    # Find the *latest* request matching the activity_id
    matching_requests = [
        req
        for req in actual_requests
        if req.get("activity_definition_id") == expected_activity_id
    ]

    assert len(matching_requests) > 0, (
        f"Expected request {expected_activity_id} not found in view. Found: {actual_requests}"
    )

    # Check the most recent matching request
    target_request = matching_requests[-1]

    assert target_request.get("status") == expected_status, (
        f"Status Mismatch for {expected_activity_id}: Expected {expected_status}, Got {target_request.get('status')}"
    )

    if expected_timing:
        actual_timing = target_request.get("encounter_timing", {})
        for timing_key, timing_value in expected_timing.items():
            assert actual_timing.get(timing_key) == timing_value, (
                f"Timing Mismatch for {expected_activity_id} ({timing_key}): Expected {timing_value}, Got {actual_timing.get(timing_key)}"
            )


# Main complex workflow test (original test_patient_encounter)
def test_standard_patient_encounter_workflow(
    patient_login_token,
    relative_login_token,
    create_patient,  # Fixture from conftest
    create_encounter,  # Fixture from conftest
    service_headers,  # Fixture from conftest
    monkeypatch,
):
    """Simulates a standard patient encounter workflow with multiple steps and checks."""

    sent_texts = []

    async def mock_send_sms(self, phone, content):
        """Mock SMS sending to track messages."""
        logging.info(f"Mock SMS to {phone}: {content}")
        sent_texts.append((phone, content))

    monkeypatch.setattr(NotificationService, "send_sms", mock_send_sms)

    headers_patient = {"Authorization": f"Bearer {patient_login_token}"}
    headers_relative = {"Authorization": f"Bearer {relative_login_token}"}
    encounter_id = create_encounter["encounter_id"]
    patient_phone = create_patient.get("phone")  # Get patient phone if available

    # --- Step 1: Initial state ---
    response = client.get("/api/v1/view", headers=headers_patient)
    assert response.status_code == 200
    assert len(response.json().get("service_request", [])) == 0, (
        "Initial view should have no service requests"
    )

    # --- Step 2: IOA Created ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        10,
        20,
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        {"wait_count": 10, "planned_end_date": 20},
    )
    check_view(
        headers_relative,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        {"wait_count": 10, "planned_end_date": 20},
    )

    # --- Step 3: IOA Timing Updated ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        5,
        10,
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        {"wait_count": 5, "planned_end_date": 10},
    )
    check_view(
        headers_relative,
        "demo-akihospit-emergency-activity-ioa",
        "not-yet-started",
        {"wait_count": 5, "planned_end_date": 10},
    )

    # --- Step 4: Medical Care Created ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
    )
    # Medical care might not be sharable initially, let's assume it's not for relative view yet

    # --- Step 5: IOA In Progress ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "in-progress",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-ioa", "in-progress")
    check_view(headers_relative, "demo-akihospit-emergency-activity-ioa", "in-progress")

    # --- Step 6: IOA Completed, Medical Care Timing Updated ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "completed",
        headers=service_headers,
    )
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        50,
        60,
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-ioa", "completed")
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        {"wait_count": 50, "planned_end_date": 60},
    )
    check_view(headers_relative, "demo-akihospit-emergency-activity-ioa", "completed")
    # Check if relative sees medical care now (depends on share rules)

    # --- Step 7: Medical Care In Progress, Timing Updated ---
    sent_texts.clear()  # Clear before potential notification
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "in-progress",
        20,
        20,
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-medical-care",
        "in-progress",
        {"wait_count": 20, "planned_end_date": 20},
    )
    # Check relative view

    # --- Step 8 & 9: XRay Created, Analysis Created ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray",
        "not-yet-started",
        headers=service_headers,
    )
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray-analysis",
        "waiting-for-results",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-xray", "not-yet-started"
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-xray-analysis",
        "waiting-for-results",
    )
    # Check relative view (XRay might be sharable)

    # --- Step 10: XRay In Progress ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray",
        "in-progress",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-xray", "in-progress")
    # Check relative view

    # --- Step 11: Cardiac Arrest (Non-Sharable) ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-cardiac-arrest",
        "in-progress",
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-cardiac-arrest",
        "in-progress",
    )
    # Check relative view explicitly DOES NOT contain cardiac arrest
    response_rel = client.get("/api/v1/view", headers=headers_relative)
    assert response_rel.status_code == 200
    assert not any(
        req.get("activity_definition_id")
        == "demo-akihospit-emergency-activity-cardiac-arrest"
        for req in response_rel.json().get("service_request", [])
    ), "Relative should NOT see Cardiac Arrest"

    # --- Step 12: XRay Analysis Cancelled ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray-analysis",
        "cancelled",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-xray-analysis", "cancelled"
    )
    # Check relative view (cancelled xray analysis might still be visible? depends on rules)

    # --- Step 13 & 14: Cardiologist Consult In Progress -> Completed ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-cardiologist-consult",
        "in-progress",
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-cardiologist-consult",
        "in-progress",
    )
    # Check relative view (Cardiologist consult likely not sharable)
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-cardiologist-consult",
        "completed",
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-cardiologist-consult",
        "completed",
    )
    # Check relative view

    # --- Step 15: Cardiac Arrest Completed ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-cardiac-arrest",
        "completed",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-cardiac-arrest", "completed"
    )
    # Relative still shouldn't see it
    response_rel = client.get("/api/v1/view", headers=headers_relative)
    assert not any(
        req.get("activity_definition_id")
        == "demo-akihospit-emergency-activity-cardiac-arrest"
        for req in response_rel.json().get("service_request", [])
    )

    # --- Step 16 & 17: New XRay and Analysis Created ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray",
        "not-yet-started",
        headers=service_headers,
    )
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray-analysis",
        "waiting-for-results",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-xray", "not-yet-started"
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-xray-analysis",
        "waiting-for-results",
    )
    # Check relative view

    # --- Step 18, 19, 20, 21: XRay -> In Progress -> Completed, Analysis -> In Progress -> Completed ---
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray",
        "in-progress",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-xray", "in-progress")
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray",
        "completed",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-xray", "completed")
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray-analysis",
        "in-progress",
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-xray-analysis",
        "in-progress",
    )
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-xray-analysis",
        "completed",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-xray-analysis", "completed"
    )
    # Check relative view - should see completed Xray analysis
    check_view(
        headers_relative, "demo-akihospit-emergency-activity-xray-analysis", "completed"
    )

    # --- Notification duplicate checks (from original test) ---
    sent_texts.clear()
    # Create medical care SR with wait_count=2 (triggers SMS)
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        2,
        5,
        headers=service_headers,
    )
    if patient_phone:
        initial_sms_count = 1
    else:
        initial_sms_count = 0
    assert len(sent_texts) == initial_sms_count, (
        f"Expected {initial_sms_count} SMS for wait_count=2"
    )

    # Create again with same wait_count (should NOT trigger SMS)
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        2,
        10,
        headers=service_headers,
    )
    assert len(sent_texts) == initial_sms_count, (
        "SMS count should not increase for same wait_count"
    )

    # Create again with status='not-yet-started' (should NOT trigger SMS as status already exists)
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-medical-care",
        "not-yet-started",
        0,
        0,
        headers=service_headers,
    )
    assert len(sent_texts) == initial_sms_count, (
        "SMS count should not increase for existing status"
    )

    if patient_phone:
        wait_count_message = "Vous allez voir le Docteur dans quelques minutes"
        assert any(content == wait_count_message for _, content in sent_texts), (
            f"Expected message '{wait_count_message}' not found in sent texts: {sent_texts}"
        )
