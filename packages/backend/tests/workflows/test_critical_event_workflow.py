import pytest
from fastapi.testclient import TestClient
from packages.backend.server.main import app

client = TestClient(app)


# Helper function to create service requests (can be moved to conftest if reused)
def create_service_request_helper(
    encounter_id, activity_id, status, wait_count=0, wait_time=0, headers=None
):
    response = client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": activity_id,
            "status": status,
            "encounter_timing": {
                "planned_end_date": wait_time,
                "wait_count": wait_count,
            },
        },
        headers=headers,
    )
    assert response.status_code == 200, (
        f"Failed to create SR: {activity_id} {status}. Status: {response.status_code}, Response: {response.text}"
    )
    return response.json()


# Helper function to check the /view endpoint
def check_view(headers, expected_activity_id, expected_status, expected_timing=None):
    response = client.get("/api/v1/view", headers=headers)
    assert response.status_code == 200, (
        f"View Check Failed: Expected 200, got {response.status_code}, Response: {response.text}"
    )

    actual_requests = response.json().get("service_request", [])

    # Find the *latest* request matching the activity_id
    matching_requests = [
        req
        for req in actual_requests
        if req.get("activity_definition_id") == expected_activity_id
    ]

    assert len(matching_requests) > 0, (
        f"Expected request {expected_activity_id} not found in view. Found: {actual_requests}"
    )

    # Check the most recent matching request
    target_request = matching_requests[-1]

    assert target_request.get("status") == expected_status, (
        f"Status Mismatch for {expected_activity_id}: Expected {expected_status}, Got {target_request.get('status')}"
    )

    if expected_timing:
        actual_timing = target_request.get("encounter_timing", {})
        for timing_key, timing_value in expected_timing.items():
            assert actual_timing.get(timing_key) == timing_value, (
                f"Timing Mismatch for {expected_activity_id} ({timing_key}): Expected {timing_value}, Got {actual_timing.get(timing_key)}"
            )


def test_critical_event_no_discharge_workflow(
    patient_login_token,
    relative_login_token,
    create_encounter,
    service_headers,
):
    """Simulates a critical event, encounter not explicitly discharged."""
    headers_patient = {"Authorization": f"Bearer {patient_login_token}"}
    headers_relative = {"Authorization": f"Bearer {relative_login_token}"}
    encounter_id = create_encounter["encounter_id"]

    # Step 1: IOA Completed
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-ioa",
        "completed",
        headers=service_headers,
    )
    check_view(headers_patient, "demo-akihospit-emergency-activity-ioa", "completed")

    # Step 2: Cardiac Arrest (Non-Sharable)
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-cardiac-arrest",
        "in-progress",
        headers=service_headers,
    )
    check_view(
        headers_patient,
        "demo-akihospit-emergency-activity-cardiac-arrest",
        "in-progress",
    )
    # Verify relative cannot see it
    response_rel = client.get("/api/v1/view", headers=headers_relative)
    assert not any(
        req.get("activity_definition_id")
        == "demo-akihospit-emergency-activity-cardiac-arrest"
        for req in response_rel.json().get("service_request", [])
    ), "Relative should NOT see Cardiac Arrest"

    # Step 3: Cardiac Arrest Completed
    create_service_request_helper(
        encounter_id,
        "demo-akihospit-emergency-activity-cardiac-arrest",
        "completed",
        headers=service_headers,
    )
    check_view(
        headers_patient, "demo-akihospit-emergency-activity-cardiac-arrest", "completed"
    )
    # Relative still should not see it
    response_rel = client.get("/api/v1/view", headers=headers_relative)
    assert not any(
        req.get("activity_definition_id")
        == "demo-akihospit-emergency-activity-cardiac-arrest"
        for req in response_rel.json().get("service_request", [])
    )

    # Step 4: Check Encounter status - Should still be 'in-progress' or similar, NOT 'finished'
    response_final_view = client.get("/api/v1/view", headers=headers_patient)
    assert response_final_view.status_code == 200
    final_encounter_data = response_final_view.json().get("encounter", {})
    assert final_encounter_data.get("status") != "finished"
    assert final_encounter_data.get("actual_period", {}).get("end") is None
    print(
        f"INFO: Encounter status after critical event: {final_encounter_data.get('status')}"
    )

    # Add assertions for the critical event creation if needed
    # For example, check if the service request was created with correct details
