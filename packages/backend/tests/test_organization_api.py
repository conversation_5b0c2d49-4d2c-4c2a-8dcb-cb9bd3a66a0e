from fastapi.testclient import TestClient
import pytest
from packages.backend.server.main import app

client = TestClient(app)


@pytest.fixture()
def get_first_organization(backend_module, service_headers):
    # Fixture to retrieve the first organization for subsequent tests
    response = client.get("/api/v1/organization", headers=service_headers)
    assert response.status_code == 200
    organizations = response.json()
    assert isinstance(organizations, list)
    assert len(organizations) > 0
    return organizations[0]


def test_list_organizations(backend_module, service_headers):
    """Test retrieving the list of all organizations."""
    response = client.get("/api/v1/organization", headers=service_headers)
    assert response.status_code == 200
    organizations = response.json()
    assert isinstance(organizations, list)
    assert len(organizations) > 0

    # Check structure of each returned organization
    for org in organizations:
        assert "organization_id" in org
        assert "name" in org
        assert isinstance(org["organization_id"], str)
        assert isinstance(org["name"], str)


def test_get_organization_by_id(
    backend_module, get_first_organization, service_headers
):
    """Test retrieving a specific organization by its ID."""
    org_id = get_first_organization["organization_id"]
    response = client.get(f"/api/v1/organization/{org_id}", headers=service_headers)
    assert response.status_code == 200
    org = response.json()
    assert org["organization_id"] == org_id
    assert "name" in org
    assert "description" in org  # Assuming description is always present


def test_get_unknown_organization_by_id(backend_module, service_headers):
    """Test retrieving a non-existent organization by ID."""
    response = client.get(
        "/api/v1/organization/unknown_organization_id", headers=service_headers
    )
    assert response.status_code == 404
    assert response.json() == {"detail": "Organization not found"}
