import pytest
from datetime import datetime, timedelta, timezone
from fastapi.testclient import Test<PERSON>lient
import json

from packages.backend.impl.module import BackendModule
from packages.backend.models.event import (
    Event,
    EventType,
    TextMessage,
    ConnexionAttemptDetails,
    TextType,
)
from packages.backend.models.authentication import Role
from packages.backend.models.encounter import EncounterId
from packages.backend.models.patient import PatientId
from packages.backend.models.common import UnixTimestamp
from packages.backend.server.main import app


client = TestClient(app)

# Test cases will go here


@pytest.mark.asyncio
async def test_encounter_created_event_logged(
    backend_module: BackendModule, create_encounter: dict
) -> None:
    """Tests that ENCOUNTER_CREATED event is logged by the service upon encounter creation."""
    event_repo = backend_module.event_repository()
    encounter_id = EncounterId(create_encounter["encounter_id"])
    patient_id = PatientId(create_encounter["subject"])

    # Retrieve the event logged during fixture execution
    events = await event_repo.list_events(
        encounter_id=encounter_id, event_type=EventType.ENCOUNTER_CREATED
    )

    assert len(events) >= 1  # Should be at least one from fixture creation

    # Find the specific event
    found_event = None
    for event in events:
        if event.encounter_id == encounter_id and event.patient_id == patient_id:
            found_event = event
            break

    assert found_event is not None, "ENCOUNTER_CREATED event not found"
    assert found_event.event_type == EventType.ENCOUNTER_CREATED
    assert found_event.timestamp is not None
    assert found_event.role is None
    assert found_event.text_message_details is None
    assert found_event.connexion_details is None


@pytest.mark.asyncio
async def test_login_sms_sent_event_logged(
    backend_module: BackendModule, create_patient: dict, create_encounter: dict
) -> None:
    """Tests that TEXT_MESSAGE_SENT (LOGIN_SMS) is logged by the service upon encounter creation for patient with phone."""
    event_repo = backend_module.event_repository()
    encounter_id = EncounterId(create_encounter["encounter_id"])
    patient_id = PatientId(create_encounter["subject"])

    # Ensure the patient fixture actually has a phone number for this test
    assert create_patient.get("phone") is not None, (
        "Test requires patient with a phone number"
    )
    expected_recipient = create_patient["phone"]

    # Retrieve the relevant event logged during fixture execution
    events = await event_repo.list_events(
        encounter_id=encounter_id, event_type=EventType.TEXT_MESSAGE_SENT
    )

    # Find the specific login SMS event for the patient
    login_sms_event = None
    for event in events:
        if event.text_message_details is None or event.role is None:
            continue
        if (
            event.text_message_details.recipient == expected_recipient
            and event.text_message_details.text_type == TextType.LOGIN_SMS
            and event.role == Role.PATIENT
        ):
            login_sms_event = event
            break

    assert login_sms_event is not None, "Login SMS event for patient not found"
    assert login_sms_event.text_message_details is not None
    assert login_sms_event.patient_id == patient_id
    assert "sign-in" in login_sms_event.text_message_details.text


@pytest.mark.asyncio
async def test_connexion_attempt_event_logged(
    backend_module: BackendModule,
    create_patient: dict,
    create_encounter: dict,
) -> None:
    """Tests that CONNEXION_ATTEMPT events are logged by the service during login API calls."""
    event_repo = backend_module.event_repository()
    encounter_id = EncounterId(create_encounter["encounter_id"])
    patient_id = PatientId(create_encounter["subject"])
    login_url = "/api/v1/login/access-token"
    correct_details = {
        "id": encounter_id,
        "truncated_last_name": create_patient["name"]["last_name"][:3].upper(),
        "birth_date": create_patient["birth_date"],
    }
    incorrect_details = {
        "id": encounter_id,
        "truncated_last_name": "XXX",  # Incorrect name
        "birth_date": create_patient["birth_date"],
    }

    # --- Perform Login Attempts using API client ---

    # Failed attempt
    response_fail = client.post(login_url, params=incorrect_details)
    assert response_fail.status_code == 401  # Expect failure

    # Successful attempt
    response_success = client.post(login_url, params=correct_details)
    assert response_success.status_code == 200  # Expect success

    # --- Verify Events ---
    events = await event_repo.list_events(
        encounter_id=encounter_id, event_type=EventType.CONNEXION_ATTEMPT
    )

    # Find the failed attempt event
    failed_event = None
    for event in events:
        if event.connexion_details is None or event.role is None:
            continue
        if (
            event.connexion_details.success is False
            and event.patient_id == patient_id
            and event.role == Role.PATIENT
        ):  # Assuming login via encounter_id is PATIENT role
            failed_event = event
            break

    assert failed_event is not None, "Failed connexion attempt event not found"
    assert failed_event.connexion_details is not None
    assert failed_event.connexion_details.failure_reason is not None

    # Find the successful attempt event
    successful_event = None
    for event in events:
        if event.connexion_details is None or event.role is None:
            continue
        if (
            event.connexion_details.success is True
            and event.patient_id == patient_id
            and event.role == Role.PATIENT
        ):
            successful_event = event
            break

    assert successful_event is not None, "Successful connexion attempt event not found"
    assert successful_event.connexion_details is not None
    assert successful_event.connexion_details.failure_reason is None


@pytest.mark.asyncio
async def test_list_events_filtering(backend_module: BackendModule) -> None:
    """Tests the filtering capabilities of list_events."""
    event_repo = backend_module.event_repository()
    # Define IDs
    enc_1 = EncounterId("enc-aaa")
    pat_1 = PatientId("pat-aaa")
    enc_2 = EncounterId("enc-bbb")
    pat_2 = PatientId("pat-bbb")

    # Define timestamps and dates
    now = datetime.now(timezone.utc)
    today = now.date()
    yesterday = today - timedelta(days=1)
    ts_yesterday = int((now - timedelta(days=1)).timestamp() * 1000)
    ts_today_1 = int(now.timestamp() * 1000)
    # Ensure the second timestamp today is distinct
    ts_today_2 = ts_today_1 + 100

    # --- Create events ---

    # Yesterday: enc_1, pat_1, ENCOUNTER_CREATED
    event_yest_1 = Event(
        timestamp=UnixTimestamp(ts_yesterday),
        event_type=EventType.ENCOUNTER_CREATED,
        encounter_id=enc_1,
        patient_id=pat_1,
    )
    await event_repo.put_event(event_yest_1)

    # Today: enc_1, pat_1, TEXT_MESSAGE_SENT (PATIENT)
    event_today_1 = Event(
        timestamp=UnixTimestamp(ts_today_1),
        event_type=EventType.TEXT_MESSAGE_SENT,
        encounter_id=enc_1,
        patient_id=pat_1,
        role=Role.PATIENT,
        text_message_details=TextMessage(
            text="Msg 1", recipient="rec1", text_type=TextType.LOGIN_SMS
        ),
    )
    await event_repo.put_event(event_today_1)

    # Today: enc_2, pat_2, CONNEXION_ATTEMPT (SERVICE - success)
    event_today_2 = Event(
        timestamp=UnixTimestamp(ts_today_2),
        event_type=EventType.CONNEXION_ATTEMPT,
        encounter_id=enc_2,
        patient_id=pat_2,
        role=Role.SERVICE,  # Using SERVICE role for variety
        connexion_details=ConnexionAttemptDetails(success=True),
    )
    await event_repo.put_event(event_today_2)

    # --- Test filtering ---

    # Filter by encounter_id=enc_1
    events_enc1 = await event_repo.list_events(encounter_id=enc_1)
    assert len(events_enc1) == 2
    assert event_today_1 in events_enc1  # Newest first
    assert event_yest_1 in events_enc1

    # Filter by patient_id=pat_2
    events_pat2 = await event_repo.list_events(patient_id=pat_2)
    assert len(events_pat2) == 1
    assert event_today_2 in events_pat2

    # Filter by event_type=ENCOUNTER_CREATED
    events_created = await event_repo.list_events(
        event_type=EventType.ENCOUNTER_CREATED
    )
    assert len(events_created) == 1
    assert event_yest_1 in events_created

    # Filter by date=today
    events_today = await event_repo.list_events(start_date=today, end_date=today)
    assert len(events_today) == 2
    assert event_today_2 in events_today  # Newest first
    assert event_today_1 in events_today

    # Filter by date=yesterday
    events_yesterday = await event_repo.list_events(
        start_date=yesterday, end_date=yesterday
    )
    assert len(events_yesterday) == 1
    assert event_yest_1 in events_yesterday

    # Filter by date range (both days)
    events_both_days = await event_repo.list_events(
        start_date=yesterday, end_date=today
    )
    assert len(events_both_days) == 3
    assert event_today_2 in events_both_days
    assert event_today_1 in events_both_days
    assert event_yest_1 in events_both_days

    # Filter by date=today AND event_type=CONNEXION_ATTEMPT
    events_today_conn = await event_repo.list_events(
        start_date=today, end_date=today, event_type=EventType.CONNEXION_ATTEMPT
    )
    assert len(events_today_conn) == 1
    assert event_today_2 in events_today_conn

    # Filter by date=today AND encounter_id=enc_1
    events_today_enc1 = await event_repo.list_events(
        start_date=today, end_date=today, encounter_id=enc_1
    )
    assert len(events_today_enc1) == 1
    assert event_today_1 in events_today_enc1

    # Filter by encounter_id=enc_1 AND event_type=TEXT_MESSAGE_SENT
    events_enc1_text = await event_repo.list_events(
        encounter_id=enc_1, event_type=EventType.TEXT_MESSAGE_SENT
    )
    assert len(events_enc1_text) == 1
    assert event_today_1 in events_enc1_text

    # Filter by non-existent encounter_id
    events_none = await event_repo.list_events(encounter_id=EncounterId("enc-xxx"))
    assert len(events_none) == 0

    # List all (no filters)
    events_all = await event_repo.list_events()
    assert len(events_all) == 3
    # Check default sort order (newest first)
    assert events_all[0] == event_today_2
    assert events_all[1] == event_today_1
    assert events_all[2] == event_yest_1


@pytest.mark.asyncio
async def test_log_login_page_viewed_via_route_patient_id_exists(
    backend_module: BackendModule,
    create_patient: dict,  # Used to create a valid encounter context
    create_encounter: dict,
) -> None:
    """Tests LOGIN_PAGE_VIEWED (unauth) when encounter_id (PATIENT format) exists."""
    event_repo = backend_module.event_repository()
    encounter_id_str = create_encounter["encounter_id"]  # e.g., "enc-something"
    expected_patient_id = PatientId(create_encounter["subject"])
    expected_role = Role.PATIENT

    api_url = "/api/v1/events/log/login_page_viewed"
    payload = {"encounter_id": encounter_id_str}
    response = client.post(
        api_url,
        content=json.dumps(payload),
        headers={"Content-Type": "application/json"},
    )
    assert response.status_code == 204, f"API call failed: {response.text}"

    events = await event_repo.list_events(
        encounter_id=EncounterId(encounter_id_str),
        event_type=EventType.LOGIN_PAGE_VIEWED,
    )
    logged_event = next(
        (e for e in reversed(events) if e.role == expected_role), None
    )  # Get latest for this role
    assert logged_event is not None, "Event not found or role mismatch"
    assert logged_event.patient_id == expected_patient_id
    assert logged_event.role == expected_role


@pytest.mark.asyncio
async def test_log_login_page_viewed_via_route_share_id_exists(
    backend_module: BackendModule,
    create_patient: dict,
    create_encounter: dict,  # Fixture creates an encounter with both enc- and SH- IDs
) -> None:
    """Tests LOGIN_PAGE_VIEWED (unauth) when encounter_id (SHARE ID format) exists."""
    event_repo = backend_module.event_repository()
    real_encounter_id = EncounterId(create_encounter["encounter_id"])
    encounter_obj = await backend_module.encounter_repository().get_encounter_by_id(
        real_encounter_id
    )
    share_id_str = str(
        encounter_obj.share_id
    )
    assert share_id_str.startswith("SH-"), "Fixture did not provide a valid ShareID"
    expected_patient_id = encounter_obj.subject
    expected_role = Role.RELATIVE

    api_url = "/api/v1/events/log/login_page_viewed"
    payload = {"encounter_id": share_id_str}
    response = client.post(
        api_url,
        content=json.dumps(payload),
        headers={"Content-Type": "application/json"},
    )
    assert response.status_code == 204, f"API call failed: {response.text}"

    # Event is logged with the resolved real_encounter_id
    events = await event_repo.list_events(
        encounter_id=real_encounter_id, event_type=EventType.LOGIN_PAGE_VIEWED
    )
    logged_event = next((e for e in reversed(events) if e.role == expected_role), None)
    assert logged_event is not None, "Event not found or role mismatch"
    assert logged_event.patient_id == expected_patient_id
    assert logged_event.role == expected_role


@pytest.mark.asyncio
async def test_log_login_page_viewed_via_route_id_not_exists(
    backend_module: BackendModule
) -> None:
    """Tests LOGIN_PAGE_VIEWED (unauth) when encounter_id (PATIENT format) DOES NOT exist."""
    event_repo = backend_module.event_repository()
    non_existent_enc_id = "enc-does-not-exist"
    expected_role = Role.PATIENT

    api_url = "/api/v1/events/log/login_page_viewed"
    payload = {"encounter_id": non_existent_enc_id}
    response = client.post(
        api_url,
        content=json.dumps(payload),
        headers={"Content-Type": "application/json"},
    )
    assert response.status_code == 204, f"API call failed: {response.text}"

    events = await event_repo.list_events(
        encounter_id=EncounterId(non_existent_enc_id),
        event_type=EventType.LOGIN_PAGE_VIEWED,
    )
    logged_event = next((e for e in reversed(events) if e.role == expected_role), None)
    assert logged_event is not None, "Event not found or role mismatch"
    assert logged_event.patient_id is None
    assert logged_event.role == expected_role


# --- ACCEPTED_TOS Tests (similar structure) ---
@pytest.mark.asyncio
async def test_log_accepted_tos_via_route_patient_id_exists(
    backend_module: BackendModule,
    create_patient: dict,
    create_encounter: dict,
) -> None:
    """Tests ACCEPTED_TOS (unauth) when encounter_id (PATIENT format) exists."""
    event_repo = backend_module.event_repository()
    encounter_id_str = create_encounter["encounter_id"]
    expected_patient_id = PatientId(create_encounter["subject"])
    expected_role = Role.PATIENT

    api_url = "/api/v1/events/log/accepted_tos"
    payload = {"encounter_id": encounter_id_str}
    response = client.post(
        api_url,
        content=json.dumps(payload),
        headers={"Content-Type": "application/json"},
    )
    assert response.status_code == 204, f"API call failed: {response.text}"

    events = await event_repo.list_events(
        encounter_id=EncounterId(encounter_id_str), event_type=EventType.ACCEPTED_TOS
    )
    logged_event = next((e for e in reversed(events) if e.role == expected_role), None)
    assert logged_event is not None, "Event not found or role mismatch"
    assert logged_event.patient_id == expected_patient_id
    assert logged_event.role == expected_role


@pytest.mark.asyncio
async def test_log_accepted_tos_via_route_share_id_exists(
    backend_module: BackendModule,
    create_patient: dict,
    create_encounter: dict,
) -> None:
    """Tests ACCEPTED_TOS (unauth) when encounter_id (SHARE ID format) exists."""
    event_repo = backend_module.event_repository()
    real_encounter_id = EncounterId(create_encounter["encounter_id"])
    encounter_obj = await backend_module.encounter_repository().get_encounter_by_id(
        real_encounter_id
    )
    share_id_str = str(encounter_obj.share_id)
    assert share_id_str.startswith("SH-"), "Fixture did not provide a valid ShareID"
    expected_patient_id = encounter_obj.subject
    expected_role = Role.RELATIVE

    api_url = "/api/v1/events/log/accepted_tos"
    payload = {"encounter_id": share_id_str}
    response = client.post(
        api_url,
        content=json.dumps(payload),
        headers={"Content-Type": "application/json"},
    )
    assert response.status_code == 204, f"API call failed: {response.text}"

    # Event is logged with the resolved real_encounter_id
    events = await event_repo.list_events(
        encounter_id=real_encounter_id, event_type=EventType.ACCEPTED_TOS
    )
    logged_event = next((e for e in reversed(events) if e.role == expected_role), None)
    assert logged_event is not None, "Event not found or role mismatch"
    assert logged_event.patient_id == expected_patient_id
    assert logged_event.role == expected_role


@pytest.mark.asyncio
async def test_log_accepted_tos_via_route_id_not_exists(
    backend_module: BackendModule
) -> None:
    """Tests ACCEPTED_TOS (unauth) when encounter_id (PATIENT format) DOES NOT exist."""
    event_repo = backend_module.event_repository()
    non_existent_enc_id = "enc-also-does-not-exist"
    expected_role = Role.PATIENT  # Still inferred as PATIENT due to non-SH format

    api_url = "/api/v1/events/log/accepted_tos"
    payload = {"encounter_id": non_existent_enc_id}
    response = client.post(
        api_url,
        content=json.dumps(payload),
        headers={"Content-Type": "application/json"},
    )
    assert response.status_code == 204, f"API call failed: {response.text}"

    events = await event_repo.list_events(
        encounter_id=EncounterId(non_existent_enc_id), event_type=EventType.ACCEPTED_TOS
    )
    logged_event = next((e for e in reversed(events) if e.role == expected_role), None)
    assert logged_event is not None, "Event not found or role mismatch"
    assert logged_event.patient_id is None
    assert logged_event.role == expected_role


@pytest.mark.asyncio
async def test_log_sent_share_link_via_route(
    backend_module: BackendModule,
    create_patient: dict,
    create_encounter: dict,
) -> None:
    """Tests logging SENT_SHARE_LINK event via the API route using an auth token."""
    event_repo = backend_module.event_repository()

    encounter_id_str = create_encounter["encounter_id"]
    expected_patient_id = PatientId(create_encounter["subject"])
    # Role depends on how login happens. Assuming Patient login here.
    expected_role = Role.PATIENT

    # 1. Obtain access token for the patient
    login_payload = {
        "id": encounter_id_str,
        "truncated_last_name": create_patient["name"]["last_name"][:3].upper(),
        "birth_date": create_patient["birth_date"],
    }
    login_url = "/api/v1/login/access-token"
    login_response = client.post(login_url, params=login_payload)
    assert login_response.status_code == 200, f"Failed to login: {login_response.text}"
    access_token = login_response.json()["access_token"]

    # 2. Call the event logging route
    api_url = "/api/v1/events/log/sent_share_link"
    headers = {"Authorization": f"Bearer {access_token}"}
    response = client.post(api_url, headers=headers)
    assert response.status_code == 204, f"API call failed: {response.text}"

    # 3. Verify the event
    events = await event_repo.list_events(
        encounter_id=EncounterId(encounter_id_str),
        event_type=EventType.SENT_SHARE_LINK,
    )
    logged_event = None
    for e in events:
        if (
            e.encounter_id == EncounterId(encounter_id_str)
            and e.event_type == EventType.SENT_SHARE_LINK
            and e.role == expected_role
        ):
            if logged_event is None or e.timestamp > logged_event.timestamp:
                logged_event = e

    assert logged_event is not None, "Event not found or attributes mismatch"
    assert logged_event.patient_id == expected_patient_id
    assert logged_event.role == expected_role
