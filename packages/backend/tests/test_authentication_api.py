import pytest
from fastapi.testclient import TestClient
from packages.backend.server.main import app

client = TestClient(app)

# --- Fixtures (from test_patient_login.py) ---


@pytest.fixture
def valid_patient_data(create_patient, create_encounter):
    # This fixture provides data needed specifically for patient login tests
    return {
        "id": create_encounter["encounter_id"],
        "truncated_last_name": create_patient["name"]["last_name"][:3],
        "birth_date": create_patient["birth_date"],
    }


# --- Patient Login API Tests (from test_patient_login.py) ---


def test_patient_login_success(patient_login_token):
    # Uses fixture from conftest.py
    assert patient_login_token, "Login token should be generated successfully"


def test_patient_login_nonexistent():
    response = client.post(
        "/api/v1/login/access-token",
        params={"id": 9999, "truncated_last_name": "Doe", "birth_date": "1990-01-01"},
    )
    assert response.status_code == 404
    assert "detail" in response.json(), "Error message should be provided"


def test_patient_login_wrong_last_name(valid_patient_data):
    invalid_data = valid_patient_data.copy()
    invalid_data["truncated_last_name"] = "Xyz"
    response = client.post("/api/v1/login/access-token", params=invalid_data)
    assert response.status_code == 401
    assert "detail" in response.json(), "Error message should be provided"


def test_patient_login_wrong_birthdate(valid_patient_data):
    invalid_data = valid_patient_data.copy()
    invalid_data["birth_date"] = "2420-01-01"
    response = client.post("/api/v1/login/access-token", params=invalid_data)
    assert response.status_code == 401
    assert "detail" in response.json(), "Error message should be provided"


@pytest.mark.parametrize(
    "field,value",
    [
        ("id", ""),
        ("truncated_last_name", ""),
        ("birth_date", "invalid-date"),
    ],
)
def test_patient_login_invalid_input(valid_patient_data, field, value):
    invalid_data = valid_patient_data.copy()
    invalid_data[field] = value
    response = client.post("/api/v1/login/access-token", params=invalid_data)
    # Expecting 401 (auth fail), 422 (validation error), or 404 (not found)
    assert response.status_code in [
        401,
        422,
        404,
    ], f"Invalid {field} should return 401, 422 or 404"


# --- Relative Login API Tests (from test_relative_login.py) ---


def test_relative_login_success(relative_login_token):
    # Uses fixture from conftest.py
    assert relative_login_token, "Relative login token should be generated"


# Note: View tests (`test_patient_view_...`, `test_relative_view_...`)
# have been moved to test_view_api.py
