import pytest
from packages.backend.models.patient import (
    <PERSON><PERSON><PERSON>,
    <PERSON>ient,
    PatientId,
    Contact,
    Telecom,
)
from packages.backend.models.common import ISODate

# --- Model Validation Tests (Moved from original test_patient.py) ---


def test_patient_model_validation():
    # Valid minimal patient
    Patient(
        patient_id=PatientId("p1"),
        name=<PERSON><PERSON><PERSON>("<PERSON>", "<PERSON>"),
        birth_date=ISODate("2000-02-29"),
    )

    # Valid patient with phone
    Patient(
        patient_id=PatientId("p2"),
        name=<PERSON><PERSON><PERSON>("<PERSON>", "<PERSON>"),
        birth_date=ISODate("2000-02-29"),
        phone="+33123456789",
    )

    # Valid patient with SMS contacts
    contact1 = Contact(telecom=[Telecom(system="sms", value="+33611223344")])
    Patient(
        patient_id=PatientId("p3"),
        name=<PERSON><PERSON><PERSON>("<PERSON>", "<PERSON>"),
        birth_date=ISODate("2000-02-29"),
        contact=[contact1],
    )

    # Invalid phone (patient level)
    with pytest.raises(AssertionError, match="Phone number must start with \\+33"):
        Patient(
            patient_id=<PERSON><PERSON>Id("p4"),
            name=<PERSON><PERSON><PERSON>("<PERSON>", "<PERSON>"),
            birth_date=ISODate("2000-02-29"),
            phone="**********",
        )

    # Invalid birth date format
    with pytest.raises(AssertionError, match="Birth date format should be YYYY-MM-DD"):
        Patient(
            patient_id=PatientId("p5"),
            name=HumanName("Jane", "Smith"),
            birth_date=ISODate("2000/02/29"),
        )
    with pytest.raises(
        AssertionError, match="Birth date month must be between 01 and 12"
    ):
        Patient(
            patient_id=PatientId("p6"),
            name=HumanName("Jane", "Smith"),
            birth_date=ISODate("2000-13-01"),
        )

    # Invalid contact (missing telecom)
    with pytest.raises(
        AssertionError, match="Contact must have at least one telecom entry"
    ):
        Contact(telecom=[])

    # Invalid telecom value (sms prefix)
    with pytest.raises(
        AssertionError, match="must be a valid phone number starting with \\+33"
    ):
        Contact(telecom=[Telecom(system="sms", value="**********")])

    # Invalid telecom system (Contact validation)
    telecom_with_wrong_system = Telecom(system="sms", value="+33612345678")
    telecom_with_wrong_system.system = "email"  # type: ignore[assignment]

    with pytest.raises(
        AssertionError, match="Only 'sms' telecom system is currently supported"
    ):
        Contact(telecom=[telecom_with_wrong_system])
