import random
import string
from fastapi.testclient import TestClient
from packages.backend.models.common import Identifier
from packages.backend.server.main import app
import pytest
import packages.backend.server.api.deps as deps

client = TestClient(app)


@pytest.fixture
def service_request_creator_func(create_encounter, service_headers):
    def _create_service_request(encounter_id=None, status="not-yet-started"):
        if encounter_id is None:
            encounter_id = create_encounter["encounter_id"]
        response = client.post(
            "/api/v1/service_request",
            json={
                "encounter_id": encounter_id,
                "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
                "status": status,
                "encounter_timing": {"planned_end_date": 0, "wait_count": 0},
            },
            headers=service_headers,
        )
        assert response.status_code == 200
        return response.json()

    return _create_service_request


@pytest.fixture(scope="function")
def service_request_fixture(create_encounter, service_headers):
    """Fixture to ensure a service request exists for testing GET/PATCH."""
    encounter_id = create_encounter["encounter_id"]
    payload = {
        "encounter_id": encounter_id,
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "not-yet-started",
        "encounter_timing": {"wait_count": 10, "planned_end_date": 1678886400},
    }
    response = client.post(
        "/api/v1/service_request",
        json=payload,
        headers=service_headers,
    )
    assert response.status_code == 200
    return response.json()


def test_create_service_request(service_request_creator_func):
    service_request = service_request_creator_func()
    assert "service_request_id" in service_request


def test_read_service_request(
    create_encounter, service_request_creator_func, service_headers
):
    service_request = service_request_creator_func()

    read_response = client.get(
        f"/api/v1/service_request?encounter_id={create_encounter['encounter_id']}",
        headers=service_headers,
    )

    assert read_response.status_code == 200
    assert isinstance(read_response.json(), list)
    assert len(read_response.json()) > 0
    retrieved_service_request = read_response.json()[0]
    assert (
        retrieved_service_request["service_request_id"]
        == service_request["service_request_id"]
    )
    assert retrieved_service_request["status"] == "not-yet-started"
    assert (
        retrieved_service_request["activity_definition_id"]
        == "demo-akihospit-emergency-activity-ioa"
    )


@pytest.fixture
def second_encounter_fixture(create_patient, service_headers):
    # Fixed fixture name and implementation
    return client.post(
        "/api/v1/encounter",
        json={
            "subject": create_patient["patient_id"],
            "service_provider": "demo-akihospit",
            "service_type": "demo-akihospit-emergency",
            "identifier": [
                Identifier(
                    system="https://www.demo-akihospit.com",
                    value="".join(
                        random.choices(string.ascii_letters + string.digits, k=10)
                    ),
                ).__dict__
            ],
        },
        headers=service_headers,
    ).json()


def test_multiple_service_requests(
    create_encounter,
    second_encounter_fixture,  # Use fixed fixture
    service_request_creator_func,  # Use creator func
    service_headers,
):
    encounter1 = create_encounter
    encounter2 = second_encounter_fixture  # Use fixed fixture directly

    sr1 = service_request_creator_func(encounter1["encounter_id"])["service_request_id"]
    sr2 = service_request_creator_func(encounter2["encounter_id"])["service_request_id"]

    for encounter, expected_sr in [(encounter1, sr1), (encounter2, sr2)]:
        response = client.get(
            f"/api/v1/service_request?encounter_id={encounter['encounter_id']}",
            headers=service_headers,
        )
        assert response.status_code == 200
        service_requests = response.json()

        assert len(service_requests) == 1
        assert service_requests[0]["service_request_id"] == expected_sr
        assert service_requests[0]["encounter"] == encounter["encounter_id"]
        assert (
            service_requests[0]["activity_definition_id"]
            == "demo-akihospit-emergency-activity-ioa"
        )


def test_get_service_request_by_encounter_identifier(
    create_encounter,
    service_request_creator_func,  # Use creator func
    service_headers,
):
    encounter = create_encounter
    service_request = service_request_creator_func(encounter["encounter_id"])

    identifier = (
        f"{encounter['identifier'][0]['system']}|{encounter['identifier'][0]['value']}"
    )

    response = client.get(
        f"/api/v1/service_request?identifier={identifier}",
        headers=service_headers,
    )
    assert response.status_code == 200
    retrieved_service_requests = response.json()

    assert len(retrieved_service_requests) == 1
    assert (
        retrieved_service_requests[0]["service_request_id"]
        == service_request["service_request_id"]
    )
    assert (
        retrieved_service_requests[0]["activity_definition_id"]
        == "demo-akihospit-emergency-activity-ioa"
    )
    assert retrieved_service_requests[0]["status"] == "not-yet-started"


def test_get_service_request_by_non_existent_identifier(service_headers):
    response = client.get(
        "/api/v1/service_request?identifier=non-existent-system|non-existent-value",
        headers=service_headers,
    )
    assert response.status_code == 404
    assert "No encounter found for identifier" in response.text


@pytest.mark.parametrize(
    "status", ["not-yet-started", "in-progress", "completed", "cancelled"]
)
def test_service_request_status(
    create_encounter,
    service_request_creator_func,  # Use creator func
    status,
    service_headers,
):
    service_request = service_request_creator_func(status=status)
    assert service_request["status"] == status

    read_response = client.get(
        f"/api/v1/service_request?encounter_id={create_encounter['encounter_id']}",
        headers=service_headers,
    )
    assert read_response.status_code == 200
    retrieved_service_request = read_response.json()[0]
    assert retrieved_service_request["status"] == status


def test_create_service_request_practitioner_auth(practitioner_headers):
    payload = {
        "encounter_id": "some-encounter-id",
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "not-yet-started",
        "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
    }
    headers = practitioner_headers  # Use the fixture directly
    response = client.post("/api/v1/service_request", json=payload, headers=headers)
    assert response.status_code == 401  # Changed from 403 (SERVICE only)


# --- GET /service_request Tests ---


def test_list_service_requests_success(
    service_request_fixture,
    practitioner_headers,  # Use simple fixture
):
    encounter_id = service_request_fixture["encounter"]
    headers = practitioner_headers
    response = client.get(
        f"/api/v1/service_request?encounter_id={encounter_id}", headers=headers
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    retrieved_service_request = response.json()[0]
    assert (
        retrieved_service_request["service_request_id"]
        == service_request_fixture["service_request_id"]
    )
    assert retrieved_service_request["status"] == "not-yet-started"
    assert (
        retrieved_service_request["activity_definition_id"]
        == "demo-akihospit-emergency-activity-ioa"
    )


def test_list_service_requests_latest_false(
    create_encounter, service_headers, practitioner_headers
):
    encounter_id = create_encounter["encounter_id"]
    # Create two requests for the same activity
    client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
            "status": "not-yet-started",
            "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
        },
        headers=service_headers,
    )
    client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
            "status": "in-progress",
            "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
        },
        headers=service_headers,
    )

    headers = practitioner_headers
    # Get all requests (latest=False)
    response = client.get(
        f"/api/v1/service_request?encounter_id={encounter_id}&latest=false",
        headers=headers,
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) == 2

    # Get only latest requests (latest=True or default)
    response_latest = client.get(
        f"/api/v1/service_request?encounter_id={encounter_id}&latest=true",
        headers=headers,
    )
    assert response_latest.status_code == 200
    assert isinstance(response_latest.json(), list)
    assert len(response_latest.json()) == 1
    assert response_latest.json()[0]["status"] == "in-progress"


def test_list_service_requests_multiple_activities(
    create_encounter, service_headers, practitioner_headers
):
    encounter_id = create_encounter["encounter_id"]
    activity_id_1 = "demo-akihospit-emergency-activity-ioa"
    activity_id_2 = "demo-akihospit-emergency-activity-ioa2"
    # Create requests for two different activities
    client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": activity_id_1,
            # Set status for activity 1
            "status": "not-yet-started",
            "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
        },
        headers=service_headers,
    )
    client.post(
        "/api/v1/service_request",
        json={
            "encounter_id": encounter_id,
            "activity_definition_id": activity_id_2,
            # Set status for activity 2
            "status": "in-progress",
            "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
        },
        headers=service_headers,
    )

    headers = practitioner_headers
    # Get latest requests (default)
    response = client.get(
        f"/api/v1/service_request?encounter_id={encounter_id}", headers=headers
    )
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 2
    # Find requests by activity ID and check status
    sr1 = next(
        (sr for sr in data if sr["activity_definition_id"] == activity_id_1), None
    )
    sr2 = next(
        (sr for sr in data if sr["activity_definition_id"] == activity_id_2), None
    )
    assert sr1 is not None
    assert sr2 is not None
    # Corrected assertions based on created statuses
    assert sr1["status"] == "not-yet-started"
    assert sr2["status"] == "in-progress"


def test_list_service_requests_with_identifier(
    service_request_fixture,
    practitioner_headers,  # Use simple fixture
):
    encounter_id = service_request_fixture["encounter"]
    # Need to get the encounter identifier first (ensure practitioner_headers are used here)
    encounter_response = client.get(
        f"/api/v1/encounter/{encounter_id}", headers=practitioner_headers
    )
    assert encounter_response.status_code == 200
    encounter_data = encounter_response.json()
    identifier_obj = encounter_data["identifier"][0]
    identifier_str = f"{identifier_obj['system']}|{identifier_obj['value']}"

    headers = practitioner_headers
    response = client.get(
        f"/api/v1/service_request?identifier={identifier_str}", headers=headers
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)
    assert len(response.json()) > 0
    retrieved_service_request = response.json()[0]
    assert (
        retrieved_service_request["service_request_id"]
        == service_request_fixture["service_request_id"]
    )
    assert retrieved_service_request["status"] == "not-yet-started"
    assert (
        retrieved_service_request["activity_definition_id"]
        == "demo-akihospit-emergency-activity-ioa"
    )


def test_list_service_requests_no_encounter_id_or_identifier(practitioner_headers):
    headers = practitioner_headers
    response = client.get("/api/v1/service_request", headers=headers)
    assert response.status_code == 400
    assert "Encounter ID or matching Identifier must be provided" in response.text


def test_list_service_requests_invalid_identifier_format(practitioner_headers):
    headers = practitioner_headers
    response = client.get(
        "/api/v1/service_request?identifier=invalidformat", headers=headers
    )
    assert response.status_code == 400
    assert "Invalid identifier" in response.text


def test_list_service_requests_nonexistent_identifier(practitioner_headers):
    headers = practitioner_headers
    response = client.get(
        "/api/v1/service_request?identifier=sys|valuenotfound", headers=headers
    )
    assert response.status_code == 404
    assert "No encounter found for identifier" in response.text


def test_list_service_requests_no_auth(
    service_request_fixture, monkeypatch
):  # Use simple fixture
    original_auth_state = deps._DISABLE_AUTH
    deps._DISABLE_AUTH = False  # Temporarily force auth ON for this test
    try:
        encounter_id = service_request_fixture["encounter"]
        response = client.get(
            f"/api/v1/service_request?encounter_id={encounter_id}"
        )  # No headers
        assert response.status_code == 401  # Changed from 403, expect Unauthorized
    finally:
        deps._DISABLE_AUTH = original_auth_state  # Restore original state


def test_create_service_request_success(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    payload = {
        "encounter_id": encounter_id,
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "not-yet-started",
        "encounter_timing": {"wait_count": 10, "planned_end_date": 1678886400},
    }
    response = client.post(
        "/api/v1/service_request", json=payload, headers=service_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["encounter"] == encounter_id
    assert data["activity_definition_id"] == "demo-akihospit-emergency-activity-ioa"
    assert data["status"] == "not-yet-started"
    assert data["encounter_timing"]["wait_count"] == 10
    assert data["encounter_timing"]["planned_end_date"] == 1678886400
    assert "service_request_id" in data
    assert "authored_on" in data


def test_create_service_request_success_no_timing(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    payload = {
        "encounter_id": encounter_id,
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "in-progress",
        "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
    }
    response = client.post(
        "/api/v1/service_request", json=payload, headers=service_headers
    )
    assert response.status_code == 200
    data = response.json()
    assert data["encounter"] == encounter_id
    assert data["activity_definition_id"] == "demo-akihospit-emergency-activity-ioa"
    assert data["status"] == "in-progress"
    assert "service_request_id" in data
    assert "authored_on" in data


def test_create_service_request_invalid_status(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    payload = {
        "encounter_id": encounter_id,
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "invalid-status",  # Use an invalid status
        "encounter_timing": {"wait_count": 10, "planned_end_date": 1678886400},
    }
    response = client.post(
        "/api/v1/service_request", json=payload, headers=service_headers
    )
    assert response.status_code == 422  # Expect validation error


def test_create_service_request_missing_encounter_id(service_headers):
    payload = {
        # encounter_id is missing
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "not-yet-started",
        "encounter_timing": {"wait_count": 10, "planned_end_date": 1678886400},
    }
    response = client.post(
        "/api/v1/service_request", json=payload, headers=service_headers
    )
    assert response.status_code == 400  # Expect bad request due to missing field logic
    assert "Encounter ID or matching Identifier must be provided" in response.text


def test_create_service_request_missing_activity_id(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    payload = {
        "encounter_id": encounter_id,
        # activity_definition_id is missing
        "status": "not-yet-started",
        "encounter_timing": {"wait_count": 10, "planned_end_date": 1678886400},
    }
    response = client.post(
        "/api/v1/service_request", json=payload, headers=service_headers
    )
    assert response.status_code == 422  # Expect validation error


def test_create_service_request_missing_status(create_encounter, service_headers):
    encounter_id = create_encounter["encounter_id"]
    payload = {
        "encounter_id": encounter_id,
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        # status is missing
        "encounter_timing": {"wait_count": 10, "planned_end_date": 1678886400},
    }
    response = client.post(
        "/api/v1/service_request", json=payload, headers=service_headers
    )
    assert response.status_code == 422  # Expect validation error


def test_create_service_request_with_identifier(create_encounter, service_headers):
    encounter_identifier = create_encounter["identifier"][
        0
    ]  # Assuming first identifier exists
    identifier_str = f"{encounter_identifier['system']}|{encounter_identifier['value']}"
    payload = {
        # No encounter_id, using identifier query param instead
        "activity_definition_id": "demo-akihospit-emergency-activity-xray",
        "status": "not-yet-started",
        "encounter_timing": {
            "wait_count": 0,
            "planned_end_date": 0,
        },  # ADDED default timing
    }
    response = client.post(
        f"/api/v1/service_request?identifier={identifier_str}",
        json=payload,
        headers=service_headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert (
        data["encounter"] == create_encounter["encounter_id"]
    )  # Check it found the right encounter
    assert data["activity_definition_id"] == "demo-akihospit-emergency-activity-xray"
    assert data["status"] == "not-yet-started"


def test_create_service_request_with_invalid_identifier_format(
    create_encounter, service_headers
):
    identifier_str = "invalid-format-no-pipe"
    payload = {
        "activity_definition_id": "demo-akihospit-emergency-activity-xray",
        "status": "not-yet-started",
        "encounter_timing": {
            "wait_count": 0,
            "planned_end_date": 0,
        },  # ADDED default timing
    }
    response = client.post(
        f"/api/v1/service_request?identifier={identifier_str}",
        json=payload,
        headers=service_headers,
    )
    assert response.status_code == 400
    assert "Invalid identifier" in response.text


def test_create_service_request_with_nonexistent_identifier(
    create_encounter, service_headers
):
    identifier_str = "http://example.com|nonexistent123"
    payload = {
        "activity_definition_id": "demo-akihospit-emergency-activity-xray",
        "status": "not-yet-started",
        "encounter_timing": {
            "wait_count": 0,
            "planned_end_date": 0,
        },  # ADDED default timing
    }
    response = client.post(
        f"/api/v1/service_request?identifier={identifier_str}",
        json=payload,
        headers=service_headers,
    )
    assert response.status_code == 404  # Expect Not Found from EncounterService
    assert "No encounter found for identifier" in response.text


def test_create_service_request_no_auth(monkeypatch):
    original_auth_state = deps._DISABLE_AUTH
    deps._DISABLE_AUTH = False
    try:
        payload = {
            "encounter_id": "some-encounter-id",
            "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
            "status": "not-yet-started",
            "encounter_timing": {
                "wait_count": 0,
                "planned_end_date": 0,
            },  # ADDED default timing
        }
        response = client.post("/api/v1/service_request", json=payload)  # No headers
        assert (
            response.status_code == 401
        )  # Changed from 403, expect Unauthorized first
    finally:
        deps._DISABLE_AUTH = original_auth_state


def test_create_service_request_patient_auth(patient_login_token):
    payload = {
        "encounter_id": "some-encounter-id",
        "activity_definition_id": "demo-akihospit-emergency-activity-ioa",
        "status": "not-yet-started",
        "encounter_timing": {"wait_count": 0, "planned_end_date": 0},
    }
    headers = {"Authorization": f"Bearer {patient_login_token}"}
    response = client.post("/api/v1/service_request", json=payload, headers=headers)
    assert response.status_code == 401  # Changed from 403


def test_create_service_request_idempotency_attempt_update_completed(
    create_encounter, service_headers
):
    encounter_id = create_encounter["encounter_id"]
    activity_id = "demo-akihospit-emergency-activity-xray"

    # 1. Create initial request as completed
    payload1 = {
        "encounter_id": encounter_id,
        "activity_definition_id": activity_id,
        "status": "completed",
        "encounter_timing": {
            "wait_count": 0,
            "planned_end_date": 0,
        },  # ADDED default timing
    }
    response1 = client.post(
        "/api/v1/service_request", json=payload1, headers=service_headers
    )
    assert response1.status_code == 200  # Expecting success now timing is added
    sr1_id = response1.json()["service_request_id"]

    # 2. Attempt to "update" it by posting again with different status
    payload2 = {
        "encounter_id": encounter_id,
        "activity_definition_id": activity_id,
        "status": "in-progress",  # Different status
        "encounter_timing": {
            "wait_count": 0,
            "planned_end_date": 0,
        },  # ADDED default timing
    }
    response2 = client.post(
        "/api/v1/service_request", json=payload2, headers=service_headers
    )
    assert (
        response2.status_code == 200
    )  # POST should still succeed by creating a NEW request
    sr2_id = response2.json()["service_request_id"]

    # 3. Verify a new request was created, not an update
    assert sr1_id != sr2_id

    # 4. Check list (latest=True) returns the new 'in-progress' one
    list_response = client.get(
        f"/api/v1/service_request?encounter_id={encounter_id}", headers=service_headers
    )
    assert list_response.status_code == 200
    latest_requests = list_response.json()
    assert len(latest_requests) >= 1  # Might be other requests from fixtures
    latest_xray = next(
        (sr for sr in latest_requests if sr["activity_definition_id"] == activity_id),
        None,
    )
    assert latest_xray is not None
    assert latest_xray["status"] == "in-progress"
    assert latest_xray["service_request_id"] == sr2_id
