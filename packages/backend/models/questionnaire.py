from dataclasses import dataclass, field
from typing import List, Literal, NewType

from packages.backend.models.common import UnixTimestamp
from packages.backend.models.patient import PatientId


QuestionnaireId = NewType("QuestionnaireId", str)
LinkId = NewType("LinkId", str)


@dataclass
class AnswerOption:
    value_integer: int


@dataclass
class QuestionnaireItem:
    link_id: LinkId
    text: str
    type: Literal["integer"]
    answer_option: List[AnswerOption] = field(default_factory=list)
    initial: List[AnswerOption] = field(default_factory=list)


@dataclass
class Questionnaire:
    # Unique ID for the questionnaire
    questionnaire_id: QuestionnaireId

    # Name of the questionnaire
    name: str

    # Description of the questionnaire
    description: str

    # List of items in the questionnaire
    item: List[QuestionnaireItem] = field(default_factory=list)


@dataclass
class QuestionResponseItem:
    link_id: LinkId
    answer: List[AnswerOption]


@dataclass
class QuestionnaireResponse:
    questionnaire_id: QuestionnaireId
    authored: UnixTimestamp
    subject: PatientId
    item: List[QuestionResponseItem]
