from dataclasses import dataclass
from enum import Enum
from typing import Optional

from packages.backend.models.common import UnixTimestamp
from packages.backend.models.encounter import EncounterId
from packages.backend.models.patient import PatientId
from packages.backend.models.authentication import Role


class EventType(Enum):
    ENCOUNTER_CREATED = "ENCOUNTER_CREATED"
    TEXT_MESSAGE_SENT = "TEXT_MESSAGE_SENT"
    CONNEXION_ATTEMPT = "CONNEXION_ATTEMPT"
    LOGIN_PAGE_VIEWED = "LOGIN_PAGE_VIEWED"
    SENT_SHARE_LINK = "SENT_SHARE_LINK"
    ACCEPTED_TOS = "ACCEPTED_TOS"


class TextType(Enum):
    POST_DISCHARGE_SMS = "POST_DISCHARGE_SMS"
    LOGIN_SMS = "LOGIN_SMS"
    TRIGGERED_SMS = "TRIGGERED_SMS"
    SATISFACTION_SURVEY = "SATISFACTION_SURVEY"


@dataclass
class TextMessage:
    text: str
    recipient: str
    text_type: TextType


@dataclass
class ConnexionAttemptDetails:
    success: bool
    failure_reason: Optional[str] = None


@dataclass
class Event:
    timestamp: UnixTimestamp
    event_type: EventType
    encounter_id: EncounterId
    patient_id: Optional[PatientId]
    role: Optional[Role] = None
    text_message_details: Optional[TextMessage] = None
    connexion_details: Optional[ConnexionAttemptDetails] = None

    def __post_init__(self):
        if self.event_type == EventType.TEXT_MESSAGE_SENT:
            assert self.text_message_details is not None, (
                "text_message_details required for TEXT_MESSAGE_SENT"
            )
            assert self.role is not None, "Role required for TEXT_MESSAGE_SENT"
            assert self.patient_id is not None, (
                "Patient ID required for TEXT_MESSAGE_SENT"
            )
        elif self.event_type == EventType.CONNEXION_ATTEMPT:
            assert self.connexion_details is not None, (
                "connexion_details required for CONNEXION_ATTEMPT"
            )
            assert self.role is not None, "Role required for CONNEXION_ATTEMPT"
            assert self.patient_id is not None, (
                "Patient ID required for CONNEXION_ATTEMPT"
            )


@dataclass
class UnauthLogPayload:
    """Payload for unauthenticated event logging requiring only an encounter ID."""

    encounter_id: EncounterId
