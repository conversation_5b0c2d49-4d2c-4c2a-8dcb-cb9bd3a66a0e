from dataclasses import dataclass, field
from typing import NewType, List, Optional, Literal

from packages.backend.models.common import ISODate


PatientId = NewType("PatientId", str)


@dataclass
class HumanName:
    first_name: str
    last_name: str

    def __post_init__(self):
        assert self.first_name, "First name must not be empty"
        assert self.last_name, "Last name must not be empty"


@dataclass
class Telecom:
    system: Literal["sms"]  # Only support sms for now
    value: str

    def __post_init__(self):
        # Simple validation for SMS
        assert self.value.startswith("+33"), (
            "SMS value must be a valid phone number starting with +33"
        )


@dataclass
class Contact:
    relationship: Optional[List[Literal["relative"]]] = None
    telecom: List[Telecom] = field(default_factory=list)

    def __post_init__(self):
        if self.relationship is None:
            self.relationship = []
        assert self.telecom, "Contact must have at least one telecom entry"
        for t in self.telecom:
            assert t.system == "sms", (
                "Only 'sms' telecom system is currently supported for relatives"
            )


@dataclass
class Patient:
    patient_id: PatientId
    name: HumanName
    birth_date: ISODate
    phone: Optional[str] = None
    contact: List[Contact] = field(default_factory=list)

    def __post_init__(self):
        if self.phone:
            assert self.phone.startswith("+33"), "Phone number must start with +33"

        birth_date_splitted = self.birth_date.split("-")
        assert len(birth_date_splitted) == 3 and [
            len(x) for x in birth_date_splitted
        ] == [
            4,
            2,
            2,
        ], "Birth date format should be YYYY-MM-DD (e.g. 2021-05-21)"
        assert 1 <= int(birth_date_splitted[1]) <= 12, (
            "Birth date month must be between 01 and 12"
        )

        # Validate contacts if present
        for contact_item in self.contact:
            assert isinstance(contact_item, Contact), (
                "Each item in contact list must be a Contact object"
            )
            # Further validation within Contact and Telecom happens in their __post_init__
