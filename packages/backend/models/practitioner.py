from dataclasses import dataclass
from typing import NewType

from packages.backend.models.organization import OrganizationId


PractitionerId = NewType("PractitionerId", str)


@dataclass
class Practitioner:
    """
    A practitioner is a healthcare professional.
    The practitioner information is coming from the SSO.
    """

    #: The practitioner's unique identifier
    practitioner_id: PractitionerId

    #: organization_id
    organization_id: OrganizationId
