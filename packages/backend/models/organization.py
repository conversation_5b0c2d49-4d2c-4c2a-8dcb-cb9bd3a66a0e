from dataclasses import dataclass, field
from typing import List, NewType, Optional

from packages.backend.models.activity_definition import ActivityDefinition
from packages.backend.models.questionnaire import QuestionnaireId


OrganizationId = NewType("OrganizationId", str)
HealthcareServiceId = NewType("HealthcareServiceId", str)


@dataclass
class Organization:
    organization_id: OrganizationId
    name: str
    description: str
    patient_session_timeout_minutes: int


@dataclass
class HealthcareService:
    #: Unique identifier for the healthcare service
    healthcare_service_id: HealthcareServiceId

    #: Name of the different "filières" (e.g. "Ambulatoire", "Couché", "Pediatric", etc.)
    name: str

    #: Description of the healthcare service
    description: str

    #: Unique identifier for the healthcare service
    provided_by: OrganizationId

    #: The service in which this service is offered
    offered_in: Optional[HealthcareServiceId] = None

    #: List of activity that can be performed in this healthcare service
    activity_definition: List[ActivityDefinition] = field(default_factory=list)

    #: Questionnaire to be filled by the patient
    questionnaire: List[QuestionnaireId] = field(default_factory=list)
