from dataclasses import dataclass
from typing import NewType, Optional


Principal = NewType("Principal", str)
ISODate = NewType("ISODate", str)
UnixTimestamp = NewType("UnixTimestamp", int)


@dataclass
class Timing:
    # The number of minutes to wait before the encounter
    planned_end_date: UnixTimestamp

    # The number of patients to wait for before the encounter
    wait_count: int


@dataclass
class Period:
    start: Optional[UnixTimestamp] = None
    end: Optional[UnixTimestamp] = None


@dataclass
class Identifier:
    value: str
    system: str
    use: str = "secondary"
