from dataclasses import dataclass
from typing import NewType, Optional

ActivityDefinitionId = NewType("ActivityDefinitionId", str)


@dataclass
class SendNotification:
    trigger: int | str
    message: str


@dataclass
class ActivityDefinition:
    activity_definition_id: ActivityDefinitionId
    name: str
    description: str

    # Whether the activity is shareable or not (to a close relative)
    shareable: bool

    # Default wait time in seconds
    default_wait_time: Optional[int]

    # When to send a notification for this activity
    # It is an list of wait_counts and the service_activity status
    send_notification: Optional[list[SendNotification] | SendNotification]
