from dataclasses import dataclass, field
from typing import List

from packages.backend.models.encounter import Encounter
from packages.backend.models.organization import HealthcareService, Organization
from packages.backend.models.patient import Patient
from packages.backend.models.questionnaire import Questionnaire
from packages.backend.models.service_request import ServiceRequest


@dataclass
class PatientView:
    encounter: Encounter
    organization: Organization
    healthcare_service: List[HealthcareService] = field(default_factory=list)
    service_request: List[ServiceRequest] = field(default_factory=list)
    questionnaire: List[Questionnaire] = field(default_factory=list)


@dataclass
class PractitionerView:
    organization: Organization
    encounters: List[Encounter] = field(default_factory=list)
    patients: List[Patient] = field(default_factory=list)
    healthcare_service: List[HealthcareService] = field(default_factory=list)
