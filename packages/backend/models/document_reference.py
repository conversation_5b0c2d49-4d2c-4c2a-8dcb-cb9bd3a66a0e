from dataclasses import dataclass
from typing import List


@dataclass
class Reference:
    reference: str


@dataclass
class Attachment:
    id: str
    name: str


@dataclass
class Content:
    attachment: Attachment


@dataclass
class DocumentReference:
    id: str
    description: str
    author: List[Reference]
    content: List[Content]
    status: str = "current"
    resourceType: str = "DocumentReference"
