from dataclasses import dataclass
from typing import Literal, NewType

from packages.backend.models.activity_definition import ActivityDefinitionId
from packages.backend.models.common import Timing, UnixTimestamp
from packages.backend.models.encounter import EncounterId


ServiceRequestId = NewType("ServiceRequestId", str)
ServiceRequestStatus = Literal[
    "not-yet-started", "in-progress", "waiting-for-results", "cancelled", "completed"
]


@dataclass
class ServiceRequest:
    """
    A record of a request for service such as diagnostic investigations,
    treatments, or operations to be performed.
    """

    service_request_id: ServiceRequestId
    activity_definition_id: ActivityDefinitionId
    authored_on: UnixTimestamp
    encounter: EncounterId
    encounter_timing: Timing
    status: ServiceRequestStatus = "not-yet-started"
