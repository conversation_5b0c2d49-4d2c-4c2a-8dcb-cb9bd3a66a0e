from dataclasses import dataclass
from enum import Enum
from typing import Optional

from packages.backend.models.encounter import EncounterId
from packages.backend.models.organization import OrganizationId
from packages.backend.models.patient import PatientId


class Role(Enum):
    PRACTITIONER = "PRACTITIONER"
    PATIENT = "PATIENT"
    RELATIVE = "RELATIVE"
    SERVICE = "SERVICE"


@dataclass
class User:
    """
    A user is a practitioner, a patient or a patient's close relative.
    """

    #: The user's unique identifier
    #: This is either a practitioner ID (coming from SSO) or an encounter ID
    user_id: str  # PractitionerId | EncounterId

    #: The user's role
    role: Role

    #: The practitioner's organization (e.g. chu-limoges)
    organization_id: Optional[OrganizationId] = None


@dataclass
class TokenData:
    patient_id: PatientId
    encounter_id: EncounterId
    close_relative: bool


@dataclass
class Token:
    access_token: str
    token_type: str
