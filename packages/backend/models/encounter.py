from dataclasses import dataclass, field
from typing import List, Literal, NewType, Optional

from packages.backend.models.common import (
    Identifier,
    Period,
    UnixTimestamp,
)
from packages.backend.models.organization import HealthcareServiceId, OrganizationId
from packages.backend.models.patient import PatientId
from packages.backend.models.document_reference import Attachment


EncounterId = NewType("EncounterId", str)
ShareId = NewType("ShareId", str)
DischargeDisposition = Literal[
    "home",
    "same-hospital",
    "other-hospital",
    "against-medical-advice",
    "escaped",
    "other",
    "pending",
]


@dataclass
class Destination:
    # Name of the destination if the patient is admitted or transferred
    main_destination: Optional[str] = None
    # Contains details about the destination, such as "cardiology departement" or "room 424"
    details: Optional[str] = None


@dataclass
class Discharge:
    # Category or kind of location after discharge
    disposition: Optional[DischargeDisposition] = None

    # Organization to which the patient is discharged
    # (only if the disposition is hospital/nursing/hospice or applicable)
    destination: Optional[Destination] = None

    # Instructions (a.k.a. "consignes de sortie") provided to the patient
    instructions: List[Attachment] = field(default_factory=list)


@dataclass
class Encounter:
    # Unique ID for the patient to share its encounter with a close relative
    share_id: ShareId

    # Patient who is involved in the encounter
    subject: PatientId

    # Organization where the encounter took place
    service_provider: OrganizationId

    # Period during which the encounter took place
    actual_period: Period

    # Unique ID for the encounter
    encounter_id: EncounterId

    # At first, the emergency service the patient arrives to. After the IOA assessment, the actual "filière" chosen by the IOA (infirmerie d'orientation et d'accueil)
    service_type: HealthcareServiceId

    # Identifiers for the encounter in the upstream system
    identifier: Optional[List[Identifier]] = None

    # The estimated length of the encounter in minutes
    planned_length: Optional[int] = None

    # Date at which the patient agreed with the CGU
    subject_consent_date: Optional[UnixTimestamp] = None

    # Discharge information
    discharge: Optional[Discharge] = None

    # Timestamp when the post-discharge SMS was sent
    post_discharge_sms_sent_at: Optional[UnixTimestamp] = None
