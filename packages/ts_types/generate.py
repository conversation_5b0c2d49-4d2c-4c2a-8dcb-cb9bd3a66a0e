# flake8: noqa
from dataclasses import MISSING, Field, dataclass, fields, is_dataclass
from enum import Enum
import importlib
import inspect
from pathlib import Path
import re
from textwrap import dedent
from types import UnionType
from typing import (
    Any,
    Dict,
    List,
    Literal,
    NewType,
    Optional,
    Sequence,
    Set,
    Type,
    Union,
    cast,
    get_args,
    get_origin,
)
from typing import _GenericAlias, _LiteralGenericAlias  # type: ignore
import collections.abc


ModuleName = NewType("ModuleName", str)
TypeName = NewType("TypeName", str)


@dataclass
class ModuleDependency:
    """Declares the dependency of one module on another module."""

    #: The name of the module depended on.
    name: ModuleName

    #: The types dependended on.
    types: Sequence[TypeName]

    def __post_init__(self) -> None:
        assert len(self.types) > 0


@dataclass
class OutputModule:
    """A TypeScript module ready to be written to disk."""

    #: The module dependencies.
    dependencies: Sequence[ModuleDependency]

    #: The type definitions.
    type_definitions: Dict[TypeName, str]

    def __post__init__(self) -> None:
        # There must be at least one type definition in a module.
        assert len(self.type_definitions) > 0


@dataclass
class TsTypesOutput:
    """
    A dictionary of TypeScript modules ready to be written to disk.

    Note that all the inter-module dependencies are resolved: no module
    depends on a module that is not within this dictionary.
    """

    modules: Dict[ModuleName, OutputModule]

    def write(self, path: Path) -> None:
        """
        Writes the modules to disk.

        Args:
            path: Must be a folder.  If it does not exist, it is created (and the
                parent folders are created recursively).  The full folder is cleaned up
                at the beginning to deal with module deletion on Python's side.  There
                is one TypeScript file per module.
        """
        assert not path.exists() or path.is_dir()
        path.mkdir(parents=True, exist_ok=True)
        for p in path.iterdir():
            assert p.is_file()
            p.unlink()

        for module_name, module in self.modules.items():
            content = "/* AUTOGENERATED: DO NOT EDIT */\n"

            for dep in module.dependencies:
                print(dep.name)
                stem = dep.name[dep.name.rindex(".") + 1 :]
                content += (
                    "import { " + ", ".join(dep.types) + ' } from "./' + stem + '";\n'
                )

            for type_name in sorted(module.type_definitions.keys()):
                content += "\n" + module.type_definitions[type_name]

            with (path / f"{module_name}.ts").open("w") as f:
                f.write(content)


def generate_ts_for_modules(module_names: Sequence[ModuleName]) -> TsTypesOutput:
    """Generates TypeScript types for a list of Python modules."""
    modules: Dict[ModuleName, OutputModule] = {}
    for module_name in module_names:
        type_definitions: Dict[TypeName, str] = {}
        module = importlib.import_module(module_name)
        module_types: List[Type] = []
        type_dependencies: Set[Type] = set()
        for type_name, type in inspect.getmembers(module):
            generator = _Generator()
            if get_origin(type) is Union:
                source = inspect.getsource(module)
                ts = generator.generate_ts_for_alias(type, type_name, source)
            else:
                if inspect.isclass(type):
                    file: Optional[str] = inspect.getfile(type)
                elif isinstance(type, NewType):
                    file = importlib.import_module(type.__module__).__file__
                elif get_origin(type) is Literal:
                    file = None
                elif get_origin(type) is Union:
                    file = None
                else:
                    continue
                if file is not None and file != module.__file__:
                    continue
                ts = generator.generate_ts_for_type(type, name_override=type_name)
            module_types.append(type)
            type_definitions[TypeName(type_name)] = ts
            type_dependencies.update(generator.dependencies)
        if len(type_definitions) == 0:
            continue
        module_dependencies: Dict[ModuleName, Set[TypeName]] = {}
        for dep in type_dependencies:
            if dep in module_types:
                continue
            qualified_type_name = _qualified_type_name(dep)
            if qualified_type_name.module not in module_dependencies:
                module_dependencies[qualified_type_name.module] = set()
            module_dependencies[qualified_type_name.module].add(
                qualified_type_name.type
            )
        module_dependency_names = sorted(module_dependencies.keys())
        module_stem = ModuleName(module_name[module_name.rindex(".") + 1 :])
        modules[module_stem] = OutputModule(
            dependencies=[
                ModuleDependency(name=name, types=sorted(module_dependencies[name]))
                for name in module_dependency_names
            ],
            type_definitions=type_definitions,
        )
    return TsTypesOutput(modules=modules)


def generate_ts_for_types(*types: Type) -> str:
    """
    Generates TypeScript types for a list of types.  The types can depend on each other but
    not on types not within the list.
    """
    type_definitions: Dict[TypeName, str] = {}
    dependencies: Set[Type] = set()
    for type in types:
        assert inspect.isclass(type)
        generator = _Generator()
        type_definitions[TypeName(type.__name__)] = generator.generate_ts_for_type(
            type, name_override=None
        )
        dependencies.update(generator.dependencies)
    assert all(
        inspect.isclass(dep) and dep.__name__ in type_definitions
        for dep in dependencies
    )
    return "\n".join(type_definitions[type_name] for type_name in type_definitions)


class _Generator:
    """
    Internal generator class used to generate TypeScript types and to keep
    track of dependencies.
    """

    def __init__(self) -> None:
        self._dependencies: Set[Type] = set()

    @property
    def dependencies(self) -> Sequence[Type]:
        """The list of type dependencies encountered."""
        return list(self._dependencies)

    def generate_ts_for_type(self, type: Type, name_override: Optional[str]) -> str:
        """
        Generates TypeScript code for a type.
        """
        if inspect.isclass(type):
            if is_dataclass(type):
                return self._generate_ts_for_dataclass(type, name_override)
            if issubclass(type, Enum):
                return self._generate_ts_for_enum(type, name_override)
        elif isinstance(type, NewType):
            return self._generate_ts_for_newtype(type, name_override)
        elif get_origin(type) is Literal:
            return self._generate_ts_for_literal(type, name_override)
        raise Exception(f"unsupported type {type}")

    def generate_ts_for_alias(self, t: Type, name: str, source: str) -> str:
        """
        Generates TypeScript code for an alias.

        Contrary to a type, a type alias has no intrinsic name and it is not
        possible to inspect it to get its source.
        """

        # Gets the documentation for the type alias.  We do this by loading the
        # source code and finding the line where the type alias is declared using
        # a crude regex.  The comment lines appear right before the type alias
        # and start with `#: `.
        cleaned_comment_lines: List[str] = []
        comment_lines_found = False
        for match in re.finditer(
            r"\n(?P<comment>(#:(\s+[^\n]*)?\n)+)" + name + r"\s*=\s*",
            source,
            re.MULTILINE,
        ):
            assert not comment_lines_found
            comment_lines_found = True
            for line in match.group("comment").strip().split("\n"):
                line = line.strip()
                if line.startswith("#: "):
                    line = line[len("#: ") :]
                elif line.startswith("#:"):
                    line = line[len("#:") :]
                else:
                    raise Exception("unexpected line start")
                cleaned_comment_lines.append(line)
        output = ""
        if cleaned_comment_lines is not None:
            if len(cleaned_comment_lines) == 1:
                output += f"/** {cleaned_comment_lines[0]} */\n"
            else:
                output += "/**\n"
                for line in cleaned_comment_lines:
                    if len(line.strip()) == 0:
                        output += " *\n"
                    else:
                        output += f" * {line}\n"
                output += " */\n"

        output += f"export type {name} = " + self._get_ts_field_type(t) + ";\n"

        return output

    def _generate_ts_for_dataclass(
        self, type: Type, name_override: Optional[str]
    ) -> str:
        """
        Generates TypeScript code for a dataclass
        (declared using the `dataclasses.dataclass` decorator).
        """
        assert inspect.isclass(type)
        assert is_dataclass(type), f"{type.__name__} is not a dataclass"

        # Get the documentation for the fields.  We do this by loading the
        # source code and finding the line where the fields are declared using
        # a crude regex.  The comment lines appear right before the fields
        # and start with `#: `.
        field_comments: Dict[str, Sequence[str]] = {}
        source = dedent(inspect.getsource(type))
        for match in re.finditer(
            r"\n(?P<comment>(\s+#:(\s+[^\n]*)?\n)+)\s+(?P<field_name>[a-z][a-z_]*): ",
            source,
            re.MULTILINE,
        ):
            cleaned_comment_lines: List[str] = []
            for line in match.group("comment").strip().split("\n"):
                line = line.strip()
                if line.startswith("#: "):
                    line = line[len("#: ") :]
                elif line.startswith("#:"):
                    line = line[len("#:") :]
                else:
                    raise Exception("unexpected line start")
                cleaned_comment_lines.append(line)
            field_comments[match.group("field_name")] = cleaned_comment_lines

        output = ""

        # Get the type's documentation by looking at its docstring.
        # Note that dataclasses automatically have by default a generic docstring
        # that we don't want to carry over to TypeScript.
        if type.__doc__ is not None and _is_generic_dataclass_docstring(
            type.__doc__, type.__name__
        ):
            doc_lines = dedent(type.__doc__).strip().split("\n")
            if len(doc_lines) == 1:
                output += f"/** {doc_lines[0]} */\n"
            else:
                output += "/**\n"
                for line in doc_lines:
                    if len(line.strip()) == 0:
                        output += " *\n"
                    else:
                        output += f" * {line}\n"
                output += " */\n"

        output += f"export interface {name_override or type.__name__} "

        # Get the inheritance chain by looking at all the dataclass superclasses
        relevant_bases = [base for base in type.__bases__ if is_dataclass(base)]
        base_fields: List[Field] = []
        discriminator: Optional[str] = None
        if len(relevant_bases) > 0:
            assert len(relevant_bases) == 1
            base = relevant_bases[0]
            base_fields += list(fields(base))
            for field in fields(base):
                if field.metadata.get("discriminator") is True:
                    if discriminator is not None:
                        raise Exception("multiple discriminators")
                    discriminator = field.name
            self._add_dependency(base)
            output += f"extends {base.__name__} "
        relevant_fields = [field for field in fields(type) if field not in base_fields]
        if len(relevant_fields) == 0:
            output += "{}\n"
        else:
            output += "{\n"
            for index, field in enumerate(relevant_fields):
                comment_lines = field_comments.get(field.name)
                if comment_lines is not None:
                    if index > 0:
                        output += "\n"
                    if len(comment_lines) == 1:
                        output += f"  /** {comment_lines[0]} */\n"
                    else:
                        output += "  /**\n"
                        for line in comment_lines:
                            if len(line.strip()) == 0:
                                output += "   *\n"
                            else:
                                output += f"   * {line}\n"
                        output += "   */\n"
                ts_optional = False
                if field.name == discriminator:
                    assert field.default is not None and field.default is not MISSING
                    ts_field_type = f'"{field.default}"'
                else:
                    assert field.type is not None
                    py_optional_type = self._get_optional_type(field.type)
                    if py_optional_type is None:
                        ts_field_type = self._get_ts_field_type(
                            field.type, all_keys=field.metadata.get("all_keys") is True
                        )
                    else:
                        ts_field_type = f"{self._get_ts_field_type(py_optional_type, all_keys=field.metadata.get('all_keys') is True)} | null"
                        if field.default is None:
                            ts_optional = True
                if ts_optional:
                    output += f"  {field.name}?: {ts_field_type};\n"
                else:
                    output += f"  {field.name}: {ts_field_type};\n"
            output += "}\n"

        return output

    def _generate_ts_for_literal(self, type: Type, name_override: Optional[str]) -> str:
        """
        Transforms a Python Literal type into a TypeScript union type.

        :param type_name: The name of the TypeScript type.
        :param values: A list of values in the Python Literal.
        :return: A string representing the TypeScript type.
        """
        # Join the values into a TypeScript union type string
        ts_values = " | ".join(f'"{value}"' for value in list(get_args(type)))

        # Format the TypeScript type declaration
        return f"export type {name_override} = {ts_values};\n"

    def _generate_ts_for_enum(self, type: Type, name_override: Optional[str]) -> str:
        """
        Generates TypeScript code for an `enum.Enum` enum.
        """
        assert inspect.isclass(type)

        output = ""

        if type.__doc__ is not None and type.__doc__ != "An enumeration.":
            doc_lines = dedent(type.__doc__).strip().split("\n")
            if len(doc_lines) == 1:
                output += f"/** {doc_lines[0]} */\n"
            else:
                output += "/**\n"
                for line in doc_lines:
                    if len(line.strip()) == 0:
                        output += " *\n"
                    else:
                        output += f" * {line}\n"
                output += " */\n"

        output += f"export enum {name_override or type.__name__} {{\n"
        for e in cast(Type[Enum], type):
            assert e.name == e.value, "enums where names != values are not supported"
            output += f'  {e.name} = "{e.name}",\n'
        output += "}\n"

        return output

    def _generate_ts_for_newtype(self, t: Type, name_override: Optional[str]) -> str:
        """
        Generates TypeScript code for a `typing.NewType` new type.
        """
        assert isinstance(t, NewType)
        name = name_override or t.__name__
        name_first_lowercase = name[0].lower() + name[1:]
        supertype_ts = self._get_ts_field_type(t.__supertype__)
        source = inspect.getsource(importlib.import_module(t.__module__))
        cleaned_comment_lines: List[str] = []
        comment_lines_found = False
        for match in re.finditer(
            r"\n(?P<comment>(#:(\s+[^\n]*)?\n)+)" + t.__name__ + r"\s*=\s*",
            source,
            re.MULTILINE,
        ):
            assert not comment_lines_found
            comment_lines_found = True
            for line in match.group("comment").strip().split("\n"):
                line = line.strip()
                if line.startswith("#: "):
                    line = line[len("#: ") :]
                elif line.startswith("#:"):
                    line = line[len("#:") :]
                else:
                    raise Exception("unexpected line start")
                cleaned_comment_lines.append(line)
        if len(cleaned_comment_lines) > 0:
            cleaned_comment_lines.append("")
        cleaned_comment_lines.append(
            'This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/).'
        )
        output = ""
        if cleaned_comment_lines is not None:
            if len(cleaned_comment_lines) == 1:
                output += f"/** {cleaned_comment_lines[0]} */\n"
            else:
                output += "/**\n"
                for line in cleaned_comment_lines:
                    if len(line.strip()) == 0:
                        output += " *\n"
                    else:
                        output += f" * {line}\n"
                output += " */\n"
        output += f"export type {name} = {supertype_ts} & {{ readonly __tag: unique symbol }};\n\n"
        output += f"/** Creates a {name} from a {supertype_ts}. */\n"
        output += f"export const {name_first_lowercase} = (value: {supertype_ts}) => value as {name};\n"
        return output

    def _is_optional(self, t: Type) -> bool:
        return get_origin(t) is Union and type(None) in get_args(t)

    def _get_optional_type(self, t: Type) -> Optional[Type]:
        if not self._is_optional(t):
            return None
        args = tuple(arg for arg in get_args(t) if arg is not type(None))
        assert len(args) > 0
        return _GenericAlias(Union, args)

    def _get_ts_field_type(self, t: Type, all_keys: bool = False) -> str:
        if get_origin(t) is Union:
            return " | ".join(self._get_ts_field_type(it) for it in get_args(t))
        elif get_origin(t) is UnionType:
            return " | ".join(self._get_ts_field_type(it) for it in get_args(t))
        elif get_origin(t) is collections.abc.Sequence or get_origin(t) is list:
            return f"Array<{self._get_ts_field_type(get_args(t)[0])}>"
        elif get_origin(t) is dict:
            key_type = get_args(t)[0]
            value_type = get_args(t)[1]
            if key_type is str:
                return f"{{ [key: string]: {self._get_ts_field_type(value_type)} }}"
            elif inspect.isclass(key_type):
                self._add_dependency(key_type)
                if all_keys:
                    return f"{{ [key in {key_type.__name__}]: {self._get_ts_field_type(value_type)} }}"
                else:
                    return f"Partial<{{ [key in {key_type.__name__}]: {self._get_ts_field_type(value_type)} }}>"
            else:
                raise Exception(f"unsupported type {key_type} for dictionary keys")
        elif t is dict:
            return "{ [key: string]: any }"
        elif t is list:
            return "Array<any>"
        elif t is str:
            return "string"
        elif t is bool:
            return "boolean"
        elif t is int:
            return "number"
        elif t is float:
            return "number"
        elif t is Any:
            return "any"
        elif inspect.isclass(t):
            self._add_dependency(t)
            return t.__name__
        elif isinstance(t, NewType):
            self._add_dependency(t)
            return t.__name__
        elif isinstance(t, _LiteralGenericAlias):
            return " | ".join(f'"{v}"' for v in get_args(t))
        else:
            raise Exception(f"unsupported type {t}")

    def _add_dependency(self, t: Type) -> None:
        if t not in self._dependencies:
            self._dependencies.add(t)


def _is_generic_dataclass_docstring(docstring: str, type_name: str) -> bool:
    """
    Checks whether a docstring is a generic docstring put by the `@dataclasses.dataclass`
    decorator.
    """
    return not docstring.startswith(f"{type_name}(")


@dataclass
class _QualifiedTypeName:
    """Fully qualify a type name with the name of the module that declares it."""

    module: ModuleName
    type: TypeName


def _qualified_type_name(t: Type) -> _QualifiedTypeName:
    """Gets the qualified type name of a type."""
    if isinstance(t, NewType):
        return _QualifiedTypeName(module=t.__module__, type=t.__name__)
    else:
        assert inspect.isclass(t), f"not a class: {t}"
        module = inspect.getmodule(t)
        assert module is not None
        return _QualifiedTypeName(
            module=ModuleName(module.__name__), type=TypeName(t.__name__)
        )
