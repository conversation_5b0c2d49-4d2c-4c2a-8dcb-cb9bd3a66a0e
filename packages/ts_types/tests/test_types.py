from abc import ABC
from dataclasses import dataclass, field
from enum import Enum, unique
from textwrap import dedent
from typing import Any, Dict, List, Optional, Sequence, Union
import pytest

from packages.ts_types.generate import generate_ts_for_types


def test_not_dataclass() -> None:
    class Class:
        pass

    with pytest.raises(Exception):
        generate_ts_for_types(Class)


@dataclass
class DataclassMandatoryField:
    mandatory_field: str


def test_dataclass_mandatory_field() -> None:
    assert generate_ts_for_types(DataclassMandatoryField) == dedent(
        """\
            export interface DataclassMandatoryField {
              mandatory_field: string;
            }
        """
    )


@dataclass
class DataclassComment:
    """Doc string."""

    pass


def test_dataclass_comment() -> None:
    assert generate_ts_for_types(DataclassComment) == dedent(
        """\
        /** Doc string. */
        export interface DataclassComment {}
    """
    )


@dataclass
class DataclassMultilineComment:
    """
    Multiline
    doc string.
    """

    pass


def test_dataclass_multiline_comment() -> None:
    assert generate_ts_for_types(DataclassMultilineComment) == dedent(
        """\
        /**
         * Multiline
         * doc string.
         */
        export interface DataclassMultilineComment {}
    """
    )


@dataclass
class DataclassFieldComment:
    #: Comment.
    field_with_comment: str

    #: Multiline
    #: comment.
    field_with_multiline_comment: str


def test_dataclass_field_comment() -> None:
    assert generate_ts_for_types(DataclassFieldComment) == dedent(
        """\
        export interface DataclassFieldComment {
          /** Comment. */
          field_with_comment: string;

          /**
           * Multiline
           * comment.
           */
          field_with_multiline_comment: string;
        }
    """
    )


@dataclass
class DataclassMandatoryFieldWithDefault:
    mandatory_field: str = field(default="value")


def test_dataclass_mandatory_field_with_default() -> None:
    assert generate_ts_for_types(DataclassMandatoryFieldWithDefault) == dedent(
        """\
        export interface DataclassMandatoryFieldWithDefault {
          mandatory_field: string;
        }
    """
    )


@dataclass
class DataclassOptionalField:
    optional_field: Optional[str]


def test_dataclass_optional_field() -> None:
    assert generate_ts_for_types(DataclassOptionalField) == dedent(
        """\
        export interface DataclassOptionalField {
          optional_field: string | null;
        }
    """
    )


@dataclass
class DataclassOptionalFieldWithDefault:
    optional_field: Optional[str] = field(default=None)


def test_dataclass_optional_field_with_default() -> None:
    assert generate_ts_for_types(DataclassOptionalFieldWithDefault) == dedent(
        """\
        export interface DataclassOptionalFieldWithDefault {
          optional_field?: string | null;
        }
    """
    )


@dataclass
class DataclassScalarFields:
    str_field: str
    bool_field: bool
    int_field: int
    float_field: float
    any_field: Any


def test_dataclass_scalar_fields() -> None:
    assert generate_ts_for_types(DataclassScalarFields) == dedent(
        """\
        export interface DataclassScalarFields {
          str_field: string;
          bool_field: boolean;
          int_field: number;
          float_field: number;
          any_field: any;
        }
    """
    )


@dataclass
class DataclassArrayFields:
    sequence_field: Sequence[str]
    list_field: List[str]
    any_list_field: list


def test_dataclass_array_fields() -> None:
    assert generate_ts_for_types(DataclassArrayFields) == dedent(
        """\
        export interface DataclassArrayFields {
          sequence_field: Array<string>;
          list_field: Array<string>;
          any_list_field: Array<any>;
        }
    """
    )


@dataclass
class DataclassDictFields:
    dict_field: Dict[str, int]
    any_dict_field: dict


def test_dataclass_dict_fields() -> None:
    assert generate_ts_for_types(DataclassDictFields) == dedent(
        """\
        export interface DataclassDictFields {
          dict_field: { [key: string]: number };
          any_dict_field: { [key: string]: any };
        }
    """
    )


@dataclass
class DataclassUnionFields:
    union_field: Union[str, bool]
    optional_union_field: Optional[Union[str, bool]]


def test_dataclass_union_fields() -> None:
    assert generate_ts_for_types(DataclassUnionFields) == dedent(
        """\
        export interface DataclassUnionFields {
          union_field: string | boolean;
          optional_union_field: string | boolean | null;
        }
    """
    )


@unique
class EnumTest(Enum):
    ONE = "ONE"
    TWO = "TWO"


def test_enum() -> None:
    assert generate_ts_for_types(EnumTest) == dedent(
        """\
        export enum EnumTest {
          ONE = "ONE",
          TWO = "TWO",
        }
    """
    )


@unique
class EnumTestWithComment(Enum):
    """Doc string."""

    ONE = "ONE"
    TWO = "TWO"


def test_enum_with_comment() -> None:
    assert generate_ts_for_types(EnumTestWithComment) == dedent(
        """\
        /** Doc string. */
        export enum EnumTestWithComment {
          ONE = "ONE",
          TWO = "TWO",
        }
    """
    )


@unique
class EnumTestWithMultilineComment(Enum):
    """
    Multiline
    doc string.
    """

    ONE = "ONE"
    TWO = "TWO"


def test_enum_with_multiline_comment() -> None:
    assert generate_ts_for_types(EnumTestWithMultilineComment) == dedent(
        """\
        /**
         * Multiline
         * doc string.
         */
        export enum EnumTestWithMultilineComment {
          ONE = "ONE",
          TWO = "TWO",
        }
    """
    )


@dataclass
class DataclassDictFieldWithEnumKeys:
    dict_field: Dict[EnumTest, int]
    dict_field_with_all_keys: Dict[EnumTest, int] = field(metadata={"all_keys": True})


def test_dataclass_dict_field_with_enum_keys() -> None:
    assert generate_ts_for_types(DataclassDictFieldWithEnumKeys, EnumTest) == dedent(
        """\
        export interface DataclassDictFieldWithEnumKeys {
          dict_field: Partial<{ [key in EnumTest]: number }>;
          dict_field_with_all_keys: { [key in EnumTest]: number };
        }

        export enum EnumTest {
          ONE = "ONE",
          TWO = "TWO",
        }
    """
    )


@dataclass
class Dependency:
    field: str


@dataclass
class DataclassTypeDependency:
    field: Dependency


def test_dataclass_type_dependency() -> None:
    assert generate_ts_for_types(DataclassTypeDependency, Dependency) == dedent(
        """\
        export interface DataclassTypeDependency {
          field: Dependency;
        }

        export interface Dependency {
          field: string;
        }
    """
    )


@dataclass
class DataclassMother:
    field_1: str


@dataclass
class DataclassChildren(DataclassMother):
    field_2: str


def test_dataclass_inheritance() -> None:
    assert generate_ts_for_types(DataclassChildren, DataclassMother) == dedent(
        """\
        export interface DataclassChildren extends DataclassMother {
          field_2: string;
        }

        export interface DataclassMother {
          field_1: string;
        }
    """
    )


@dataclass
class DataclassPolymorphismMother(ABC):
    type: str = field(metadata={"discriminator": True})


@dataclass(kw_only=True)
class DataclassPolymorphismChildrenOne(DataclassPolymorphismMother):
    type: str = field(default="One")
    field_1: str


@dataclass(kw_only=True)
class DataclassPolymorphismChildrenTwo(DataclassPolymorphismMother):
    type: str = field(default="Two")
    field_2: str


def test_dataclass_polymorphism() -> None:
    assert generate_ts_for_types(
        DataclassPolymorphismMother,
        DataclassPolymorphismChildrenOne,
        DataclassPolymorphismChildrenTwo,
    ) == dedent(
        """\
        export interface DataclassPolymorphismMother {
          type: string;
        }

        export interface DataclassPolymorphismChildrenOne extends DataclassPolymorphismMother {
          type: "One";
          field_1: string;
        }

        export interface DataclassPolymorphismChildrenTwo extends DataclassPolymorphismMother {
          type: "Two";
          field_2: string;
        }
    """
    )
