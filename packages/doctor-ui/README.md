# Doctor UI

This project has been bootstrapped with React + TypeScript + Vite.

## 1. Getting Started

### A. Prerequisites

- Node.js 20+
- Basic knowledge of TypeScript, React and Vite

### B. Install dependencies

```bash
npm install
```

### C. Setting up the environment

You will find a `.env` file with sane defaults at the root of the project.

To override the defaults or to add sensitive variables, you can create a `.env.local` file which will have precedence over the `.env` file.

You can also pass them directly through the CLI at build time and they will have precedence over the files.

Please refer to [Vite's documentation](https://vite.dev/guide/env-and-mode.html) for more information.

### D. Run the development server

```bash
npm run dev
```

## 2. Deployment

### A. Build the project

```bash
npm run build
```

The build will be outputted in the `dist` folder and can be served statically.
