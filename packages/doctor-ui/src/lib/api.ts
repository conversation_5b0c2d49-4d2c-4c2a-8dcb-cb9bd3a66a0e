import { Identifier } from "@/models/common";
import { Encounter } from "@/models/encounter";
import { HealthcareServiceId, Organization, OrganizationId } from "@/models/organization";
import { Patient, PatientId } from "@/models/patient";
import { QuestionnaireId, QuestionnaireResponse } from "@/models/questionnaire";
import { PractitionerView } from "@/models/view";
import { useOidcFetch } from "@axa-fr/react-oidc";
import { Attachment, DocumentReference } from "@/models/document_reference";

// Wrapper around the fetch function that adds the Authorization header
type OidcFetch = ReturnType<typeof useOidcFetch>["fetch"];

export const getEncounters = async (fetch: OidcFetch) => {
  const url = `${import.meta.env.VITE_API_URL}/encounter`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /encounter: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<Encounter[]>;
};

export const updateEncounter = async (fetch: OidcFetch, identifier: Identifier, instructions: Array<Attachment>) => {
  const queryParams = new URLSearchParams({
    identifier: `${identifier.system}|${identifier.value}`,
  });
  const url = `${import.meta.env.VITE_API_URL}/encounter?${queryParams.toString()}`;
  const res = await fetch(url, {
    method: "PATCH",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ discharge: { instructions } }),
  });

  if (!res.ok) {
    const msg = `PATCH /encounter: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<Encounter>;
};

export const getPatient = async (fetch: OidcFetch, patientId: PatientId) => {
  const url = `${import.meta.env.VITE_API_URL}/patient/${patientId}`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /patient/${patientId}: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<Patient>;
};

export const getQuestionnaireResponses = async (
  fetch: OidcFetch,
  questionnaireId: QuestionnaireId,
  patientId: PatientId,
) => {
  const url = `${import.meta.env.VITE_API_URL}/questionnaire-response?questionnaire_id=${questionnaireId}&patient_id=${patientId}`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /questionnaire: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<QuestionnaireResponse[]>;
};

export const getOrganizations = async (fetch: OidcFetch) => {
  const url = `${import.meta.env.VITE_API_URL}/organization`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /organization: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<Organization[]>;
};

export const getOrganization = async (fetch: OidcFetch, organizationId: OrganizationId) => {
  const url = `${import.meta.env.VITE_API_URL}/organization/${organizationId}`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /organization/${organizationId}: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<Organization>;
};

export const getPractitionerView = async (fetch: OidcFetch) => {
  const url = `${import.meta.env.VITE_API_URL}/view`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /view/: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<PractitionerView>;
};

export const getDischargeInstructions = async (fetch: OidcFetch, healthcareServiceId: HealthcareServiceId) => {
  const url = `${import.meta.env.VITE_API_URL}/document_reference?healthcare_service_id=${healthcareServiceId}`;
  const res = await fetch(url);

  if (!res.ok) {
    const msg = `GET /document_reference: HTTP ${res.status} ${res.statusText}`;
    throw new Error(msg);
  }

  return res.json() as Promise<DocumentReference[]>;
};
