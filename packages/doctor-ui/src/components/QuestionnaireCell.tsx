import { getQuestionnaireResponses } from "@/lib/api";
import { useOidcFetch } from "@axa-fr/react-oidc";
import { QuestionnaireId } from "@/models/questionnaire";
import { Encounter } from "@/models/encounter";
import { useQuery } from "@tanstack/react-query";

interface QuestionnairesCellProps {
  encounter: Encounter;
  questionnaireId: QuestionnaireId;
}

export default function QuestionnairesCell({ encounter, questionnaireId }: QuestionnairesCellProps) {
  const { fetch } = useOidcFetch();
  const { data } = useQuery({
    queryKey: ["questionnaires", questionnaireId, encounter.subject],
    queryFn: () => getQuestionnaireResponses(fetch, questionnaireId, encounter.subject),
  });

  return <div>{data?.length ? "✅" : "N/A"}</div>;
}
