import ActionCell from "@/components/ActionCell";
import HealthcareServiceCell from "@/components/HealthcareServiceCell";
import PatientCell from "@/components/PatientCell";
import { getPractitionerView } from "@/lib/api";
import { HealthcareService, HealthcareServiceId } from "@/models/organization";
import { useOidcFetch } from "@axa-fr/react-oidc";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import ReportIcon from "@mui/icons-material/Report";
import SearchIcon from "@mui/icons-material/Search";
import Alert from "@mui/joy/Alert";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import CircularProgress from "@mui/joy/CircularProgress";
import Divider from "@mui/joy/Divider";
import FormControl from "@mui/joy/FormControl";
import FormLabel from "@mui/joy/FormLabel";
import IconButton from "@mui/joy/IconButton";
import Input from "@mui/joy/Input";
import Modal from "@mui/joy/Modal";
import ModalClose from "@mui/joy/ModalClose";
import ModalDialog from "@mui/joy/ModalDialog";
import Option from "@mui/joy/Option";
import Select from "@mui/joy/Select";
import Sheet from "@mui/joy/Sheet";
import Table from "@mui/joy/Table";
import Typography from "@mui/joy/Typography";
import { useQuery } from "@tanstack/react-query";
import * as React from "react";
import { useMemo, useState } from "react";
import { Encounter } from "@/models/encounter";

const renderFilters = (
  healthcareServices: HealthcareService[],
  selectedService: HealthcareServiceId | "all",
  setSelectedService: (service: HealthcareServiceId | "all") => void,
  encounters: Encounter[],
) => (
  <React.Fragment>
    <FormControl size="sm" sx={{ flex: 1 }}>
      <FormLabel>Services</FormLabel>
      <Select
        size="sm"
        placeholder="Filtrer par service"
        slotProps={{ button: { sx: { whiteSpace: "nowrap" } } }}
        value={selectedService.toString()}
        onChange={(_, value) => setSelectedService(value as HealthcareServiceId | "all")}
      >
        <Option key="all" value="all">
          Tous les services ({encounters.length})
        </Option>
        {healthcareServices.map((service) => (
          <Option key={service.healthcare_service_id} value={service.healthcare_service_id}>
            {service.name} ({encounters.filter((e) => e.service_type === service.healthcare_service_id).length})
          </Option>
        ))}
      </Select>
    </FormControl>
  </React.Fragment>
);

export default function EncounterTable() {
  const { fetch } = useOidcFetch();
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedService, setSelectedService] = useState<HealthcareServiceId | "all">("all");
  const { data, isLoading, isError, error } = useQuery({
    queryKey: [getPractitionerView.name],
    queryFn: () => getPractitionerView(fetch),
  });

  // Hook to filter the table based on search query
  const filteredData = useMemo(() => {
    if (!data) return [];
    const search = searchQuery.trim().toLowerCase();
    return data.encounters
      .filter((item) => {
        if (!search) return true;

        const patient = data.patients.find((p) => p.patient_id === item.subject);
        if (!patient) return false;

        const { name } = patient;
        const patientName = `${name.first_name} ${name.last_name}`.toLowerCase();

        return patientName.includes(search);
      })
      .filter((item) => {
        if (selectedService !== "all") return item.service_type === selectedService;
        return true;
      });
  }, [data, searchQuery, selectedService]);

  return (
    <React.Fragment>
      <Sheet sx={{ display: { xs: "flex", sm: "none" }, my: 1, gap: 1 }}>
        <Input
          size="sm"
          placeholder="Rechercher..."
          startDecorator={<SearchIcon />}
          sx={{ flexGrow: 1 }}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <IconButton size="sm" variant="outlined" color="neutral" onClick={() => setOpen(true)}>
          <FilterAltIcon />
        </IconButton>
        <Modal open={open} onClose={() => setOpen(false)}>
          <ModalDialog aria-labelledby="filter-modal" layout="fullscreen">
            <ModalClose />
            <Typography id="filter-modal" level="h2">
              Filtres
            </Typography>
            <Divider sx={{ my: 2 }} />
            <Sheet sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              {renderFilters(
                data?.healthcare_service ?? [],
                selectedService,
                setSelectedService,
                data?.encounters ?? [],
              )}
              <Button color="primary" onClick={() => setOpen(false)}>
                Appliquer
              </Button>
            </Sheet>
          </ModalDialog>
        </Modal>
      </Sheet>
      <Box
        sx={{
          borderRadius: "sm",
          py: 2,
          display: { xs: "none", sm: "flex" },
          flexWrap: "wrap",
          gap: 1.5,
          "& > *": {
            minWidth: { xs: "120px", md: "160px" },
          },
        }}
      >
        <FormControl sx={{ flex: 1 }} size="sm">
          <FormLabel>Recherche</FormLabel>
          <Input
            size="sm"
            placeholder="Rechercher un patient..."
            startDecorator={<SearchIcon />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </FormControl>
        {renderFilters(data?.healthcare_service ?? [], selectedService, setSelectedService, data?.encounters ?? [])}
      </Box>

      {/* Loading state */}
      {isLoading && <CircularProgress color="neutral" determinate={false} size="lg" value={55} variant="soft" />}

      {/* Error state */}
      {isError && (
        <Alert sx={{ alignItems: "flex-start" }} startDecorator={<ReportIcon />} variant="soft" color="danger">
          <div>
            <div>Erreur</div>
            <Typography level="body-sm" color="danger">
              {error?.message}
            </Typography>
          </div>
        </Alert>
      )}

      {/* Success state */}
      {!isLoading && !isError && (
        <Sheet
          variant="outlined"
          sx={{
            width: "100%",
            borderRadius: "sm",
            flexShrink: 1,
            overflow: "auto",
            minHeight: 0,
          }}
        >
          {filteredData.length === 0 && (
            <Alert sx={{ alignItems: "flex-start" }} startDecorator={<ReportIcon />} variant="soft" color="warning">
              <div>Aucun enregistrement trouvé.</div>
            </Alert>
          )}
          {filteredData.length > 0 && (
            <Table stickyHeader hoverRow>
              <thead>
                <tr>
                  <th>Patient</th>
                  <th>Arrivée</th>
                  <th>Sortie</th>
                  <th>Service</th>
                  <th>Consignes de sortie</th>
                </tr>
              </thead>
              <tbody>
                {filteredData.map((item) => (
                  <tr key={item.encounter_id}>
                    <td>
                      <PatientCell patient={data?.patients.find((p) => p.patient_id === item.subject)} />
                    </td>
                    <td>
                      <Typography level="body-xs">{new Date(item.actual_period.start).toLocaleDateString()}</Typography>
                    </td>
                    <td>
                      <Typography level="body-xs">
                        {item.actual_period.end ? new Date(item.actual_period.end).toLocaleDateString() : null}
                      </Typography>
                    </td>
                    <td>
                      <HealthcareServiceCell
                        healthcareServiceId={item.service_type}
                        healthcareServices={data?.healthcare_service ?? []}
                      />
                    </td>
                    <td>
                      <ActionCell encounter={item} healthcareServices={data?.healthcare_service ?? []} />
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Sheet>
      )}
    </React.Fragment>
  );
}
