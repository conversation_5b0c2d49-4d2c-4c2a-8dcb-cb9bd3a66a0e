import LogoutRoundedIcon from "@mui/icons-material/LogoutRounded";
import User from "@mui/icons-material/ContactEmergency";
import Avatar from "@mui/joy/Avatar";
import Box from "@mui/joy/Box";
import Divider from "@mui/joy/Divider";
import GlobalStyles from "@mui/joy/GlobalStyles";
import IconButton from "@mui/joy/IconButton";
import List from "@mui/joy/List";
import ListItem from "@mui/joy/ListItem";
import ListItemButton, { listItemButtonClasses } from "@mui/joy/ListItemButton";
import ListItemContent from "@mui/joy/ListItemContent";
import Sheet from "@mui/joy/Sheet";
import Typography from "@mui/joy/Typography";
import logo from "@/assets/logo.png";
import { closeSidebar } from "@/lib/utils";
import { OidcUserStatus, useOidc, useOidcUser } from "@axa-fr/react-oidc";
import ColorSchemeToggle from "./ColorSchemeToggle";

function DisplayUserInfo() {
  const { oidcUser, oidcUserLoadingState } = useOidcUser();
  const { logout } = useOidc();

  switch (oidcUserLoadingState) {
    case OidcUserStatus.Loading:
      return <p>User Information are loading</p>;
    case OidcUserStatus.Unauthenticated:
      return <p>you are not authenticated</p>;
    case OidcUserStatus.LoadingError:
      return <p>Fail to load user information</p>;
  }
  return (
    <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
      <Avatar variant="outlined" size="sm" />
      <Box sx={{ minWidth: 0, flex: 1 }}>
        <Typography level="title-sm">{oidcUser.name}</Typography>
        <Typography level="body-xs">{oidcUser.email}</Typography>
      </Box>
      <IconButton size="sm" variant="plain" color="neutral" onClick={() => logout()}>
        <LogoutRoundedIcon />
      </IconButton>
    </Box>
  );
}

export default function Sidebar() {
  return (
    <Sheet
      sx={{
        position: { xs: "fixed", lg: "sticky" },
        transform: {
          xs: "translateX(calc(100% * (var(--SideNavigation-slideIn, 0) - 1)))",
          lg: "none",
        },
        transition: "transform 0.4s, width 0.4s",
        zIndex: 20,
        height: "100dvh",
        width: "var(--Sidebar-width)",
        top: 0,
        p: 2,
        flexShrink: 0,
        display: "flex",
        flexDirection: "column",
        gap: 2,
        borderRight: "1px solid",
        borderColor: "divider",
      }}
    >
      <GlobalStyles
        styles={(theme) => ({
          ":root": {
            "--Sidebar-width": "220px",
            [theme.breakpoints.up("lg")]: {
              "--Sidebar-width": "240px",
            },
          },
        })}
      />
      <Box
        sx={{
          position: "fixed",
          zIndex: 9998,
          top: 0,
          left: 0,
          width: "100vw",
          height: "100vh",
          opacity: "var(--SideNavigation-slideIn)",
          backgroundColor: "var(--joy-palette-background-backdrop)",
          transition: "opacity 0.4s",
          transform: {
            xs: "translateX(calc(100% * (var(--SideNavigation-slideIn, 0) - 1) + var(--SideNavigation-slideIn, 0) * var(--Sidebar-width, 0px)))",
            lg: "translateX(-100%)",
          },
        }}
        onClick={() => closeSidebar()}
      />
      <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
        <img src={logo} alt="Urgences Chrono Logo" style={{ width: "24px", height: "24px" }} />
        <Typography level="title-md">Urgences Chrono</Typography>
        <ColorSchemeToggle sx={{ ml: "auto", display: { xs: "none", lg: "block" } }} />
      </Box>
      <Box
        sx={{
          minHeight: 0,
          overflow: "hidden auto",
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
          [`& .${listItemButtonClasses.root}`]: {
            gap: 1.5,
          },
        }}
      >
        <List
          size="sm"
          sx={{
            gap: 1,
            "--List-nestedInsetStart": "30px",
            "--ListItem-radius": (theme) => theme.vars.radius.sm,
          }}
        >
          <ListItem>
            <ListItemButton selected>
              <User />
              <ListItemContent>
                <Typography level="title-sm">Séjours hospitaliers</Typography>
              </ListItemContent>
            </ListItemButton>
          </ListItem>
        </List>
      </Box>
      <Divider />
      <DisplayUserInfo />
    </Sheet>
  );
}
