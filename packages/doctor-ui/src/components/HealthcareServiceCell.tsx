import Typography from "@mui/joy/Typography/Typography";
import Chip from "@mui/joy/Chip";
import { HealthcareService, HealthcareServiceId } from "@/models/organization";

interface HealthcareServiceCellProps {
  healthcareServiceId: HealthcareServiceId;
  healthcareServices: HealthcareService[];
}

export default function HealthcareServiceCell({ healthcareServiceId, healthcareServices }: HealthcareServiceCellProps) {
  // As of today, there's no way to get the service name from the API.
  // @todo Improve this once we have a way to get the service name from the API.

  return (
    <Typography level="body-xs">
      <Chip size="md" variant="soft">
        {healthcareServices.find((service) => service.healthcare_service_id === healthcareServiceId)?.name}
      </Chip>
    </Typography>
  );
}
