import { Patient } from "@/models/patient";
import Box from "@mui/joy/Box";
import Typography from "@mui/joy/Typography";

interface PatientBoxProps {
  patient: Patient | undefined;
}

export default function PatientCell({ patient }: PatientBoxProps) {
  if (patient === undefined) {
    return null;
  }

  const birthDate = new Date(patient.birth_date).toLocaleDateString();
  const fullName = `${patient.name.first_name} ${patient.name.last_name}`;
  return (
    <Box sx={{ display: "flex", gap: 2, alignItems: "center" }}>
      <div>
        <Typography level="body-xs" fontWeight="bold">
          {fullName}
        </Typography>
        <Typography level="body-xs">{birthDate}</Typography>
      </div>
    </Box>
  );
}
