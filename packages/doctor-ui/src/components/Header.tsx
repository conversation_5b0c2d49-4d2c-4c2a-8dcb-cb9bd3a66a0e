import logo from "@/assets/logo.png";
import { toggleSidebar } from "@/lib/utils";
import MenuIcon from "@mui/icons-material/Menu";
import Box from "@mui/joy/Box";
import GlobalStyles from "@mui/joy/GlobalStyles";
import IconButton from "@mui/joy/IconButton";
import Sheet from "@mui/joy/Sheet";
import Typography from "@mui/joy/Typography";
import ColorSchemeToggle from "./ColorSchemeToggle";

export default function Header() {
  return (
    <Sheet
      sx={{
        display: { xs: "flex", lg: "none" },
        alignItems: "center",
        justifyContent: "space-between",
        position: "fixed",
        top: 0,
        width: "100vw",
        height: "var(--Header-height)",
        zIndex: 10,
        p: 2,
        gap: 1,
        borderBottom: "1px solid",
        borderColor: "background.level1",
        boxShadow: "sm",
      }}
    >
      <GlobalStyles
        styles={(theme) => ({
          ":root": {
            "--Header-height": "52px",
            [theme.breakpoints.up("lg")]: {
              "--Header-height": "0px",
            },
          },
        })}
      />
      <IconButton onClick={() => toggleSidebar()} variant="outlined" color="neutral" size="sm">
        <MenuIcon />
      </IconButton>
      <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
        <img src={logo} alt="Urgences Chrono Logo" style={{ width: "24px", height: "24px" }} />
        <Typography level="title-md">Urgences Chrono</Typography>
      </Box>
      <ColorSchemeToggle />
    </Sheet>
  );
}
