import { getDischargeInstructions, getPractitionerView, updateEncounter } from "@/lib/api";
import { Attachment } from "@/models/document_reference";
import { Encounter } from "@/models/encounter";
import { HealthcareService } from "@/models/organization";
import CheckRoundedIcon from "@mui/icons-material/CheckRounded";
import EditIcon from "@mui/icons-material/Edit";
import ReportIcon from "@mui/icons-material/Report";
import Alert from "@mui/joy/Alert";
import Box from "@mui/joy/Box";
import Button from "@mui/joy/Button";
import Checkbox from "@mui/joy/Checkbox";
import Chip from "@mui/joy/Chip";
import CircularProgress from "@mui/joy/CircularProgress";
import DialogContent from "@mui/joy/DialogContent";
import DialogTitle from "@mui/joy/DialogTitle";
import IconButton from "@mui/joy/IconButton";
import Modal from "@mui/joy/Modal";
import ModalDialog from "@mui/joy/ModalDialog";
import Sheet from "@mui/joy/Sheet";
import Snackbar from "@mui/joy/Snackbar";
import Stack from "@mui/joy/Stack";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
interface ActionCellProps {
  encounter: Encounter;
  healthcareServices: HealthcareService[];
}

export default function ActionCell({ encounter, healthcareServices }: ActionCellProps) {
  const queryClient = useQueryClient();
  const [modalOpen, setModalOpen] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  // Fetch the discharge instructions for the current service type
  const { data, isLoading, isError, error } = useQuery({
    queryKey: [getDischargeInstructions.name, encounter.service_type],
    queryFn: () => getDischargeInstructions(fetch, encounter.service_type),
    initialData: [],
    enabled: modalOpen,
  });

  // Update the encounter with the selected instructions
  const updateInstructions = useMutation({
    mutationFn: (selectedOptions: Attachment[]) => updateEncounter(fetch, encounter.identifier[0], selectedOptions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [getPractitionerView.name] });
      setModalOpen(false);
      setSnackbarOpen(true);
    },
    onError: () => {
      alert("Une erreur est survenue lors de la mise à jour des consignes de sortie.");
    },
  });

  return (
    <>
      {encounter.discharge?.instructions.length ? (
        <Stack direction="row" spacing={1} alignItems="center">
          <Chip variant="soft" size="md" startDecorator={<CheckRoundedIcon />} color="success">
            Envoyées
          </Chip>
          <IconButton variant="plain" onClick={() => setModalOpen(true)}>
            <EditIcon />
          </IconButton>
        </Stack>
      ) : (
        <Button size="sm" onClick={() => setModalOpen(true)}>
          Consignes
        </Button>
      )}

      <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
        <ModalDialog>
          <DialogTitle>Consignes de sortie</DialogTitle>
          <DialogContent>
            Spécifiques au service :{" "}
            {healthcareServices.find((s) => s.healthcare_service_id === encounter.service_type)?.name}.
          </DialogContent>

          {/* Loading state */}
          {isLoading && <CircularProgress color="neutral" determinate={false} size="sm" variant="soft" />}

          {/* Error state */}
          {isError && (
            <Alert sx={{ alignItems: "flex-start" }} startDecorator={<ReportIcon />} variant="soft" color="warning">
              <div>Une erreur est survenue lors de la récupération des consignes de sortie.</div>
              <div>{error.message}</div>
            </Alert>
          )}

          {/* No data state */}
          {!isLoading && !isError && data.length === 0 && (
            <Alert sx={{ alignItems: "flex-start" }} startDecorator={<ReportIcon />} variant="soft" color="warning">
              <div>Aucune consigne de sortie trouvée pour ce service.</div>
            </Alert>
          )}

          {/* Normal state */}
          {!isLoading && !isError && data.length > 0 && (
            <form
              onSubmit={(event: React.FormEvent<HTMLFormElement>) => {
                event.preventDefault();
                const formData = new FormData(event.currentTarget);
                const selectedOptions = formData.getAll("options[]");
                updateInstructions.mutate(
                  data
                    .filter((instruction) => selectedOptions.includes(instruction.id))
                    .map((instruction) => ({ id: instruction.id, name: instruction.description })),
                );
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 1,
                  "& > div": { p: 2, borderRadius: "md", display: "flex" },
                }}
              >
                {data.map((instruction) => (
                  <Sheet variant="outlined" key={instruction.id}>
                    <Checkbox name="options[]" value={instruction.id} label={instruction.description} overlay />
                  </Sheet>
                ))}
              </Box>
              <Button type="submit" sx={{ mt: 2, float: "right" }}>
                Valider
              </Button>
            </form>
          )}
        </ModalDialog>
      </Modal>

      <Snackbar
        autoHideDuration={4000}
        open={snackbarOpen}
        onClose={() => setSnackbarOpen(false)}
        variant="soft"
        color="success"
      >
        Consignes de sortie enregistrées.
      </Snackbar>
    </>
  );
}
