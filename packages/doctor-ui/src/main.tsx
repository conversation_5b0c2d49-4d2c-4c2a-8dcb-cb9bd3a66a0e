import { Oid<PERSON><PERSON><PERSON>ider } from "@axa-fr/react-oidc";
import { StyledEngineProvider } from "@mui/joy/styles";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider, createRouter } from "@tanstack/react-router";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";

// Import the generated route tree
import { routeTree } from "./routeTree.gen";

// Create a new router instance
const router = createRouter({ routeTree });

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const queryClient = new QueryClient();

// This configuration use hybrid mode
// ServiceWorker are used if available (more secure) else tokens are given to the client
// You need to give inside your code the "access_token" when using fetch
const oidcConfig = {
  client_id: import.meta.env.VITE_CLIENT_ID,
  redirect_uri: window.location.origin + "/authentication/callback",
  scope: import.meta.env.VITE_SCOPE,
  authority: import.meta.env.VITE_AUTHORITY,
  service_worker_only: ["1", "true"].includes(
    import.meta.env.VITE_SERVICE_WORKER_ONLY,
  ),
  demonstrating_proof_of_possession: ["1", "true"].includes(
    import.meta.env.VITE_DEMONSTRATING_PROOF_OF_POSSESSION,
  ),
};

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <StyledEngineProvider injectFirst>
        <OidcProvider configuration={oidcConfig}>
          <RouterProvider router={router} />
        </OidcProvider>
      </StyledEngineProvider>
    </QueryClientProvider>
  </StrictMode>,
);
