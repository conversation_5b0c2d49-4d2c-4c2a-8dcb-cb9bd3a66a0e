/* AUTOGENERATED: DO NOT EDIT */
import { OrganizationId } from './organization';

/**
 * A practitioner is a healthcare professional.
 * The practitioner information is coming from the SSO.
 */
export interface Practitioner {
  /** The practitioner's unique identifier */
  practitioner_id: PractitionerId;

  /** organization_id */
  organization_id: OrganizationId;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type PractitionerId = string & { readonly __tag: unique symbol };

/** Creates a PractitionerId from a string. */
export const practitionerId = (value: string) => value as PractitionerId;
