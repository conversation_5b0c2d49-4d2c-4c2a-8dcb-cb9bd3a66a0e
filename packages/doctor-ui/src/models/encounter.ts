/* AUTOGENERATED: DO NOT EDIT */
import { Identifier, Period, UnixTimestamp } from './common';
import { Attachment } from './document_reference';
import { HealthcareServiceId, OrganizationId } from './organization';
import { PatientId } from './patient';

export interface Destination {
  main_destination?: string | null;
  details?: string | null;
}

export interface Discharge {
  disposition?: 'home' | 'hospital' | 'nursing' | 'hospice' | 'other' | null;
  destination?: Destination | null;
  instructions: Array<Attachment>;
}

export type DischargeDisposition =
  | 'home'
  | 'hospital'
  | 'nursing'
  | 'hospice'
  | 'other';

export interface Encounter {
  share_id: ShareId;
  subject: PatientId;
  service_provider: OrganizationId;
  actual_period: Period;
  encounter_id: EncounterId;
  service_type: HealthcareServiceId;
  identifier: Array<Identifier>;
  planned_end_date?: UnixTimestamp | null;
  subject_consent_date?: UnixTimestamp | null;
  discharge?: Discharge | null;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type EncounterId = string & { readonly __tag: unique symbol };

/** Creates a EncounterId from a string. */
export const encounterId = (value: string) => value as EncounterId;

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type ShareId = string & { readonly __tag: unique symbol };

/** Creates a ShareId from a string. */
export const shareId = (value: string) => value as ShareId;
