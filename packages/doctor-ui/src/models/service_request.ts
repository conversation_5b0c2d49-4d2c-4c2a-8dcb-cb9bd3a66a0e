/* AUTOGENERATED: DO NOT EDIT */
import { ActivityDefinitionId } from './activity_definition';
import { Timing, UnixTimestamp } from './common';
import { EncounterId } from './encounter';

/**
 * A record of a request for service such as diagnostic investigations,
 * treatments, or operations to be performed.
 */
export interface ServiceRequest {
  service_request_id: ServiceRequestId;
  activity_definition_id: ActivityDefinitionId;
  authored_on: UnixTimestamp;
  encounter: EncounterId;
  encounter_timing: Timing;
  status:
    | 'not-yet-started'
    | 'in-progress'
    | 'waiting-for-results'
    | 'cancelled'
    | 'completed';
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type ServiceRequestId = string & { readonly __tag: unique symbol };

/** Creates a ServiceRequestId from a string. */
export const serviceRequestId = (value: string) => value as ServiceRequestId;

export type ServiceRequestStatus =
  | 'not-yet-started'
  | 'in-progress'
  | 'waiting-for-results'
  | 'cancelled'
  | 'completed';
