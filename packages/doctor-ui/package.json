{"name": "doctor-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@axa-fr/react-oidc": "^7.22.32", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.1.0", "@mui/icons-material": "^6.1.4", "@mui/joy": "^5.0.0-beta.48", "@mui/material": "^6.1.4", "@rollup/rollup-darwin-arm64": "^4.35.0", "@tanstack/react-query": "^5.59.15", "@tanstack/react-router": "^1.69.1", "@tanstack/react-table": "^8.20.5", "prettier": "^3.3.3", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.11.1", "@tanstack/router-devtools": "^1.69.1", "@tanstack/router-plugin": "^1.69.1", "@types/node": "^22.7.5", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.19"}}