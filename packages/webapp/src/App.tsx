/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Providers
import MantineProvider from '@/providers/MantineProvider';
import NotificationsProvider from '@/providers/NotificationsProvider';
import DatesProvider from '@/providers/DatesProvider';
import ModalsProvider from '@/providers/ModalsProvider';
import QueryClientProvider from '@/providers/QueryClientProvider';
import RouterProvider from '@/providers/RouterProvider';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

export default function App() {
  return (
    <MantineProvider>
      <NotificationsProvider />
      <DatesProvider>
        <ModalsProvider>
          <QueryClientProvider>
            <RouterProvider />
          </QueryClientProvider>
        </ModalsProvider>
      </DatesProvider>
    </MantineProvider>
  );
}
