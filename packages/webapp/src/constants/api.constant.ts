/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Types
import { ApiRequestMethod } from '@/types/api';

// Configs
import i18next from '@/configs/organization-i18n.config';

/* -------------------------------------------------------------------------- */
/*                                 Constants                                  */
/* -------------------------------------------------------------------------- */

type ApiErrors = {
  [route: string]: Partial<
    Record<ApiRequestMethod, Record<number, { fr: string }>>
  >;
};

const apiErrors: ApiErrors = {
  '/login/access-token': {
    POST: {
      401: { fr: i18next.t('SIGN_IN_INFORMATION_IS_INCORRECT') },
      404: { fr: i18next.t('SIGN_IN_INFORMATION_IS_INCORRECT') },
      422: { fr: i18next.t('SIGN_IN_INFORMATION_IS_INCORRECT') },
    },
  },
};

export { apiErrors };
