/* AUTOGENERATED: DO NOT EDIT */

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type ISODate = string & { readonly __tag: unique symbol };

/** Creates a ISODate from a string. */
export const iSODate = (value: string) => value as ISODate;

export interface Identifier {
  value: string;
  system: string;
  use: string;
}

export interface Period {
  start?: UnixTimestamp | null;
  end?: UnixTimestamp | null;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type Principal = string & { readonly __tag: unique symbol };

/** Creates a Principal from a string. */
export const principal = (value: string) => value as Principal;

export interface Timing {
  planned_end_date: UnixTimestamp;
  wait_count: number;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type UnixTimestamp = number & { readonly __tag: unique symbol };

/** Creates a UnixTimestamp from a number. */
export const unixTimestamp = (value: number) => value as UnixTimestamp;
