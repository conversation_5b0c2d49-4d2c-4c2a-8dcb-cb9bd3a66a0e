/* AUTOGENERATED: DO NOT EDIT */
import { UnixTimestamp } from "./common";
import { PatientId } from "./patient";

export interface AnswerOption {
  value_integer: number;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type LinkId = string & { readonly __tag: unique symbol };

/** Creates a LinkId from a string. */
export const linkId = (value: string) => value as LinkId;

export interface QuestionResponseItem {
  link_id: LinkId;
  answer: Array<AnswerOption>;
}

export interface Questionnaire {
  questionnaire_id: QuestionnaireId;
  name: string;
  description: string;
  item: Array<QuestionnaireItem>;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type QuestionnaireId = string & { readonly __tag: unique symbol };

/** Creates a QuestionnaireId from a string. */
export const questionnaireId = (value: string) => value as QuestionnaireId;

export interface QuestionnaireItem {
  link_id: LinkId;
  text: string;
  type: "integer";
  answer_option: Array<AnswerOption>;
  initial: Array<AnswerOption>;
}

export interface QuestionnaireResponse {
  questionnaire_id: QuestionnaireId;
  authored: UnixTimestamp;
  subject: PatientId;
  item: Array<QuestionResponseItem>;
}
