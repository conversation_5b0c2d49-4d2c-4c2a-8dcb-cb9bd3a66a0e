/* AUTOGENERATED: DO NOT EDIT */
import { Encounter } from "./encounter";
import { HealthcareService, Organization } from "./organization";
import { Patient } from "./patient";
import { Questionnaire } from "./questionnaire";
import { ServiceRequest } from "./service_request";

export interface PatientView {
  encounter: Encounter;
  organization: Organization;
  healthcare_service: Array<HealthcareService>;
  service_request: Array<ServiceRequest>;
  questionnaire: Array<Questionnaire>;
}

export interface PractitionerView {
  organization: Organization;
  encounters: Array<Encounter>;
  patients: Array<Patient>;
  healthcare_service: Array<HealthcareService>;
}
