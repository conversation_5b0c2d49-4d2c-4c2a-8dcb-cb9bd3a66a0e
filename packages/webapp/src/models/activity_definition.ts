/* AUTOGENERATED: DO NOT EDIT */

export interface ActivityDefinition {
  activity_definition_id: ActivityDefinitionId;
  name: string;
  description: string;
  shareable: boolean;
  default_wait_time: number | null;
  send_notification: Array<SendNotification> | SendNotification | null;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type ActivityDefinitionId = string & { readonly __tag: unique symbol };

/** Creates a ActivityDefinitionId from a string. */
export const activityDefinitionId = (value: string) => value as ActivityDefinitionId;

export interface SendNotification {
  trigger: number | string;
  message: string;
}
