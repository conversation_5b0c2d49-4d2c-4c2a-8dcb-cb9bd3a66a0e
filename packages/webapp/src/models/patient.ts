/* AUTOGENERATED: DO NOT EDIT */
import { ISODate } from "./common";

export interface Contact {
  relationship?: Array<"relative"> | null;
  telecom: Array<Telecom>;
}

export interface HumanName {
  first_name: string;
  last_name: string;
}

export interface Patient {
  patient_id: PatientId;
  name: <PERSON><PERSON><PERSON>;
  birth_date: ISODate;
  phone?: string | null;
  contact: Array<Contact>;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type PatientId = string & { readonly __tag: unique symbol };

/** Creates a PatientId from a string. */
export const patientId = (value: string) => value as PatientId;

export interface Telecom {
  system: "sms";
  value: string;
}
