/* AUTOGENERATED: DO NOT EDIT */
import { Role } from './authentication';
import { UnixTimestamp } from './common';
import { EncounterId } from './encounter';
import { PatientId } from './patient';

export interface ConnexionAttemptDetails {
  success: boolean;
  failure_reason?: string | null;
}

export interface Event {
  timestamp: UnixTimestamp;
  event_type: EventType;
  encounter_id: EncounterId;
  patient_id: PatientId | null;
  role?: Role | null;
  text_message_details?: TextMessage | null;
  connexion_details?: ConnexionAttemptDetails | null;
}

export enum EventType {
  ENCOUNTER_CREATED = 'ENCOUNTER_CREATED',
  TEXT_MESSAGE_SENT = 'TEXT_MESSAGE_SENT',
  CONNEXION_ATTEMPT = 'CONNEXION_ATTEMPT',
  LOGIN_PAGE_VIEWED = 'LOGIN_PAGE_VIEWED',
  SENT_SHARE_LINK = 'SENT_SHARE_LINK',
  ACCEPTED_TOS = 'ACCEPTED_TOS',
}

export interface TextMessage {
  text: string;
  recipient: string;
  text_type: TextType;
}

export enum TextType {
  POST_DISCHARGE_SMS = 'POST_DISCHARGE_SMS',
  LOGIN_SMS = 'LOGIN_SMS',
  TRIGGERED_SMS = 'TRIGGERED_SMS',
  SATISFACTION_SURVEY = 'SATISFACTION_SURVEY',
}

/** Payload for unauthenticated event logging requiring only an encounter ID. */
export interface UnauthLogPayload {
  encounter_id: EncounterId;
}
