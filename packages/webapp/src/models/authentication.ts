/* AUTOGENERATED: DO NOT EDIT */
import { EncounterId } from "./encounter";
import { OrganizationId } from "./organization";
import { PatientId } from "./patient";

export enum Role {
  PRACTITIONER = "PRACTITIONER",
  PATIENT = "PATIENT",
  RELATIVE = "RELATIVE",
  SERVICE = "SERVICE",
}

export interface Token {
  access_token: string;
  token_type: string;
}

export interface TokenData {
  patient_id: PatientId;
  encounter_id: EncounterId;
  close_relative: boolean;
}

/** A user is a practitioner, a patient or a patient's close relative. */
export interface User {
  /**
   * The user's unique identifier
   * This is either a practitioner ID (coming from SSO) or an encounter ID
   */
  user_id: string;

  /** The user's role */
  role: Role;

  /** The practitioner's organization (e.g. chu-limoges) */
  organization_id?: OrganizationId | null;
}
