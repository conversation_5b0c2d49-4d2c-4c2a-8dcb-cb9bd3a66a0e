/* AUTOGENERATED: DO NOT EDIT */
import { ActivityDefinition } from "./activity_definition";
import { QuestionnaireId } from "./questionnaire";

export interface HealthcareService {
  /** Unique identifier for the healthcare service */
  healthcare_service_id: HealthcareServiceId;

  /** Name of the different "filières" (e.g. "Ambulatoire", "Couché", "Pediatric", etc.) */
  name: string;

  /** Description of the healthcare service */
  description: string;

  /** Unique identifier for the healthcare service */
  provided_by: OrganizationId;

  /** The service in which this service is offered */
  offered_in?: HealthcareServiceId | null;

  /** List of activity that can be performed in this healthcare service */
  activity_definition: Array<ActivityDefinition>;

  /** Questionnaire to be filled by the patient */
  questionnaire: Array<QuestionnaireId>;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type HealthcareServiceId = string & { readonly __tag: unique symbol };

/** Creates a HealthcareServiceId from a string. */
export const healthcareServiceId = (value: string) => value as HealthcareServiceId;

export interface Organization {
  organization_id: OrganizationId;
  name: string;
  description: string;
  patient_session_timeout_minutes: number;
}

/** This is a "NewType" (see https://kubyshkin.name/posts/newtype-in-typescript/). */
export type OrganizationId = string & { readonly __tag: unique symbol };

/** Creates a OrganizationId from a string. */
export const organizationId = (value: string) => value as OrganizationId;
