/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Outlet } from '@tanstack/react-router';

// Components
import { Header } from '@/components/Header';

// Styles
import classes from './PublicLayout.module.css';

/* -------------------------------------------------------------------------- */
/*                                   Layout                                   */
/* -------------------------------------------------------------------------- */

export default function PublicLayout() {
  return (
    <main className={classes['page-root']}>
      <Header />
      <Outlet />
    </main>
  );
}
