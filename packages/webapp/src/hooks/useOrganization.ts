/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useEffect, useState } from 'react';

// Types
import { OrganizationId } from '@/models/organization';

// Helpers
import {
  extractOrganizationFromEncounterId,
  getLogoPathWithFallback,
} from '@/helpers/organization.helper';

// Configs
import { setCurrentOrganization } from '@/configs/organization-i18n.config';

// Stores
import usePpuStore from '@/stores/ppu';

/* -------------------------------------------------------------------------- */
/*                                    Hook                                     */
/* -------------------------------------------------------------------------- */

export function useOrganization() {
  const [organizationId, setOrganizationId] = useState<OrganizationId | null>(
    null
  );
  const [logoPath, setLogoPath] = useState<string | null>(null);
  const { authenticatedUser } = usePpuStore();

  useEffect(() => {
    const initializeOrganization = async () => {
      let orgId: OrganizationId | null = null;

      if (authenticatedUser) {
        // If user is authenticated, we can try to extract from authId (encounter_id or share_id)
        try {
          orgId = extractOrganizationFromEncounterId(authenticatedUser.authId);
        } catch (error) {
          console.warn('Failed to extract organization from authId:', error);
        }
      } else {
        // If not authenticated, try to extract from current URL
        // This handles the sign-in page case where auth-id is in the URL
        const pathParts = window.location.pathname.split('/');
        const authIdIndex =
          pathParts.findIndex((part) => part === 'sign-in') + 1;

        if (authIdIndex > 0 && authIdIndex < pathParts.length) {
          const authId = pathParts[authIdIndex];
          try {
            orgId = extractOrganizationFromEncounterId(authId);
          } catch (error) {
            console.warn('Failed to extract organization from URL:', error);
          }
        }
      }

      // Update organization state and translations
      setOrganizationId(orgId);
      setCurrentOrganization(orgId);
      setLogoPath(getLogoPathWithFallback(orgId || undefined));
    };

    initializeOrganization();
  }, [authenticatedUser]);

  return {
    organizationId,
    logoPath,
  };
}

export default useOrganization;
