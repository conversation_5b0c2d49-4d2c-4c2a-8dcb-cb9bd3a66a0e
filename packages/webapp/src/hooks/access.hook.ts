/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';

// Configs
import i18next from '@/configs/organization-i18n.config';
import { EncounterService } from '../pages/public/SignIn/services/encounter.service';

/* -------------------------------------------------------------------------- */
/*                                  Post Hook                                 */
/* -------------------------------------------------------------------------- */

const useDeleteAccess = () =>
  useMutation({
    mutationFn: async (encounterId: string) => {
      const encounterService = new EncounterService();
      await encounterService.resetShare(encounterId);
    },
    onSuccess: () => {
      notifications.show({
        message: i18next.t('YOUR_ACCESS_HAS_BEEN_SUCCESSFULLY_DELETED'),
      });
    },
    onError: () => {
      notifications.show({
        message: i18next.t('AN_ERROR_OCCURED_WHILE_DELETING_ACCESS'),
        color: 'red',
      });
    },
  });

export { useDeleteAccess };
