import { EncounterId } from '@/models/encounter';
import { UnauthLogPayload } from '@/models/event';

// Config
import ppuApi from '@/configs/ppu-api.config';

/**
 * Logs the LOGIN_PAGE_VIEWED event (unauthenticated).
 * @param encounterId The encounter ID (or share ID) associated with the page view.
 */
export const logLoginPageViewed = async (
  encounterId: EncounterId
): Promise<void> => {
  const payload: UnauthLogPayload = {
    encounter_id: encounterId,
  };
  const url = '/events/log/login_page_viewed';

  try {
    await ppuApi.post(url, payload);
  } catch (error) {
    console.error('Error logging LOGIN_PAGE_VIEWED:', error);
  }
};

/**
 * Logs the ACCEPTED_TOS event (unauthenticated).
 * @param encounterId The encounter ID (or share ID) associated with the acceptance.
 */
export const logAcceptedTos = async (
  encounterId: EncounterId
): Promise<void> => {
  const payload: UnauthLogPayload = {
    encounter_id: encounterId,
  };
  const url = '/events/log/accepted_tos';

  try {
    await ppuApi.post(url, payload);
  } catch (error) {
    // Log error but don't throw
    console.error('Error logging ACCEPTED_TOS:', error);
  }
};

/**
 * Logs the SENT_SHARE_LINK event (authenticated).
 * This function assumes the user is authenticated and ppuApi will send the token.
 */
export const logSentShareLink = async (): Promise<void> => {
  const url = '/events/log/sent_share_link'; // No payload needed, backend uses auth context

  try {
    await ppuApi.post(url, {}); // Empty object as payload, or null if API expects no body
  } catch (error) {
    // Log error but don't throw
    console.error('Error logging SENT_SHARE_LINK:', error);
  }
};
