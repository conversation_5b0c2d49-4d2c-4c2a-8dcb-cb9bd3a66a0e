/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Config
import ppuApi from '@/configs/ppu-api.config';

// Helpers
import { makeUrl } from '@/helpers/api.helper';

// Types
import { PatientView } from '../models/view';

/* -------------------------------------------------------------------------- */
/*                               Service class                                */
/* -------------------------------------------------------------------------- */

type getViewArgs = {
  headers?: {
    Authorization?: string;
  };
};

class ViewService {
  get = async (args?: getViewArgs) => {
    const apiResponse = (await ppuApi.get(makeUrl({ path: `/view` }), {
      headers: args?.headers || undefined,
    })) as unknown;

    return apiResponse as PatientView;
  };
}

export { ViewService };
