/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { type QueryClient } from '@tanstack/react-query';
import { createRootRouteWithContext, Outlet } from '@tanstack/react-router';
import { lazy, Suspense } from 'react';

// Configs
import { env } from '@/configs/env.config';
import i18next from '@/configs/organization-i18n.config';

// Components
import { NotFound } from '@/components/NotFound';

// Types
import { User } from '@/types/user';

/* -------------------------------------------------------------------------- */
/*                                 Root Route                                 */
/* -------------------------------------------------------------------------- */

type AppRootContext = {
  queryClient: QueryClient;
  isAuthenticated: boolean;
  session: User | null;
};

const ReactQueryDevtools =
  env.nodeEnv === 'development'
    ? lazy(() =>
        // Lazy load in development
        import('@tanstack/react-query-devtools').then((res) => ({
          default: res.ReactQueryDevtools,
        }))
      )
    : () => null; // Render nothing

const TanStackRouterDevtools =
  env.nodeEnv === 'development'
    ? lazy(() =>
        // Lazy load in development
        import('@tanstack/router-devtools').then((res) => ({
          default: res.TanStackRouterDevtools,
          // For Embedded Mode
          // default: res.TanStackRouterDevtoolsPanel
        }))
      )
    : () => null; // Render nothing

export const Route = createRootRouteWithContext<AppRootContext>()({
  component: () => (
    <>
      <Outlet />
      <Suspense fallback={null}>
        <TanStackRouterDevtools />
        <ReactQueryDevtools />
      </Suspense>
    </>
  ),
  notFoundComponent: NotFound,
  errorComponent: () => (
    <NotFound data={{ data: { message: i18next.t('SOMETHING_WENT_WRONG') } }} />
  ),
});
