/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';

/* -------------------------------------------------------------------------- */
/*                                     Route                                  */
/* -------------------------------------------------------------------------- */

const searchParamsSchema = z.object({
  'is-share-link': z.boolean().optional(),
  'shared-by': z.string().optional(),
});

export const Route = createFileRoute('/_public/_auth/sign-in/$auth-id/')({
  validateSearch: searchParamsSchema,
});
