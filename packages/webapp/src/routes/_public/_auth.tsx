/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { createFileRoute, redirect } from '@tanstack/react-router';

// Stores
import usePpuStore from '@/stores/ppu';

/* -------------------------------------------------------------------------- */
/*                              Pathless Route                                */
/* -------------------------------------------------------------------------- */

export const Route = createFileRoute('/_public/_auth')({
  beforeLoad: async ({ context, location, params, search }) => {
    const { isAuthenticated } = context;

    // Case: user is authenticated
    if (isAuthenticated) {
      // Case: user wants to sign in
      if (location.pathname.includes('/sign-in/')) {
        const { 'auth-id': targetAuthId } = params as { 'auth-id': string };
        const authenticatedUserAuthId = await usePpuStore
          .getState()
          .getAuthId();

        // Case: User attempts to sign in with a different account than the currently connected one.
        if (targetAuthId !== authenticatedUserAuthId) {
          throw redirect({
            to: '/home',
            search: {
              'target-auth-id': targetAuthId,
              ...search,
            },
          });
        }
      }

      throw redirect({
        to: '/home',
      });
    }
  },
});
