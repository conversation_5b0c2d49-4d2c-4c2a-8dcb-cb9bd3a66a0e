/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { openContextModal } from '@mantine/modals';
import { createFileRoute, redirect } from '@tanstack/react-router';
import { z } from 'zod';

// Configs
import i18next from '@/configs/organization-i18n.config';

// Stores
import usePpuStore from '@/stores/ppu';

// Providers
import { router } from '@/providers/RouterProvider';

/* -------------------------------------------------------------------------- */
/*                                     Route                                  */
/* -------------------------------------------------------------------------- */

const searchParamsSchema = z.object({
  'target-auth-id': z.string().optional(),
});

export const Route = createFileRoute('/_private/home/')({
  validateSearch: searchParamsSchema,
  beforeLoad: async ({ search, context: { queryClient, session } }) => {
    // Redirect relatives (not original user) to /journey
    if (session && session.isOriginalUser === false) {
      throw redirect({
        to: '/journey',
      });
    }

    const { 'target-auth-id': targetAuthId, ...rest } = search;

    if (targetAuthId) {
      openContextModal({
        modal: 'confirmation',
        innerProps: {
          title: i18next.t('SIGN_OUT'),
          description: i18next.t(
            'YOU_ARE_ABOUT_TO_SIGN_OUT_FROM_YOUR_ACCOUNT_AND_CONTINUE_A_NEW_SESSION'
          ),
          onCancel: () => {
            // Eliminate target-auth-id from URL
            router.navigate({
              from: '/home',
              to: '/home',
            });
          },
          onConfirm: async () => {
            await queryClient.cancelQueries({ exact: false });
            await usePpuStore.getState().signOut();
            router.navigate({
              from: '/home',
              to: '/sign-in/$auth-id',
              params: { 'auth-id': targetAuthId },
              search: { ...rest },
            });
          },
        },
      });
    }
  },
});
