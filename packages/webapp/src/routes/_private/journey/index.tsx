/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { createFileRoute } from '@tanstack/react-router';

// Helpers

import { getShareAccessData } from '@/helpers/access.helper';

/* -------------------------------------------------------------------------- */
/*                                     Route                                  */
/* -------------------------------------------------------------------------- */

export const Route = createFileRoute('/_private/journey/')({
  loader: async ({ context }) => {
    const { session } = context;
    const shareAccessData = getShareAccessData({
      session,
    });

    return { shareAccessData };
  },
});
