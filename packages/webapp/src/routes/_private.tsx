/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { createFileRoute, notFound } from '@tanstack/react-router';

// Stores
import usePpuStore from '@/stores/ppu';

// Configs
import i18next from '@/configs/organization-i18n.config';

/* -------------------------------------------------------------------------- */
/*                              Pathless Route                                */
/* -------------------------------------------------------------------------- */

export const Route = createFileRoute('/_private')({
  beforeLoad: async ({ context }) => {
    const { isAuthenticated } = context;

    // Case: user is not authenticated => redirect user to not found page
    if (!isAuthenticated) {
      throw notFound({
        data: {
          message: i18next.t(
            'PLEASE_SIGN_IN_USING_THE_ACCESS_LINK_PROVIDED_BY_THE_HOSPITAL'
          ),
        },
      });
    }

    try {
      const user = await usePpuStore.getState().getSession();

      // Case: empty session data
      if (!user) throw Error('empty session data');

      return { session: user };
    } catch (error) {
      console.error('Error during session verification:', error);
      // Case: error during session verification
      usePpuStore.getState().signOut();
      throw notFound({
        data: {
          message: i18next.t(
            'PLEASE_SIGN_IN_USING_THE_ACCESS_LINK_PROVIDED_BY_THE_HOSPITAL'
          ),
        },
      });
    }
  },
});
