/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  Divider as NativeDivider,
  DividerProps as NativeDividerProps,
} from '@mantine/core';
import clsx from 'clsx';

// Styles
import classes from './Divider.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type DividerProps = NativeDividerProps;

function Divider(props: DividerProps) {
  const { className, ...rest } = props;

  return (
    <NativeDivider
      className={clsx(classes['divider-root'], className)}
      {...rest}
    />
  );
}

export { Divider };
