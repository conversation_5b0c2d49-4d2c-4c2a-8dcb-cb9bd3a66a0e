.header-root {
  position: fixed;
  z-index: 100;
  top: 0px;
  left: 0px;
  width: 100%;
  padding: 19px 20px;
  background: linear-gradient(
    180deg,
    var(--mantine-color-blue-0),
    var(--mantine-color-blue-1)
  );
}

.header-inner {
  width: 100%;
  height: 44.5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image {
  height: 100%;
  width: auto;
}

.image2 {
  height: 100%;
  width: auto;
  margin-top: 2.5px;
  margin-left: 4px;
}

/* Special positioning for MonParcours logo when alone */
.logo-container-single {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
