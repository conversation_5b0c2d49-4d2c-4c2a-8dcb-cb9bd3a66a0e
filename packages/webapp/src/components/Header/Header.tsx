/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  Image,
  Flex,
  ActionIcon,
  Divider,
  Burger,
  useMantineTheme,
  Anchor,
} from '@mantine/core';
import { IconPower, IconShare } from '@tabler/icons-react';
import { Link, useRouteContext } from '@tanstack/react-router';
import { useNavigate } from '@tanstack/react-router';
import { openContextModal } from '@mantine/modals';
import { useTranslation } from 'react-i18next';
import { useDisclosure } from '@mantine/hooks';

// Stores
import usePpuStore from '@/stores/ppu';

// Components
import { Menu } from '@/components/Menu';

// Helpers
import { shareAccess, ShareAccessData } from '@/helpers/access.helper';

// Hooks
import { useOrganization } from '@/hooks/useOrganization';

// Styles
import classes from './Header.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type HeaderProps = {
  hasShareButton?: boolean;
  shareAccessData?: ShareAccessData;
};

function Header(props: HeaderProps) {
  const { hasShareButton } = props;
  const { isAuthenticated, session } = useRouteContext({ strict: false });
  const { t } = useTranslation();
  const { signOut } = usePpuStore();
  const navigate = useNavigate();
  const [opened, { toggle, close }] = useDisclosure();
  const theme = useMantineTheme();
  const { logoPath } = useOrganization();

  const handleSignOut = async () => {
    openContextModal({
      modal: 'confirmation',
      innerProps: {
        title: t('SIGN_OUT'),
        description: t('YOU_ARE_ABOUT_TO_SIGN_OUT_FROM_YOUR_ACCOUNT'),
        onConfirm: async () => {
          await signOut();
          navigate({ to: '/home' });
        },
      },
    });
  };

  const handleShareAccess = () => {
    if (session) {
      shareAccess({ session });
    }
  };

  return (
    <header className={classes['header-root']}>
      {isAuthenticated ? (
        <>
          <Menu opened={opened} close={close} />
          <Flex
            className={classes['header-inner']}
            h={44.5}
            align="center"
            justify="space-between"
          >
            {logoPath ? (
              <Flex h="100%" columnGap={6} align="center">
                <Anchor component={Link} to="/journey" h="100%">
                  <Image src={logoPath} className={classes['image']} />
                </Anchor>
                <Anchor component={Link} to="/journey" h="100%">
                  <Image
                    src="/images/logo_monparcours-blanc-2.png"
                    className={classes['image2']}
                  />
                </Anchor>
              </Flex>
            ) : (
              <div className={classes['logo-container-single']}>
                <Anchor component={Link} to="/journey" h="100%">
                  <Image
                    src="/images/logo_monparcours-blanc-2.png"
                    className={classes['image2']}
                  />
                </Anchor>
              </div>
            )}

            <Flex columnGap={6} align="center">
              <Burger
                color={theme.white}
                opened={opened}
                onClick={toggle}
                aria-label="Toggle navigation"
              />
              <Divider orientation="vertical" color="lightgray" />
              {hasShareButton && session && session.isOriginalUser ? (
                <>
                  <ActionIcon
                    aria-label="Partager"
                    bg="transparent"
                    onClick={() => handleShareAccess()}
                  >
                    <IconShare />
                  </ActionIcon>
                  <Divider orientation="vertical" color="lightgray" />
                </>
              ) : (
                <></>
              )}
              <ActionIcon bg="transparent" onClick={() => handleSignOut()}>
                <IconPower />
              </ActionIcon>
            </Flex>
          </Flex>
        </>
      ) : (
        <Flex
          className={classes['header-inner']}
          h={44.5}
          align="center"
          justify="space-between"
        >
          {logoPath ? (
            <>
              <Image src={logoPath} className={classes['image']} />
              <Image
                src="/images/logo_monparcours-blanc-2.png"
                className={classes['image2']}
              />
            </>
          ) : (
            <div className={classes['logo-container-single']}>
              <Anchor component={Link} to="/journey" h="100%">
                <Image
                  src="/images/logo_monparcours-blanc-2.png"
                  className={classes['image2']}
                />
              </Anchor>
            </div>
          )}
        </Flex>
      )}
    </header>
  );
}

export { Header };
