.menu {
  height: 100%;
  padding: 0px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.menu-content {
  flex: 1;
  padding: 20px;
  background: transparent
    linear-gradient(
      180deg,
      hsla(204, 93%, 65%, 1) 0%,
      hsla(212, 49%, 46%, 1) 0%,
      hsla(240, 47%, 24%, 1) 100%
    )
    0% 0% no-repeat padding-box;
}

.menu-media {
  height: 37px;
}

/* Special positioning for MonParcours logo when alone */
.menu-media-single {
  height: 37px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.image {
  height: 100%;
  width: auto;
}

.image2 {
  height: 100%;
  width: auto;
  margin-top: 1px;
  margin-left: 4px;
}

.divider {
  height: 1px;
  background-color: var(--mantine-color-blue-8);
  border: 0px;
  opacity: 0.15;
}

.menu-link {
  cursor: pointer;
}

.icon-delete-wrapper {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--mantine-color-blue-1);
  display: flex;
  justify-content: center;
  align-items: center;
}
