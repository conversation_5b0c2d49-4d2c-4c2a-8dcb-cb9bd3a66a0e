/* ************************************************************************** */
/*                                Dependencies                                */
/* ************************************************************************** */

// lib dependencies
import {
  Drawer,
  Flex,
  Image,
  Burger,
  useMantineTheme,
  Text,
  rem,
  Divider,
  Stack,
  Group,
} from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { IconChevronDown, IconX as IconDelete } from '@tabler/icons-react';
import { openContextModal } from '@mantine/modals';
import { useRouteContext } from '@tanstack/react-router';
import { Link } from '@tanstack/react-router';
import { Fragment } from 'react';

// Components
import { Icons } from '@/components/Icons';

// Helpers
import { shareAccess } from '@/helpers/access.helper';

// Constants
import { externalLinks } from '@/constants/site.constants';

// Hooks
import { useDeleteAccess } from '@/hooks/access.hook';
import { useOrganization } from '@/hooks/useOrganization';
// Commented out as the related function is not currently used
// import { usePostQuestionnaireResponse } from '@/pages/private/Journey/hooks/questionnaire-response.hook';

// Styles
import classes from './Menu.module.css';
import { useView } from '../../pages/private/Journey/hooks/view.hook';

/* ************************************************************************** */
/*                                  Component                                 */
/* ************************************************************************** */

type MenuProps = {
  opened: boolean;
  close: () => void;
};

function Menu(props: MenuProps) {
  /* ********************************** HOOKS ********************************* */
  const { data } = useView();
  const { opened, close } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { session } = useRouteContext({ strict: false });
  const deleteAccess = useDeleteAccess();
  const { logoPath } = useOrganization();

  /* ******************************** CALLBACKS ******************************* */

  // Commented out as currently not used in the UI
  // const hangleConfirmPainValue = (painValue: number) => {
  //   postQuestionnaireResponse.mutate({
  //     body: {
  //       questionnaire_id: 'douleur',
  //       subject: 'douleur',
  //       item: [
  //         {
  //           answer: [
  //             {
  //               value_integer: painValue,
  //             },
  //           ],
  //           link_id: '',
  //         },
  //       ],
  //     },
  //   });
  // };

  /* **************************** RENDERING HELPERS *************************** */

  const renderDeleteAccessMenuGroup = () => {
    if (!session || !session.isOriginalUser) {
      return null;
    }

    return (
      <Fragment>
        <Stack px={13} gap={18}>
          <Group
            gap={19}
            wrap="nowrap"
            className={classes['menu-link']}
            onClick={() =>
              openContextModal({
                modal: 'confirmation',
                innerProps: {
                  title: t('DELETE_CONFIRMATION'),
                  description: t(
                    'ARE_YOU_SURE_YOU_WANT_TO_DELETE_YOUR_PERSONALIZED_JOURNEY'
                  ),
                  onConfirm: () => {
                    if (data?.response.encounter.encounter_id) {
                      deleteAccess.mutate(data.response.encounter.encounter_id);
                    }
                  },
                },
              })
            }
          >
            <div className={classes['icon-delete-wrapper']}>
              <IconDelete color={theme.white} size={14} />
            </div>
            <Text c={theme.white} fz={rem(15)} lh={rem(18)}>
              {t('DELETE_MY_PERSONALIZED_JOURNEY')}
            </Text>
          </Group>
        </Stack>

        <Divider className={classes['divider']} />
      </Fragment>
    );
  };

  /* ******************************** RENDERING ******************************* */
  return (
    <Drawer
      opened={opened}
      onClose={close}
      withCloseButton={false}
      classNames={{
        body: classes['menu'],
      }}
    >
      <Flex direction="column" className={classes['menu-content']} gap={28}>
        {/* ###### Menu media ###### */}
        <Flex
          align="center"
          justify="space-between"
          className={
            logoPath ? classes['menu-media'] : classes['menu-media-single']
          }
        >
          {logoPath ? (
            <Flex h="100%" columnGap={8} align="center">
              <Image src={logoPath} className={classes['image']} />
              <Image
                src="/images/logo_monparcours-blanc-2.png"
                className={classes['image2']}
              />
            </Flex>
          ) : (
            <Image
              src="/images/logo_monparcours-blanc-2.png"
              className={classes['image2']}
              h="100%"
            />
          )}

          <Burger color={theme.white} opened onClick={close} />
        </Flex>

        {/* ###### Menu Group ###### */}
        {/* <Stack px={13} pt={25} gap={25}>
          {session && session.isOriginalUser && (
            <Group
              className={classes['menu-link']}
              gap={21}
              wrap="nowrap"
              onClick={() =>
                openContextModal({
                  modal: 'pain',
                  innerProps: {
                    defaultPainValue: painValue,
                    onConfirm: (painValue) => hangleConfirmPainValue(painValue),
                  },
                })
              }
            >
              <IconPain color={theme.white} />
              <Text c={theme.white} fz={rem(15)} lh={rem(38)}>
                {t('ASSESS_MY_PAIN')}
              </Text>
            </Group>
          )}
        </Stack> */}

        <Divider className={classes['divider']} />

        {/* ###### Menu Group ###### */}
        <Stack px={13} gap={37}>
          <Group
            className={classes['menu-link']}
            gap={10}
            wrap="nowrap"
            justify="space-between"
            onClick={() =>
              openContextModal({
                modal: 'solution-presentation',
                innerProps: {},
              })
            }
          >
            <Text c={theme.white} fz={rem(15)} lh={rem(18)}>
              {t('DETAILED_PRESENTATION_OF_THE_MPP_SOLUTION')}
            </Text>
            <IconChevronDown color={theme.colors.blue[5]} size={14} />
          </Group>
          {session && session.isOriginalUser && (
            <Group
              className={classes['menu-link']}
              gap={10}
              wrap="nowrap"
              justify="space-between"
              onClick={() =>
                openContextModal({
                  modal: 'share-access',
                  innerProps: {
                    onConfirm: () => {
                      if (session) {
                        shareAccess({ session });
                      }
                    },
                  },
                })
              }
            >
              <Text c={theme.white} fz={rem(15)} lh={rem(18)}>
                {t('SHARE_INFORMATION_ABOUT_MY_BACKGROUND')}
              </Text>
              <IconChevronDown color={theme.colors.blue[5]} size={14} />
            </Group>
          )}
        </Stack>

        <Divider className={classes['divider']} />

        {/* ###### Menu Group ###### */}
        <Stack px={13} gap={18}>
          <Link to={externalLinks.enquiry as string} target="_blank">
            <Group gap={21} wrap="nowrap">
              <Icons.SatisfactionSurvey />
              <Text c={theme.white} fz={rem(15)} lh={rem(18)}>
                {t('SATISFACTION_SURVEY')}
              </Text>
            </Group>
          </Link>
          <Link to={externalLinks.nearbyPharmacies as string} target="_blank">
            <Group gap={21} wrap="nowrap">
              <Icons.DirectoryOfPharmacies />
              <Text c={theme.white} fz={rem(15)} lh={rem(18)}>
                {t('DIRECTORY_OF_PHARMACIES')}
              </Text>
            </Group>
          </Link>
        </Stack>

        <Divider className={classes['divider']} />

        {/* ###### Menu Group ###### */}
        {renderDeleteAccessMenuGroup()}

        {/* ###### Menu Group ###### */}
        <Stack px={16} gap={18}>
          <Link to="/terms-and-conditions">
            <Text c={theme.white} fz={theme.fontSizes.sm}>
              {t('GENERAL_TERMS_AND_CONDITIONS_OF_USE')}
            </Text>
          </Link>
        </Stack>
      </Flex>

      <Text c="blue.3" px={18} py={10} fz={theme.fontSizes.sm} lh={rem(13)}>
        {t('COPYRIGHT_2024_CHU_DE_LIMOGES_FOCUS_SANTE')}
      </Text>
    </Drawer>
  );
}

export { Menu };
