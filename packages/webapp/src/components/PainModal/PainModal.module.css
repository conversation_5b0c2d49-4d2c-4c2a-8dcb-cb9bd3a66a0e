.pain-modal-root {
  overflow: hidden;
  height: fit-content;
  width: 100%;
  padding: 20px;
  border-radius: var(--mantine-radius-lg);
  background-color: var(--mantine-color-white);
  display: flex;
  flex-direction: column;
}

.close-button-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}

.pain-modal-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 30px;
}

.title {
  color: var(--mantine-color-gray-0);
  text-align: center;
  font-size: rem(22);
}

.buttons-wrapper {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  column-gap: 20px;
}

.button {
  height: 34px;
  border-radius: var(--mantine-radius-sm);

  &:disabled {
    opacity: 0.8;
    cursor: not-allowed;
  }
}
