/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ContextModalProps } from '@mantine/modals';
import { useTranslation } from 'react-i18next';
import { Box, Title } from '@mantine/core';
import { useState } from 'react';

// Components
import { Button } from '@/components/Button';
import { CloseButton } from '@/components/CloseButton';
import { PainSlider } from '@/components/PainSlider';

// Styles
import classes from './PainModal.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type PainModalProps = ContextModalProps<{
  defaultPainValue: number;
  onConfirm: (painValue: number) => void;
}>;

function PainModal(props: PainModalProps) {
  const {
    id,
    context,
    innerProps: { onConfirm, defaultPainValue },
  } = props;
  const { t } = useTranslation();
  const [painValue, setPainValue] = useState<number>(defaultPainValue);

  const handleSubmit = async () => {
    onConfirm(painValue);
    context.closeModal(id);
  };

  const handleCancel = () => {
    context.closeModal(id);
  };

  return (
    <Box className={classes['pain-modal-root']}>
      <Box className={classes['close-button-wrapper']}>
        <CloseButton
          variant="transparent"
          onClick={() => context.closeModal(id)}
        />
      </Box>

      <Box className={classes['pain-modal-content']}>
        <Title className={classes.title} component="h1">
          {t('ASSESS_YOUR_PAIN')}
        </Title>

        <PainSlider defaultValue={painValue} onChangeEnd={setPainValue} />

        <Box className={classes['buttons-wrapper']}>
          <Button
            variant="outline"
            className={classes['button']}
            onClick={handleCancel}
          >
            Annuler
          </Button>
          <Button
            variant="default"
            className={classes['button']}
            onClick={handleSubmit}
          >
            Confirmer
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export { PainModal };
