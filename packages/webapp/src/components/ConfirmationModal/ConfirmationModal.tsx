/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ContextModalProps } from '@mantine/modals';
import { Box, Title, Text } from '@mantine/core';

// Components
import { Button } from '@/components/Button';

// Styles
import classes from './ConfirmationModal.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type ConfirmationModalProps = ContextModalProps<{
  title: string;
  description: string;
  onConfirm: () => void;
  onCancel?: () => void;
}>;

function ConfirmationModal(props: ConfirmationModalProps) {
  const { id, context, innerProps } = props;

  const handleSubmit = () => {
    context.closeModal(id);
    innerProps.onConfirm();
  };

  const handleCancel = () => {
    context.closeModal(id);
    if (innerProps.onCancel) innerProps.onCancel();
  };

  return (
    <Box className={classes['confirmation-modal-root']}>
      <Title className={classes['title']}>{innerProps.title}</Title>
      <Text className={classes['description']}>{innerProps.description}</Text>
      <Box className={classes['buttons-wrapper']}>
        <Button
          variant="outline"
          className={classes['button']}
          onClick={handleCancel}
        >
          Annuler
        </Button>
        <Button
          variant="default"
          className={classes['button']}
          onClick={handleSubmit}
        >
          Confirmer
        </Button>
      </Box>
    </Box>
  );
}

export { ConfirmationModal };
