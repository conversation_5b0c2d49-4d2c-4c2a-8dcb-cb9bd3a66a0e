/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

/* -------------------------------------------------------------------------- */
/*                                  Component                                 */
/* -------------------------------------------------------------------------- */

type IconProps = React.HTMLAttributes<SVGElement>;

export const Icons = {
  Info: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="16.45"
      height="16.636"
      viewBox="0 0 16.45 16.636"
      {...props}
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_2298"
            data-name="Rectangle 2298"
            width="16.45"
            height="16.636"
            fill="#d0d0d0"
          />
        </clipPath>
      </defs>
      <g id="Groupe_1002" data-name="Groupe 1002" transform="translate(0 0)">
        <g
          id="Groupe_1001"
          data-name="Groupe 1001"
          transform="translate(0 0)"
          clipPath="url(#clip-path)"
        >
          <path
            id="Tracé_1067"
            data-name="Tracé 1067"
            d="M16.45,8.224A8.231,8.231,0,0,1,4.939,15.768a.351.351,0,0,0-.317.016,5.444,5.444,0,0,1-3.682.808.33.33,0,0,1-.115-.616A2.952,2.952,0,0,0,2.292,14.15a.33.33,0,0,0-.082-.316,8.225,8.225,0,1,1,14.24-5.61ZM9,5.675A1.032,1.032,0,1,0,7.967,4.643,1.032,1.032,0,0,0,9,5.675M8.918,6.823,7.031,7.1A.25.25,0,0,0,6.8,7.3a.51.51,0,0,0,.623.518c.271-.068.079.463-.182,1.186-.13.361-.277.769-.394,1.176-.046.165-.1.326-.146.484-.317,1.013-.58,1.857.467,2.227.817.289,1.527-.394,1.993-1.049.2-.277-.117-.562-.415-.4-.394.212-.744.35-.7.138l1.252-4.33A.34.34,0,0,0,8.918,6.823Z"
            transform="translate(0 0)"
            fill="#d0d0d0"
            fillRule="evenodd"
          />
        </g>
      </g>
    </svg>
  ),

  Opinion: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="29.237"
      height="29.331"
      viewBox="0 0 29.237 29.331"
      {...props}
    >
      <g
        id="Groupe_989"
        data-name="Groupe 989"
        transform="translate(-145.885 -1667.239)"
      >
        <path
          id="Tracé_1065"
          data-name="Tracé 1065"
          d="M20.647,4.057A11.76,11.76,0,0,0,2.874,19.463L.245,22.084A.841.841,0,0,0,.069,23a.829.829,0,0,0,.773.521H11.761A11.76,11.76,0,0,0,20.647,4.057m-2.166,9.384a.843.843,0,0,1-.84.84h-3.36v3.36a.842.842,0,0,1-.84.84h-3.36a.842.842,0,0,1-.84-.84v-3.36H5.881a.843.843,0,0,1-.84-.84v-3.36a.843.843,0,0,1,.84-.84H9.242V5.881a.842.842,0,0,1,.84-.84h3.36a.842.842,0,0,1,.84.84v3.36h3.36a.843.843,0,0,1,.84.84Z"
          transform="translate(145.885 1667.239)"
          fill="#707070"
        />
        <path
          id="Tracé_1066"
          data-name="Tracé 1066"
          d="M18.761,18.84a.859.859,0,0,1-.6.252.814.814,0,0,1-.244-.034l-4.721-1.419A11.306,11.306,0,0,1,.29,16.37a.84.84,0,0,1,1.092-1.277,9.618,9.618,0,0,0,11.264.916.874.874,0,0,1,.68-.084l3.52,1.058-1.117-3.36a.851.851,0,0,1,.084-.714,9.636,9.636,0,0,0-.882-11.516A.839.839,0,0,1,16.191.285a11.3,11.3,0,0,1,1.252,13.171l1.512,4.527a.845.845,0,0,1-.193.857"
          transform="translate(156.124 1677.478)"
          fill="#52b5f8"
        />
      </g>
    </svg>
  ),

  Biology: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24.552"
      height="29.831"
      viewBox="0 0 24.552 29.831"
      {...props}
    >
      <g
        id="Groupe_987"
        data-name="Groupe 987"
        transform="translate(-80.102 -1666.739)"
      >
        <path
          id="Tracé_1049"
          data-name="Tracé 1049"
          d="M68.185,49.141H62.319a1.792,1.792,0,0,0-1.791,1.791v.648a1.759,1.759,0,0,0,1.42,1.729v10a3.3,3.3,0,0,0,6.607,0v-10a1.811,1.811,0,0,0,1.42-1.729v-.648a1.833,1.833,0,0,0-1.791-1.791m-.926,14.171a2.007,2.007,0,1,1-4.014,0v-.031H64.82a.648.648,0,0,0,0-1.3H63.245V59.978H64.82a.648.648,0,0,0,0-1.3H63.245V56.674H64.82a.648.648,0,0,0,0-1.3H63.245V53.371h4.014ZM68.648,51.58a.488.488,0,0,1-.494.494H62.288a.488.488,0,0,1-.494-.494v-.648a.488.488,0,0,1,.494-.494h5.866a.488.488,0,0,1,.494.494Z"
          transform="translate(34.678 1629.954)"
          fill="#52b5f8"
        />
        <path
          id="Union_24"
          data-name="Union 24"
          d="M-2993.352,490.182a.634.634,0,0,1-.649-.649V468.322a.633.633,0,0,1,.649-.648h3.613v-.988a.633.633,0,0,1,.648-.649h2.007a3.3,3.3,0,0,1,3.056-2.037,3.347,3.347,0,0,1,3.056,2.037h2.038a.634.634,0,0,1,.649.649v.988h3.613a.633.633,0,0,1,.648.648v6.268h-1.3v-5.619h-2.964v.926a.633.633,0,0,1-.649.648h-10.157a.633.633,0,0,1-.648-.648v-.926h-2.965v19.883h13.431v1.328Zm4.909-20.934h8.831v-1.914h-1.853a.689.689,0,0,1-.617-.493,1.981,1.981,0,0,0-1.945-1.544,2.013,2.013,0,0,0-1.945,1.544.631.631,0,0,1-.617.493h-1.853Zm2.13,16.7a.634.634,0,0,1-.648-.649.652.652,0,0,1,.648-.648h7.04v1.3Zm-3.149,0a.633.633,0,0,1-.648-.649.632.632,0,0,1,.648-.648h.71a.652.652,0,0,1,.649.648.634.634,0,0,1-.649.649Zm0-3.736a.633.633,0,0,1-.648-.649.633.633,0,0,1,.648-.648h.71a.633.633,0,0,1,.649.648.634.634,0,0,1-.649.649Zm3.149,0a.633.633,0,0,1-.648-.648.633.633,0,0,1,.648-.648h7.04v1.3Zm0-3.767a.633.633,0,0,1-.648-.648.633.633,0,0,1,.648-.648h5.682a1.289,1.289,0,0,0-.031.494v.8Zm-3.149,0a.632.632,0,0,1-.648-.648.632.632,0,0,1,.648-.648h.71a.633.633,0,0,1,.649.648.633.633,0,0,1-.649.648Zm3.149-3.766a.633.633,0,0,1-.648-.648.634.634,0,0,1,.648-.649h7.75a.634.634,0,0,1,.649.649.652.652,0,0,1-.649.648Zm-3.149,0a.633.633,0,0,1-.648-.648.633.633,0,0,1,.648-.649h.71a.652.652,0,0,1,.649.649.633.633,0,0,1-.649.648Zm4.478-7.379a.961.961,0,0,1,.957-.957.962.962,0,0,1,.958.957.963.963,0,0,1-.958.958A.962.962,0,0,1-2984.984,467.3Z"
          transform="translate(3074.603 1203.239)"
          fill="#707070"
          stroke="rgba(0,0,0,0)"
          strokeWidth="1"
        />
      </g>
    </svg>
  ),

  Imagery: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24.304"
      height="30.331"
      viewBox="0 0 24.304 30.331"
      {...props}
    >
      <g
        id="Groupe_988"
        data-name="Groupe 988"
        transform="translate(-111.859 -1666.739)"
      >
        <path
          id="Tracé_1060"
          data-name="Tracé 1060"
          d="M3.568,8.652a.509.509,0,0,1,.22-.993,10.026,10.026,0,0,0,1.622.225V6.849A10.9,10.9,0,0,1,3,6.458a.509.509,0,1,1,.275-.981,9.982,9.982,0,0,0,2.132.354V4.8a10.934,10.934,0,0,1-3.1-.61.51.51,0,1,1,.339-.962,9.916,9.916,0,0,0,2.757.552V2.747a10.93,10.93,0,0,1-3.793-.886.508.508,0,1,1,.4-.933,9.934,9.934,0,0,0,3.391.8V1.508a.511.511,0,0,1,1.021,0v.222a9.881,9.881,0,0,0,3.463-.8.508.508,0,0,1,.4.934,10.94,10.94,0,0,1-3.865.889V3.783a9.944,9.944,0,0,0,3.134-.668.509.509,0,1,1,.371.949,10.924,10.924,0,0,1-3.5.738V5.835a9.938,9.938,0,0,0,2.51-.449.509.509,0,1,1,.3.972,10.883,10.883,0,0,1-2.813.495V7.888a9.906,9.906,0,0,0,2-.3.508.508,0,0,1,.251.986,10.967,10.967,0,0,1-2.266.335.51.51,0,0,1-.987,0,11.044,11.044,0,0,1-1.858-.251M11.461,0H.452A.445.445,0,0,0,.134.133.449.449,0,0,0,0,.452V9.716a.448.448,0,0,0,.134.319.449.449,0,0,0,.318.134H11.461a.456.456,0,0,0,.452-.452V.452a.45.45,0,0,0-.134-.319A.446.446,0,0,0,11.461,0"
          transform="translate(118.054 1676.075)"
          fill="#52b5f8"
          fillRule="evenodd"
        />
        <path
          id="Union_25"
          data-name="Union 25"
          d="M7.965,29.269a1.393,1.393,0,0,1-.974-1.487l.131-7.608h4.045L9.607,28.587a1.393,1.393,0,0,1-1.642.682Zm5.708-.682-1.561-8.412h4.045l.131,7.608a1.393,1.393,0,0,1-2.616.8ZM22.247,9.752l-3.658-.607a1.476,1.476,0,0,0-1.444-1.183H6.135A1.478,1.478,0,0,0,4.691,9.146l-3.658.607A.994.994,0,0,1,.68,7.816L7.073,6.25a2.89,2.89,0,0,1,1.53.233A9.217,9.217,0,0,0,11.639,7a9.217,9.217,0,0,0,3.037-.514,2.891,2.891,0,0,1,1.53-.233L22.6,7.816a.994.994,0,0,1-.314,1.938ZM8.82,2.82a2.82,2.82,0,1,1,2.821,2.82A2.82,2.82,0,0,1,8.82,2.82Z"
          transform="translate(112.371 1667.239)"
          fill="#707070"
          stroke="rgba(0,0,0,0)"
          strokeWidth="1"
        />
      </g>
    </svg>
  ),

  Doctor: (props: IconProps) => (
    <svg
      version="1.1"
      id="_x32_"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="32px"
      height="32px"
      viewBox="0 0 512 512"
      xmlSpace="preserve"
      {...props}
    >
      <g>
        <path
          fill="#000000"
          d="M110.547,411.844c-5.234,5.813-9.141,12.5-11.328,19.266c-1.531,4.766-2.266,9.469-2.266,13.875
		c0,2.688,0.266,5.25,0.844,7.672c0.438,1.797,1.031,3.516,1.828,5.094c0.594,1.203,1.313,2.344,2.156,3.375
		c1.266,1.531,2.828,2.859,4.688,3.781c1.844,0.938,3.969,1.438,6.125,1.422v-9.766c-0.453,0-0.797-0.063-1.125-0.156
		c-0.578-0.156-1.047-0.422-1.578-0.891c-0.375-0.359-0.781-0.828-1.156-1.469c-0.563-0.922-1.094-2.203-1.453-3.734
		c-0.359-1.547-0.563-3.344-0.563-5.328c0-3.297,0.578-7.047,1.797-10.891c1.141-3.531,2.953-7.188,5.328-10.656
		c3.547-5.219,8.391-9.984,13.984-13.391c2.781-1.703,5.781-3.078,8.891-4.016c3.125-0.938,6.391-1.438,9.766-1.438
		c4.5,0,8.813,0.906,12.844,2.531c6.031,2.406,11.484,6.453,15.844,11.281c4.344,4.813,7.578,10.406,9.266,15.688
		c1.234,3.844,1.813,7.594,1.813,10.891c0,2.016-0.219,3.844-0.594,5.391c-0.266,1.156-0.641,2.188-1.047,3
		c-0.313,0.625-0.641,1.125-0.984,1.547c-0.5,0.609-1,1-1.484,1.25c-0.5,0.234-1.016,0.375-1.766,0.391v9.766
		c1.281,0.016,2.547-0.172,3.75-0.5c2.109-0.578,4-1.672,5.547-3.094c1.156-1.063,2.125-2.281,2.922-3.609
		c1.219-2,2.063-4.219,2.609-6.594s0.813-4.906,0.813-7.547c0-4.406-0.734-9.125-2.266-13.875
		c-1.453-4.516-3.672-8.984-6.547-13.188c-4.313-6.297-10.063-12.016-16.969-16.234c-3.453-2.094-7.188-3.813-11.172-5
		c-3.984-1.219-8.203-1.875-12.578-1.875c-5.828,0-11.391,1.188-16.469,3.234C122.375,401.109,115.781,406.047,110.547,411.844z"
        />
        <path
          fill="#000000"
          d="M165.594,452.109c-1.594,1.266-2.531,3.172-2.531,5.219v7.891c0,2.031,0.938,3.953,2.531,5.219
		c1.594,1.25,3.688,1.719,5.656,1.25l5.266-1.25v-18.344l-5.266-1.25C169.281,450.375,167.188,450.844,165.594,452.109z"
        />
        <path
          fill="#000000"
          d="M121.719,450.844l-5.281,1.25v18.344l5.281,1.25c1.969,0.469,4.063,0,5.656-1.25
		c1.594-1.266,2.531-3.188,2.531-5.219v-7.891c0-2.047-0.938-3.953-2.531-5.219S123.688,450.375,121.719,450.844z"
        />
        <path
          fill="#000000"
          d="M453.453,429.594c-2.016-7.531-4.859-14.281-8.359-20.281c-6.141-10.547-14.266-18.75-23.234-25.25
		c-6.734-4.875-13.922-8.859-21.234-12.281c-10.953-5.125-22.156-9.063-32.5-12.891c-10.344-3.813-19.797-7.547-27.156-11.891
		c-2.688-1.594-5.109-3.25-7.203-4.984c-3.125-2.609-5.563-5.391-7.328-8.5s-2.953-6.609-3.406-10.984
		c-0.328-3.125-0.469-6.063-0.469-8.875c0-8.281,1.219-15.453,2.781-22.781c4.625-5.219,8.859-11.438,12.859-18.875
		c4.016-7.484,7.828-16.219,11.625-26.563c2.438-1.109,4.891-2.438,7.281-4.063c5.469-3.656,10.656-8.781,14.984-15.609
		c4.313-6.828,7.781-15.313,10.156-25.781c0.656-2.906,0.969-5.797,0.969-8.641c0.016-5.938-1.391-11.594-3.75-16.656
		c-1.641-3.516-3.719-6.734-6.141-9.656c1.234-4.563,2.734-10.703,4.078-17.891c1.844-9.984,3.375-21.984,3.375-34.594
		c0-8.141-0.641-16.547-2.297-24.844c-1.234-6.219-3.063-12.391-5.625-18.297c-3.859-8.891-9.469-17.25-17.266-24.156
		c-7.219-6.391-16.266-11.484-27.188-14.75c-3.859-4.625-7.734-8.563-11.703-11.906c-3.438-2.875-6.953-5.297-10.547-7.281
		c-5.406-3-11-4.984-16.734-6.188s-11.578-1.641-17.641-1.641c-8.359,0-17.156,0.828-26.875,1.781
		c-3.672,0.375-6.75,0.828-9.422,1.297c-3.984,0.719-6.969,1.453-9.359,1.938c-1.203,0.234-2.25,0.422-3.281,0.547
		c-1.031,0.109-2.031,0.188-3.172,0.188c-1.531,0-3.297-0.125-5.609-0.453c-7.078-1.031-13.547-2.625-18.625-4.188
		c-2.531-0.766-4.719-1.531-6.438-2.188c-0.859-0.328-1.609-0.625-2.203-0.875c-0.609-0.25-1.063-0.484-1.328-0.609l0.016,0.016
		c-0.781-0.406-1.531-0.625-2.203-0.75C182.609,0.031,182.016,0,181.5,0c-1.078,0-1.844,0.156-2.453,0.313s-1.078,0.344-1.5,0.516
		c-0.813,0.328-1.438,0.672-2.063,1.047c-1.141,0.688-2.234,1.453-3.391,2.344c-2.016,1.563-4.234,3.5-6.594,5.781
		c-3.531,3.406-7.313,7.531-10.75,12.031c-1.719,2.234-3.328,4.578-4.781,7s-2.734,4.906-3.75,7.516
		c-4.969,12.922-8.25,24.828-10.281,35.813c-2.047,10.984-2.828,21.047-2.828,30.281c0,15.109,2.109,27.922,4.141,38.75
		c0,0.516,0.016,1,0.047,1.516c0.063,1.016,0.172,2.063,0.281,3.156c0.172,1.625,0.359,3.297,0.5,4.703
		c0.078,0.703,0.141,1.328,0.188,1.813c0.016,0.234,0.031,0.453,0.031,0.609l0.016,0.156v0.047v0.016v0.922l1.984,8.828
		c-2.859,3.125-5.328,6.625-7.25,10.469c-2.688,5.344-4.281,11.375-4.281,17.75c0,2.813,0.328,5.719,0.984,8.609
		c1.563,6.984,3.641,13.078,6.125,18.391c3.719,7.984,8.438,14.188,13.656,18.844c4.047,3.625,8.375,6.266,12.656,8.219
		c3.781,10.344,7.594,19.063,11.609,26.547c4,7.453,8.219,13.656,12.844,18.875c1.563,7.328,2.781,14.516,2.797,22.797
		c0,2.813-0.156,5.75-0.484,8.875c-0.313,3-0.969,5.594-1.922,7.938c-1.422,3.5-3.5,6.484-6.328,9.313
		c-2.828,2.781-6.438,5.391-10.703,7.813c-4.328,2.453-9.344,4.75-14.797,6.938c-9.563,3.875-20.469,7.531-31.516,11.953
		c-8.281,3.297-16.672,7.063-24.672,11.766c-6,3.531-11.766,7.625-17.078,12.484c-7.953,7.281-14.813,16.359-19.547,27.578
		c-4.75,11.234-7.391,24.531-7.375,40.25c0,2.219,0.469,4.328,1.234,6.281c0.703,1.828,1.688,3.5,2.844,5.094
		c2.188,2.969,5,5.625,8.453,8.188c6.063,4.469,14.109,8.656,24.531,12.594c15.625,5.891,36.563,11.188,63.641,15.031
		c27.063,3.844,60.266,6.25,100.266,6.25c34.703,0,64.266-1.797,89.156-4.781c18.656-2.25,34.703-5.156,48.313-8.484
		c10.219-2.484,19.078-5.219,26.672-8.094c5.688-2.156,10.688-4.406,15.031-6.719c3.25-1.734,6.125-3.516,8.672-5.344
		c3.813-2.766,6.875-5.609,9.203-8.844c1.172-1.609,2.125-3.328,2.828-5.203c0.703-1.844,1.125-3.875,1.125-5.969
		C456.984,447.813,455.75,438.203,453.453,429.594z M327.266,358.094l-50.156,78.328l-5.594-38.453l14.234-15.063l-9.219-15.375
		l38.906-20.453c1.078,1.391,2.219,2.703,3.422,3.953C321.438,353.672,324.266,356,327.266,358.094z M183.078,87.156
		c45.219,10.031,133.641-9.141,133.641-9.141s0.953,21.922,16.031,42.047c5.938,7.906,10.828,20.266,14.5,32.016
		c-0.984-1.828-3.297-2.516-6.75-2.953c-7.75-1.047-19.266-1.719-32.234-1.094c-38.531,1.891-35.672,5.391-50.797,5.391
		s-12.266-3.5-50.797-5.391c-12.969-0.625-24.484,0.047-32.25,1.094c-4.031,0.531-6.563,1.344-7.141,4.031
		c-0.203,1-0.516,2.125-1.906,2.672C169.641,139.891,181.516,119.531,183.078,87.156z M339.922,176.469
		c0,0.219-0.156,22.313-15.188,29.859c-5.109,2.578-11.516,4-18.031,4.016c-6.875,0-13.156-1.563-18.172-4.516
		c-5.547-3.25-9.281-8.078-11.109-14.313c-0.438-1.453-0.828-2.906-1.234-4.313c-1.188-4.297-4.391-16.234,2.406-21.484
		c4.375-3.422,17.953-5.578,30.969-5.578c11.828,0,23.891,1.609,27.422,5.297C339.313,167.875,340.219,172.219,339.922,176.469z
		 M238.75,187.203c-0.406,1.406-0.813,2.859-1.234,4.313c-1.828,6.234-5.563,11.063-11.094,14.313
		c-5.031,2.953-11.313,4.516-18.188,4.516c-6.516-0.016-12.906-1.438-18.031-4.016c-15.031-7.547-15.172-29.641-15.188-29.859
		c-0.297-4.25,0.609-8.594,2.922-11.031c3.547-3.688,15.609-5.297,27.438-5.297c13,0,26.594,2.156,30.984,5.578
		C243.141,170.969,239.938,182.906,238.75,187.203z M188.547,264.063c-3.922-7.313-7.828-16.406-11.844-27.75l-1.328-3.703
		l-3.688-1.359c-2.563-0.938-5.063-2.156-7.453-3.766c-3.609-2.422-7.031-5.734-10.172-10.672s-5.953-11.563-7.984-20.516
		c-0.391-1.703-0.547-3.328-0.547-4.922c0-3.594,0.859-7,2.5-10.25c1.344-2.703,3.219-5.25,5.5-7.563
		c3.844,5.813,7.031,10.422,8.188,11.578c2.203,2.203,3.297,0.078,3.469-4.047c1.359,9.172,5.719,24.313,19.797,31.797
		c20.266,10.766,50.516,6.734,60.781-17.234c4.641-10.813,4.703-21.375,11.703-21.375c6.984,0,7.063,10.563,11.703,21.375
		c10.281,23.969,40.531,28,60.797,17.234c20.25-10.766,20.391-37.422,20.391-39.297c0-0.969,0.922-1.703,2.234-1.844
		c1.719,7.234,2.609,12.141,2.609,12.141s1.938-3.703,4.844-8.641c1.734,2.031,3.172,4.219,4.234,6.5
		c1.422,3.063,2.188,6.266,2.188,9.594c0,1.609-0.172,3.25-0.563,4.938c-1.344,5.969-3.047,10.906-4.953,15
		c-2.875,6.125-6.188,10.344-9.656,13.438c-3.453,3.094-7.141,5.109-10.969,6.531l-3.703,1.344l-1.313,3.719
		c-4.016,11.344-7.938,20.453-11.859,27.75c-3.938,7.313-7.844,12.813-11.906,17.094l-1.609,1.703l-0.5,2.266
		c-1.813,8.359-3.625,17.594-3.625,28.531c0,3.375,0.172,6.891,0.547,10.594c0.453,4.344,1.453,8.422,2.938,12.172
		c0.063,0.172,0.156,0.359,0.219,0.516l-50.891,26.766l-56.406-26.172c1.734-4.063,2.906-8.5,3.406-13.281
		c0.391-3.703,0.547-7.219,0.547-10.594c0.016-10.938-1.797-20.188-3.625-28.547l-0.5-2.266l-1.609-1.688
		C196.391,276.844,192.469,271.375,188.547,264.063z M188.094,355.594c2.938-2.359,5.641-5,8.031-7.969l43.016,19.969l-9.188,15.313
		l14.219,15.063l-5.25,36.203l-54.875-75.609C185.438,357.609,186.797,356.625,188.094,355.594z M440.219,458.5
		c-0.016,0.094-0.125,0.406-0.422,0.906c-0.563,0.969-1.875,2.531-4.094,4.313c-1.922,1.547-4.516,3.281-7.781,5.063
		c-5.734,3.141-13.5,6.406-23.344,9.5c-14.781,4.656-34.297,8.906-58.922,12c-24.625,3.063-54.359,4.969-89.672,4.969
		c-34.094,0-63-1.781-87.125-4.672c-18.094-2.172-33.5-4.984-46.344-8.109c-9.656-2.359-17.875-4.906-24.703-7.5
		c-5.141-1.938-9.5-3.906-13.078-5.828c-2.688-1.438-4.953-2.859-6.797-4.172c-2.75-1.969-4.5-3.766-5.375-5
		c-0.438-0.594-0.656-1.063-0.734-1.281c-0.047-0.094-0.063-0.156-0.063-0.188c0-9.375,1.063-17.406,2.906-24.375
		c1.609-6.094,3.828-11.391,6.531-16.078c4.719-8.203,10.922-14.641,18.297-20.063c5.5-4.078,11.672-7.563,18.203-10.672
		c7.328-3.484,15.109-6.484,22.922-9.375v16.875h5.859h5.859v-21.203c7.469-2.797,14.75-5.672,21.531-9.109l86.703,119.453
		l75.75-118.266c0.234,0.359,0.469,0.719,0.688,1.063c3.156,5.078,5.359,10.609,6.828,16.875c1.453,6.25,2.125,13.25,2.125,21.047
		c0,18.063,0,33.797,0,44.391H318.75v11.734h67v-11.734h-27.219c0-10.594,0-26.328,0-44.391c0-11.359-1.297-21.703-4.516-31.141
		c-0.281-0.813-0.578-1.625-0.891-2.422c9.156,3.609,18.734,6.859,28.016,10.547c7.953,3.141,15.672,6.578,22.688,10.656
		c5.281,3.063,10.172,6.5,14.516,10.406c6.516,5.922,11.859,12.906,15.703,21.859C437.875,433.516,440.219,444.516,440.219,458.5
		L440.219,458.5z"
        />
      </g>
    </svg>
  ),

  PersonStanding: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path d="M8 3a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3M6 6.75v8.5a.75.75 0 0 0 1.5 0V10.5a.5.5 0 0 1 1 0v4.75a.75.75 0 0 0 1.5 0v-8.5a.25.25 0 1 1 .5 0v2.5a.75.75 0 0 0 1.5 0V6.5a3 3 0 0 0-3-3H7a3 3 0 0 0-3 3v2.75a.75.75 0 0 0 1.5 0v-2.5a.25.25 0 0 1 .5 0" />
    </svg>
  ),

  PersonStandingDress: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path d="M8 3a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3m-.5 12.25V12h1v3.25a.75.75 0 0 0 1.5 0V12h1l-1-5v-.215a.285.285 0 0 1 .56-.078l.793 2.777a.711.711 0 1 0 1.364-.405l-1.065-3.461A3 3 0 0 0 8.784 3.5H7.216a3 3 0 0 0-2.868 2.118L3.283 9.079a.711.711 0 1 0 1.365.405l.793-2.777a.285.285 0 0 1 .56.078V7l-1 5h1v3.25a.75.75 0 0 0 1.5 0Z" />
    </svg>
  ),

  PersonWalking: (props: IconProps) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path d="M9.5 1.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0M6.44 3.752A.75.75 0 0 1 7 3.5h1.445c.742 0 1.32.643 1.243 1.38l-.43 4.083a1.8 1.8 0 0 1-.088.395l-.318.906.213.242a.8.8 0 0 1 .114.175l2 4.25a.75.75 0 1 1-1.357.638l-1.956-4.154-1.68-1.921A.75.75 0 0 1 6 8.96l.138-2.613-.435.489-.464 2.786a.75.75 0 1 1-1.48-.246l.5-3a.75.75 0 0 1 .18-.375l2-2.25Z" />
      <path d="M6.25 11.745v-1.418l1.204 1.375.261.524a.8.8 0 0 1-.12.231l-2.5 3.25a.75.75 0 1 1-1.19-.914zm4.22-4.215-.494-.494.205-1.843.006-.067 1.124 1.124h1.44a.75.75 0 0 1 0 1.5H11a.75.75 0 0 1-.531-.22Z" />
    </svg>
  ),

  DirectoryOfPharmacies: (props: IconProps) => (
    <svg
      id="Groupe_1143"
      data-name="Groupe 1143"
      xmlns="http://www.w3.org/2000/svg"
      width="21.59"
      height="24.177"
      viewBox="0 0 21.59 24.177"
      {...props}
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_2305"
            data-name="Rectangle 2305"
            width="21.59"
            height="24.177"
            fill="#08d500"
          />
        </clipPath>
      </defs>
      <g id="Groupe_1019" data-name="Groupe 1019" clipPath="url(#clip-path)">
        <path
          id="Tracé_1074"
          data-name="Tracé 1074"
          d="M22.583,0h-.449A5.972,5.972,0,0,0,16.02,6.114c0,4.51,4.123,10.462,5.329,12.566a1.131,1.131,0,0,0,.982.561h.056a1.2,1.2,0,0,0,.982-.561c1.206-2.1,5.329-8.536,5.329-12.566A5.972,5.972,0,0,0,22.583,0"
          transform="translate(-11.527)"
          fill="#2eb428"
        />
        <path
          id="Tracé_1075"
          data-name="Tracé 1075"
          d="M29.212,6.159a5.063,5.063,0,1,1-3.58-1.483,5.046,5.046,0,0,1,3.58,1.483Z"
          transform="translate(-14.801 -3.364)"
          fill="#fff"
          fillRule="evenodd"
        />
        <path
          id="Tracé_1076"
          data-name="Tracé 1076"
          d="M21.563,59.4,19.207,50.7a.691.691,0,0,0-.673-.5H14.915c-.224.449-.449.926-.7,1.4h.421l1.487,4.516H8.3L6.837,51.6H7.4c-.252-.477-.5-.954-.7-1.4H3.051a.724.724,0,0,0-.673.5L.022,59.4a.778.778,0,0,0,.112.617.732.732,0,0,0,.561.28H20.89a.733.733,0,0,0,.561-.28.683.683,0,0,0,.112-.617M3.584,51.6h1.8l.9,2.833H2.826ZM1.62,58.895l.814-3.057H6.725l.982,3.057ZM16.093,51.6H18l1.234,4.516H17.608Zm-6.9,7.293-.449-1.374h3.9l.421,1.374Zm5.329,0-.393-1.374h5.5l.364,1.374Z"
          transform="translate(0 -36.12)"
          fill="#d8d4d3"
        />
        <path
          id="Tracé_1077"
          data-name="Tracé 1077"
          d="M29.988,11.851h2.13a.243.243,0,0,1,.172.072l.009.009a.24.24,0,0,1,.063.164v1.5h1.5a.246.246,0,0,1,.244.244v2.13a.243.243,0,0,1-.072.172l-.009.009a.239.239,0,0,1-.164.063h-1.5v1.5a.246.246,0,0,1-.244.244h-2.13a.243.243,0,0,1-.172-.072l-.009-.009a.24.24,0,0,1-.063-.164v-1.5h-1.5A.245.245,0,0,1,28,15.966v-2.13a.243.243,0,0,1,.072-.172l.009-.009a.241.241,0,0,1,.164-.063h1.5V12.1a.246.246,0,0,1,.244-.244"
          transform="translate(-20.149 -8.527)"
          fill="#5eb130"
          fillRule="evenodd"
        />
      </g>
    </svg>
  ),

  SatisfactionSurvey: (props: IconProps) => (
    <svg
      id="Groupe_1145"
      data-name="Groupe 1145"
      xmlns="http://www.w3.org/2000/svg"
      width="21.59"
      height="22.152"
      viewBox="0 0 21.59 22.152"
      {...props}
    >
      <defs>
        <clipPath id="clip-path">
          <rect
            id="Rectangle_2366"
            data-name="Rectangle 2366"
            width="21.59"
            height="22.152"
            fill="none"
          />
        </clipPath>
      </defs>
      <g id="Groupe_1144" data-name="Groupe 1144" clipPath="url(#clip-path)">
        <path
          id="Tracé_1144"
          data-name="Tracé 1144"
          d="M160.628,0a5,5,0,1,1-5,5,5,5,0,0,1,5-5"
          transform="translate(-144.036)"
          fill="#fff"
          fillRule="evenodd"
        />
        <path
          id="Tracé_1145"
          data-name="Tracé 1145"
          d="M5.505,226.938,2.58,221.27H8.432Z"
          transform="translate(-2.388 -204.787)"
          fill="#fff"
          fillRule="evenodd"
        />
        <rect
          id="Rectangle_2365"
          data-name="Rectangle 2365"
          width="6.236"
          height="15.738"
          fill="#fff"
        />
        <path
          id="Tracé_1146"
          data-name="Tracé 1146"
          d="M160.628,163.182a5,5,0,1,1-5,5,5,5,0,0,1,5-5"
          transform="translate(-144.036 -151.026)"
          fill="#fff"
          fillRule="evenodd"
        />
        <path
          id="Tracé_1147"
          data-name="Tracé 1147"
          d="M195.267,202.232l1.55-1.55a.414.414,0,0,0-.585-.586l-1.551,1.55-1.55-1.55a.414.414,0,0,0-.586.586l1.55,1.55-1.55,1.55a.414.414,0,0,0,.586.586l1.55-1.55,1.551,1.55a.414.414,0,0,0,.585-.586Z"
          transform="translate(-178.089 -185.078)"
          fill="red"
          fillRule="evenodd"
        />
        <path
          id="Tracé_1148"
          data-name="Tracé 1148"
          d="M191.878,43.736l1.564,1.235a.415.415,0,0,0,.59-.079l2.226-3.03a.414.414,0,1,0-.667-.49l-1.973,2.684-1.228-.969a.414.414,0,1,0-.513.65"
          transform="translate(-177.438 -38.133)"
          fill="#31b553"
          fillRule="evenodd"
        />
      </g>
    </svg>
  ),
};
