/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useTranslation } from 'react-i18next';
import { Title } from '@mantine/core';

// Components
import { Header } from '../Header';

// Styles
import classes from './NotFound.module.css';

/* -------------------------------------------------------------------------- */
/*                                  Component                                 */
/* -------------------------------------------------------------------------- */

type NotFoundProps = { data: { data: { message?: string } } | unknown };

function NotFound({ data }: NotFoundProps) {
  const { t } = useTranslation();
  let message: string | undefined;
  if (typeof data === 'object' && data !== null && 'data' in data) {
    message = (data as { data: { message?: string } }).data.message;
  }

  return (
    <main className={classes['not-found-root']}>
      <Header />
      <Title className={classes['title']}>
        {message ?? t('PAGE_NOT_FOUND')}
      </Title>
    </main>
  );
}

export { NotFound };
