/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ButtonHTMLAttributes } from 'react';
import {
  CloseButton as NativeCloseButton,
  CloseButtonProps as NativeCloseButtonProps,
} from '@mantine/core';

// Styles
import classes from './CloseButton.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type CloseButtonProps = ButtonHTMLAttributes<HTMLButtonElement> &
  NativeCloseButtonProps;

function CloseButton(props: CloseButtonProps) {
  const { className, ...rest } = props;

  return (
    <NativeCloseButton
      classNames={{ root: classes['close-button-root'] }}
      className={className}
      {...rest}
    />
  );
}

export { CloseButton };
