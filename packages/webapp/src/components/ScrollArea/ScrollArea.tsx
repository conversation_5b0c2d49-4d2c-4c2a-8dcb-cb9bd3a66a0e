/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  ScrollArea as NativeScrollArea,
  ScrollAreaProps as NativeScrollAreaProps,
} from '@mantine/core';

// Styles
import classes from './ScrollArea.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type ScrollAreaProps = NativeScrollAreaProps;

function ScrollArea(props: ScrollAreaProps) {
  const { children, ...rest } = props;

  return (
    <NativeScrollArea
      classNames={{
        scrollbar: classes['scroll-area-scrollbar'],
        thumb: classes['scroll-area-thumb'],
      }}
      {...rest}
    >
      {children}
    </NativeScrollArea>
  );
}

export { ScrollArea };
