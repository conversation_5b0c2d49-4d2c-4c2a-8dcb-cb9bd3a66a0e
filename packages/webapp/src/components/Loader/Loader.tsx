/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  Loader as NativeLoader,
  LoaderProps as NativeLoaderProps,
  useMantineTheme,
} from '@mantine/core';

/* -------------------------------------------------------------------------- */
/*                                  Component                                 */
/* -------------------------------------------------------------------------- */

type LoaderProps = NativeLoaderProps;

function Loader(props: LoaderProps) {
  const theme = useMantineTheme();

  return <NativeLoader color={theme.colors.gray[0]} {...props} />;
}

export { Loader };
