/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useState } from 'react';
import { Slider, Box, Text, BoxProps, useMantineTheme } from '@mantine/core';
import { IconMoodSmile, IconMoodWrrr } from '@tabler/icons-react';

// Styles
import classes from './PainSlider.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type PainSliderProps = BoxProps & {
  defaultValue: number;
  onChangeEnd: (value: number) => void;
};

function PainSlider(props: PainSliderProps) {
  const { defaultValue, onChangeEnd, ...rest } = props;
  const theme = useMantineTheme();
  const [value, setValue] = useState<number>(defaultValue);

  const handleChange = (value: number) => {
    setValue(value);
  };

  const handleChangeEnd = (value: number) => {
    if (navigator.vibrate) navigator.vibrate(200);
    onChangeEnd(value);
  };

  return (
    <Box className={classes['pain-slider-root']} {...rest}>
      <Box className={classes['value-range-wrapper']}>
        <IconMoodSmile
          width={25.5}
          height={25.5}
          color={theme.colors.gray[3]}
        />
        <Text className={classes['value-range']}>0</Text>
      </Box>

      <Slider
        defaultValue={defaultValue}
        min={0}
        max={10}
        step={1}
        classNames={{
          root: classes['slider-root'],
          bar: classes['slider-bar'],
          label: classes['slider-label'],
          thumb: classes['slider-thumb'],
        }}
        thumbChildren={<span>{value}</span>}
        onChange={handleChange}
        onChangeEnd={handleChangeEnd}
      />

      <Box className={classes['value-range-wrapper']}>
        <IconMoodWrrr width={25.5} height={25.5} color={theme.colors.red[7]} />
        <Text className={classes['value-range']}>10</Text>
      </Box>
    </Box>
  );
}

export { PainSlider };
