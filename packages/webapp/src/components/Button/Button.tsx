/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ButtonHTMLAttributes, ReactNode } from 'react';
import { UnstyledButton, UnstyledButtonProps } from '@mantine/core';
import { clsx } from 'clsx';

// Styles
import classes from './Button.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type ButtonVariant = 'default' | 'outline';

type ButtonProps = ButtonHTMLAttributes<HTMLButtonElement> &
  UnstyledButtonProps & {
    children?: ReactNode;
    variant?: ButtonVariant;
  };

function Button(props: ButtonProps) {
  const { children, variant = 'default', className, ...rest } = props;

  return (
    <UnstyledButton
      className={clsx(
        variant === 'default' && classes['default-button-root'],
        variant === 'outline' && classes['outline-button-root'],
        className
      )}
      {...rest}
    >
      {children}
    </UnstyledButton>
  );
}

export { Button };
