import { StaticTextPage } from '@/pages/private/Journey/components/StaticPage/StaticTextPage/StaticTextPage';
import { StaticTextParagraph } from '@/pages/private/Journey/components/StaticPage/StaticTextParagraph/StaticTextParagraph';
import { useTranslation } from 'react-i18next';
export default function Presentation() {
  const { t } = useTranslation();

  return (
    <StaticTextPage title={t('EMERGENCY_DEPARTMENT_PHONE_NUMBER')}>
      <StaticTextParagraph>
        Après votre sortie des urgences, si vous ressentez le besoin de contacter le service pour toute information complémentaire ou nouvel événement, veuillez nous appeler au numéro :  
      </StaticTextParagraph>
      <StaticTextParagraph>
        <b>{t('EMERGENCY_DEPARTMENT_PHONE_NUMBER_NAME')}
        &nbsp; 📞 &nbsp; <span style={{color:'dodgerblue'}}> {t('EMERGENCY_DEPARTMENT_PHONE_NUMBER_NUMBER')}</span></b>
      </StaticTextParagraph>
      <StaticTextParagraph>
        {/* Dans l'intérêt de de la qualité des prises en charges médicales, comprenez que chaque appel mobilise un soignant pour vous répondre. */}
        Nous ferons de notre mieux pour vous répondre dans les meilleurs délais. Pensez à préciser les informations dont nous pourrions avoir besoin, afin de faciliter l'organisation des suites de votre parcours.
        <div style={{height:'calc(100vh - 720px)'}}></div>
      </StaticTextParagraph>
    </StaticTextPage>
  );
}
