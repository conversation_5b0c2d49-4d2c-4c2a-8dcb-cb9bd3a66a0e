import { StaticTextPage } from '@/pages/private/Journey/components/StaticPage/StaticTextPage/StaticTextPage';
import { StaticTextParagraph } from '@/pages/private/Journey/components/StaticPage/StaticTextParagraph/StaticTextParagraph';
import { useTranslation } from 'react-i18next';

export default function EmergencyGuidelines() {
  const { t } = useTranslation();

  return (
    <StaticTextPage title={t('EMERGENCY_GUIDELINES')}>
      <StaticTextParagraph>
        Bonjour...
      </StaticTextParagraph>
      <StaticTextParagraph>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
      </StaticTextParagraph>
      <StaticTextParagraph>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.
      </StaticTextParagraph>
    </StaticTextPage>
  );
}
