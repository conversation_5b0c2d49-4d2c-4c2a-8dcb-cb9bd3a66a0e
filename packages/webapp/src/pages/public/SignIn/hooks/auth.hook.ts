/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useMutation } from '@tanstack/react-query';

// Services
import { AuthService } from '@/pages/public/SignIn/services/auth.service';
import { ViewService } from '@/services/view.service';
import { PatientService } from '../services/patient.service';

// Stores
import usePpuStore from '@/stores/ppu';

// Providers
import { router } from '@/providers/RouterProvider';

/* -------------------------------------------------------------------------- */
/*                              Sign in Hook                                  */
/* -------------------------------------------------------------------------- */

const authService = new AuthService();
const viewService = new ViewService();
const patientService = new PatientService();

/**
 * Hook to handle sign-in functionality.
 */
const useSignIn = () =>
  useMutation({
    mutationFn: authService.signIn,
    onSuccess: async (data, { searchParams: { id: authId } }) => {
      const { access_token } = data;

      try {
        // User authentication is not yet completed.
        // To access this endpoint, we need to manually provide a bearer token in the request headers.
        const { encounter } = await viewService.get({
          headers: { Authorization: `Bearer ${access_token}` },
        });

        // Explicitly pass the auth token for the patient service call as well
        const {
          name: { first_name: firstName },
        } = await patientService.get({
          params: { id: encounter.subject },
          headers: { Authorization: `Bearer ${access_token}` }
        });

        const { signIn } = usePpuStore.getState();
        const isOriginalUser = authId !== encounter.share_id;

        await signIn({
          accessToken: access_token,
          authId,
          data: {
            isOriginalUser,
            firstName,
            sharedId: encounter.share_id,
          },
        });

        return router.navigate({ to: isOriginalUser ? '/home' : '/journey' });
      } catch (_error) {
        //  Error during user sign-in
      }
    },
  });

export { useSignIn };
