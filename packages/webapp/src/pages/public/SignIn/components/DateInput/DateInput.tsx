/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  DateInput as NativeDateInput,
  DateInputProps as NativeDateInputProps,
} from '@mantine/dates';

// Styles
import classes from './DateInput.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type DateInputProps = NativeDateInputProps;

function DateInput(props: DateInputProps) {
  return (
    <NativeDateInput
      classNames={{
        label: classes['date-input-label'],
        input: classes['date-input-input'],
      }}
      {...props}
    />
  );
}

export { DateInput };
