/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Container, rem, Title, useMantineTheme } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { getRouteApi } from '@tanstack/react-router';
import { openContextModal } from '@mantine/modals';

// Components
import { Button } from '@/components/Button';

// Hooks
import { useOrganization } from '@/hooks/useOrganization';

// Styles
import classes from './PageTitles.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

const route = getRouteApi('/_public/_auth/sign-in/$auth-id/');

function PageTitles() {
  const { t } = useTranslation();
  const { 'is-share-link': isShareLink, 'shared-by': sharedBy } =
    route.useSearch();
  const theme = useMantineTheme();

  // Ensure organization is detected for proper translations
  useOrganization();

  if (isShareLink) {
    return (
      <Container
        className={classes['page-titles-root']}
        size="sm"
        py={36}
        px={29}
      >
        <Title
          component="h1"
          ta="center"
          pb={24}
          c="gray.0"
          fz={theme.fontSizes.lg}
          lh={rem(19)}
          lts={-0.16}
        >
          {t('WELCOME')}
        </Title>
        <Title
          component="h3"
          ta="center"
          pb={19}
          fw={900}
          c="gray.0"
          fz={rem(17)}
          lh={rem(23)}
          lts={0.22}
        >
          {sharedBy}&nbsp;
          {/* <br /> */}
          {/* Votre proche&nbsp; */}
          {t('INVITES_YOU_TO_FOLLOW_YOUR_PERSONALIZED')}
        </Title>
        <Button
          variant="outline"
          h={24}
          fz={theme.fontSizes.sm}
          onClick={() =>
            openContextModal({
              modal: 'solution-presentation',
              innerProps: {},
            })
          }
        >
          {t('FIND_OUT_MORE')}
        </Button>
      </Container>
    );
  }

  return (
    <Container
      className={classes['page-titles-root']}
      size="sm"
      pt={42}
      px={56}
      pb={19}
    >
      <Title
        component="h1"
        ta="center"
        fw={900}
        pb={16}
        c="gray.0"
        fz={rem(17)}
        lh={rem(23)}
        lts={0.22}
      >
        {t('MY_PERSONALIZED_JOURNEY_AT_THE_EMERGENCY_1')} <br />
        {t('MY_PERSONALIZED_JOURNEY_AT_THE_EMERGENCY_2')}
      </Title>
      <Title component="h3" ta="center" c="gray.1" fz={rem(15)} lh={rem(23)}>
        {t('SAFETY_INFORMATION_PRESENTATION_AMET')}
      </Title>
    </Container>
  );
}

export { PageTitles };
