/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Anchor, Box } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { getRouteApi, Link } from '@tanstack/react-router';
import { useForm } from '@mantine/form';
import { zodResolver } from 'mantine-form-zod-resolver';
import { z } from 'zod';
import { useEffect, useRef } from 'react';
import { EncounterId } from '@/models/encounter';

// Components
import { But<PERSON> } from '@/components/Button';
import { TextInput } from '@/pages/public/SignIn/components/TextInput';
import { DateInput } from '@/pages/public/SignIn/components/DateInput';
import { Checkbox } from '@/pages/public/SignIn/components/Checkbox';
import { Loader } from '@/components/Loader';

// Styles
import classes from './Form.module.css';

// Hooks
import { useSignIn } from '@/pages/public/SignIn/hooks/auth.hook';

// Services
import { logLoginPageViewed, logAcceptedTos } from '@/services/event.service';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

const route = getRouteApi('/_public/_auth/sign-in/$auth-id/');

function Form() {
  const { t } = useTranslation();
  const { 'auth-id': authId } = route.useParams();
  const signIn = useSignIn();
  const pageViewLoggedRef = useRef(false);
  const initialLoadCompleteRef = useRef(false);
  const tosLoggedRef = useRef(false);

  const STORAGE_KEY = `sign-in-form-${authId}`;

  const formSchema = z.object({
    authId: z.string(),
    lastName: z
      .string()
      .min(1, { message: t('VALUE_MUST_BE_AT_LEAST_1_CHARACTER_LONG') })
      .max(3, { message: t('VALUE_CANNOT_EXCEED_3_CHARACTERS') }),
    birthDate: z
      .union([z.date({ message: t('REQUIRED') }), z.string()])
      .refine((value) => value, { message: t('REQUIRED') }),
    hasAcceptedTerms: z.boolean().refine((value) => value, {
      message: ' ',
    }),
  });

  const form = useForm<z.infer<typeof formSchema>>({
    mode: 'uncontrolled',
    validate: zodResolver(formSchema),
    initialValues: {
      authId,
      lastName: '',
      birthDate: '',
      hasAcceptedTerms: false,
    },
    onValuesChange: (values) => {
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(values));

      // Only log if initial load is complete AND conditions are met
      if (
        initialLoadCompleteRef.current &&
        values.hasAcceptedTerms &&
        !tosLoggedRef.current &&
        authId
      ) {
        logAcceptedTos(authId as EncounterId);
        tosLoggedRef.current = true; // Mark as logged
      }
    },
  });

  // Load saved values from session storage AND log page view on mount
  useEffect(() => {
    // Load existing form values
    const savedValues = sessionStorage.getItem(STORAGE_KEY);
    if (savedValues) {
      const parsedValues = JSON.parse(savedValues);
      parsedValues.birthDate = parsedValues.birthDate
        ? new Date(parsedValues.birthDate)
        : '';

      // If loading state with TOS already accepted, mark it as 'logged'
      // to prevent logging again via onValuesChange trigger during load.
      if (parsedValues.hasAcceptedTerms) {
        tosLoggedRef.current = true;
      }

      form.setValues(parsedValues);
      initialLoadCompleteRef.current = true; // Mark initial load as complete *after* setting values
    }

    // Log the page view event ONLY ONCE
    if (authId && !pageViewLoggedRef.current) {
      logLoginPageViewed(authId as EncounterId);
      pageViewLoggedRef.current = true; // Mark as logged
    }
    // Dependencies: authId to ensure logging happens if ID changes,
    // STORAGE_KEY to align with existing logic (though less critical for logging)
  }, [authId, STORAGE_KEY]);

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    signIn.mutate({
      searchParams: {
        id: values.authId,
        truncated_last_name: values.lastName.substring(0, 3),
        birth_date: values.birthDate,
      },
    });
    // Clear storage on successful submission
    sessionStorage.removeItem(STORAGE_KEY);
  };

  return (
    <form
      className={classes['form-root']}
      onSubmit={form.onSubmit(handleSubmit)}
    >
      <Box className={classes['up-wrapper']}>
        <TextInput
          label={t('FIRST_NAME_3_LETTERS')}
          placeholder={t('LAST_NAME')}
          className={classes['input-wrapper']}
          key={form.key('lastName')}
          {...form.getInputProps('lastName')}
        />
        <DateInput
          label={t('DATE_OF_BIRTH')}
          placeholder={t('DATE_OF_BIRTH_PLACEHOLDER')}
          valueFormat="DD/MM/YYYY"
          className={classes['input-wrapper']}
          key={form.key('birthDate')}
          clearable={true}
          dateParser={(date) => {
            if (!date) return null;
            const [day, month, year] = date.split('/');
            return new Date(Number(year), Number(month) - 1, Number(day));
          }}
          {...form.getInputProps('birthDate')}
        />
        <Checkbox
          label={
            <>
              {t('I_AGREE_TO')}{' '}
              <Anchor
                component={Link}
                to="/terms-and-conditions"
                underline="always"
              >
                {t('THE_TERMS_AND_CONDITIONS')}
              </Anchor>
            </>
          }
          className={classes['checkbox-wrapper']}
          key={form.key('hasAcceptedTerms')}
          {...form.getInputProps('hasAcceptedTerms', { type: 'checkbox' })}
        />
      </Box>
      <Box className={classes['down-wrapper']}>
        <Button type="submit" miw={169}>
          {signIn.isPending ? <Loader /> : t('I_AM_SIGNING_IN')}
        </Button>
      </Box>
    </form>
  );
}

export { Form };
