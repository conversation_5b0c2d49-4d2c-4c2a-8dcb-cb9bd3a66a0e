/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { InputHTMLAttributes } from 'react';
import {
  TextInput as NativeTextInput,
  TextInputProps as NativeTextInputProps,
} from '@mantine/core';

// Styles
import classes from './TextInput.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type TextInputProps = InputHTMLAttributes<HTMLButtonElement> &
  NativeTextInputProps;

function TextInput(props: TextInputProps) {
  return (
    <NativeTextInput
      classNames={{
        label: classes['text-input-label'],
        input: classes['text-input-input'],
      }}
      {...props}
    />
  );
}

export { TextInput };
