/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Config
import ppuApi from '@/configs/ppu-api.config';

// Helpers
import { makeUrl } from '@/helpers/api.helper';

/* -------------------------------------------------------------------------- */
/*                               Service class                                */
/* -------------------------------------------------------------------------- */

type GetPatientResponse = {
  name: {
    first_name: string;
  };
};

type GetPatientArgs = {
  params: {
    id: string;
  };
  headers?: {
    Authorization: string;
  };
};

class PatientService {
  /**
   * Arguments for retrieving patient information.
   *
   * @param {object} params - The parameters for the request.
   * @param {string} params.id - The unique identifier for the patient.
   * @param {object} [headers] - Optional request headers including authorization.
   */
  get = async ({ params, headers }: GetPatientArgs) => {
    // If headers are provided, they will override the axios interceptor
    // Otherwise, the axios interceptor will handle adding the auth token
    const apiResponse = (await ppuApi.get(
      makeUrl({ path: `/patient/${params.id}` }),
      headers ? { headers } : undefined
    )) as GetPatientResponse;

    return apiResponse;
  };
}

export { PatientService };
