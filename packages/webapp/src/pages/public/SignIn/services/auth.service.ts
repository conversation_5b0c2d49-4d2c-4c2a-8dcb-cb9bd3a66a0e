/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Config
import ppuApi from '@/configs/ppu-api.config';

// Helpers
import { makeUrl } from '@/helpers/api.helper';
import { Dayjs } from '@/helpers/dayjs.helper';

/* -------------------------------------------------------------------------- */
/*                               Service class                                */
/* -------------------------------------------------------------------------- */

type SignInApiResponse = {
  access_token: string;
  token_type: string;
};

type SignInArgs = {
  searchParams: {
    id: string;
    truncated_last_name: string;
    birth_date: Date | string;
  };
};

const dayjs = new Dayjs();

class AuthService {
  /**
   * Sends a sign-in request with the provided user credentials.
   * The method returns a promise that resolves to an object containing the access token and token type.
   *
   * @param {SignInArgs} args - The arguments containing the sign-in request args.
   * @param {Object} args.searchParams - The search params object containing user credentials.
   * @param {string} args.searchParams.id - The user's encounter id or share id.
   * @param {string} args.searchParams.truncated_last_name - The first 3 characters of the user's last name.
   * @param {string} args.searchParams.birth_date - The user's birth date.
   */
  signIn = async ({ searchParams }: SignInArgs) => {
    searchParams.birth_date = dayjs.formatDate({
      date: searchParams.birth_date,
    });

    const apiResponse = (await ppuApi.post(
      makeUrl({ path: `/login/access-token`, searchParams })
    )) as unknown;

    return apiResponse as SignInApiResponse;
  };
}

export { AuthService };
