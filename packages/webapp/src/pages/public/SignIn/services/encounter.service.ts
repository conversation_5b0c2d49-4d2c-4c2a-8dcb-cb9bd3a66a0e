/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Config
import ppuApi from '@/configs/ppu-api.config';

// Helpers
import { makeUrl } from '@/helpers/api.helper';
import { Encounter } from '@/models/encounter';

/* -------------------------------------------------------------------------- */
/*                               Service class                                */
/* -------------------------------------------------------------------------- */

class EncounterService {
  resetShare = async (encounterId: string) => {
    const apiResponse = (await ppuApi.post(
      makeUrl({ path: `/encounter/${encounterId}/$reset-share` })
    )) as Encounter;

    return apiResponse;
  };
}

export { EncounterService };
