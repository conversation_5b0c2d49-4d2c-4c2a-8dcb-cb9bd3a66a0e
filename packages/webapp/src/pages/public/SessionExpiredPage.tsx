/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useTranslation } from 'react-i18next';
import { Container, Title, Text, Paper, Center } from '@mantine/core';
import { useState } from 'react';

/* -------------------------------------------------------------------------- */
/*                                  Component                                 */
/* -------------------------------------------------------------------------- */

export function SessionExpiredPage() {
  const { t } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Container size="sm" my={54} w={'80%'}>
      <Paper 
        // withBorder p={40} mt={32} radius="12" style={{ boxShadow: '0 0 10px rgba(0, 123, 255, 0.6)' }}
        withBorder
        p={24}
        mt={-8}
        radius="12"
        style={{
          boxShadow: isHovered
          ? '1px 3px 8px rgba(0, 123, 255, 0.8)' // plus intense au survol
          : '1px 3px 8px rgba(0, 123, 255, 0.4)',
          transition: 'box-shadow 0.3s ease-in-out',
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Center>
          <Title order={2} ta="center" mb="md" c="red">
            <h3>{t('SESSION_EXPIRED_PAGE_TITLE')}</h3>
          </Title>
        </Center>
        <Text c="dimmed" size="md" ta="center" mb="lg">
          {t('SESSION_EXPIRED_PAGE_MESSAGE')}
        </Text>
      </Paper>
    </Container>
  );
}