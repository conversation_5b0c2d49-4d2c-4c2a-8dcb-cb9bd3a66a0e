/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Config
import ppuApi from '@/configs/ppu-api.config';

// Helpers
import { makeUrl } from '@/helpers/api.helper';

// Types
import { AnswerOption } from '@/models/questionnaire';

/* -------------------------------------------------------------------------- */
/*                               Service class                                */
/* -------------------------------------------------------------------------- */

type PostQuestionnaireResponseArgs = {
  body: {
    questionnaire_id: string;
    subject: string;
    item: {
      link_id: string;
      answer: AnswerOption[];
    }[];
  };
};

class QuestionnaireResponseService {
  post = async ({ body }: PostQuestionnaireResponseArgs) => {
    const apiResponse = (await ppuApi.post(
      makeUrl({ path: `/questionnaire-response` }),
      body
    )) as null;

    return apiResponse;
  };
}

export { QuestionnaireResponseService };
