/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useMantineTheme, Box } from '@mantine/core';

// Components
import { Icons } from '@/components/Icons';

// Styles
import classes from './Queue.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type QueueProps = {
  personsCount: number;
};

const MAX_PERSONS_COUNT = 10;

export function Queue(props: QueueProps) {
  const { personsCount } = props;
  const theme = useMantineTheme();

  if (personsCount < 0) {
    return <></>;
  }

  return (
    <Box className={classes['queue-root']}>
      <Icons.PersonStanding
        color={theme.colors.green[0]}
        className={classes.icon}
      />
      {Array.from(
        {
          length:
            personsCount > MAX_PERSONS_COUNT
              ? MAX_PERSONS_COUNT - 1
              : personsCount - 1,
        },
        (_, index) => (
          <RandomPersonIcon key={index} />
        )
      )}
      {personsCount > 0 ? (
        <Icons.PersonWalking
          color={theme.colors.blue[0]}
          className={classes.icon}
        />
      ) : (
        <></>
      )}
    </Box>
  );
}

function RandomPersonIcon() {
  const randomNum = Math.floor(Math.random() * 2);
  const theme = useMantineTheme();

  if (!randomNum) {
    return (
      <Icons.PersonStanding
        color={theme.colors.gray[1]}
        className={classes.icon}
      />
    );
  }

  return (
    <Icons.PersonStandingDress
      color={theme.colors.gray[1]}
      className={classes.icon}
    />
  );
}
