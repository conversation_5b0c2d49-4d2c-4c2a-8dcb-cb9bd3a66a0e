.stepper-progress-root {
  position: fixed;
  z-index: 100;
  top: 82.5px;
  left: 0px;
  width: 100%;
  box-shadow: 0 4px 8px -2px rgba(0, 0, 0, 0.3);
  padding: 15px 40px 12px;
  background-color: var(--mantine-color-white);
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 11px;
}

.steps-wrapper {
  position: relative;
  width: 247px;
  padding: 18px 0px;
}

.divider {
  height: 1px;
  background-color: var(--mantine-color-gray-1);
}

.steps-root {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.step {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: solid 1px var(--mantine-color-gray-1);
  background-color: var(--mantine-color-white);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--mantine-color-gray-1);
  transition: all 300ms ease;
  cursor: pointer;
}

.active-step {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: solid 4px var(--mantine-color-white);
  background-color: var(--mantine-color-blue-0);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--mantine-color-white);
  font-weight: 900;
  font-size: var(--mantine-font-size-lg);
  transition: all 300ms ease;
  cursor: pointer;
}

.title {
  color: var(--mantine-color-gray-1);
  font-size: rem(13);
  line-height: rem(16);
}

.active-step-title {
  color: var(--mantine-color-blue-0);
}
