/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box, Flex, Skeleton } from '@mantine/core';
import { useTranslation } from 'react-i18next';

// Components
import { Divider } from '@/components/Divider';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Styles
import classes from './StepperProgress.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function StepperProgressLoading() {
  const { t } = useTranslation();

  return (
    <Box className={classes['stepper-progress-root']}>
      <Box className={classes['steps-wrapper']}>
        <Divider className={classes['divider']} />
        <Box className={classes['steps-root']}>
          {Object.values(STATIC_STEPS).map((step) => (
            <Skeleton key={step.id} radius="50%" w={25} h={25} />
          ))}
        </Box>
      </Box>

      <Flex className={classes['title']} columnGap={5} align="center">
        {t('YOU_ARE_AT_THE_STEP')} : {}
        <Skeleton h={16} w={200} />
      </Flex>
    </Box>
  );
}

export { StepperProgressLoading };
