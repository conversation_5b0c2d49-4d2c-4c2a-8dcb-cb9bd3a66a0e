/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Hooks
import { useView } from '@/pages/private/Journey/hooks/view.hook';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type StepTitleProps = {
  stepId: StepId;
};

function StepTitle(props: StepTitleProps) {
  const { stepId } = props;
  // `loading` is always `false` when this component renders,
  // as it's only displayed when loading is complete (handled by a condition in the page component).
  // However, we explicitly check `isLoading === false` when rendering the UI
  // to safeguard against any potential future changes in the page component's logic.
  const { data, isLoading } = useView();

  if (stepId === '1') return <>{STATIC_STEPS[1].title}</>;

  if (stepId === '2') {
    if (!isLoading) return <>{data?.formattedResponse.steps[2].title}</>;
  }

  if (stepId === '3') {
    if (!isLoading) return <>{data?.formattedResponse.steps[3].title}</>;
  }

  if (stepId === '4') return <>{STATIC_STEPS[4].title}</>;

  return <></>;
}

export { StepTitle };
