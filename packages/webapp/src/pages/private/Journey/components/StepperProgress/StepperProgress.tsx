/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box, Title } from '@mantine/core';
import { useTranslation } from 'react-i18next';

// Components
import { Divider } from '@/components/Divider';
import { StepTitle } from '@/pages/private/Journey/components/StepperProgress/StepTitle';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Stores
import useJourney from '@/pages/private/Journey/stores/journey';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';

// Styles
import classes from './StepperProgress.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type StepperProgressProps = {
  viewedStepId: StepId;
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
};

function StepperProgress(props: StepperProgressProps) {
  const { setViewedStepId } = props;
  const { defaultActiveStepId } = useJourney();
  const { t } = useTranslation();

  return (
    <Box className={classes['stepper-progress-root']}>
      <Box className={classes['steps-wrapper']}>
        <Divider className={classes['divider']} />
        <Box className={classes['steps-root']}>
          {Object.values(STATIC_STEPS).map((step) => (
            <Box
              key={step.id}
              className={
                step.id === defaultActiveStepId
                  ? classes['active-step']
                  : classes['step']
              }
              role="button"
              onClick={() => setViewedStepId(step.id)}
            >
              {step.id}
            </Box>
          ))}
        </Box>
      </Box>

      <Title className={classes['title']}>
        {t('YOU_ARE_AT_THE_STEP')} : {}
        <Title component="span" className={classes['active-step-title']}>
          {defaultActiveStepId && <StepTitle stepId={defaultActiveStepId} />}
        </Title>
      </Title>
    </Box>
  );
}

export { StepperProgress };
