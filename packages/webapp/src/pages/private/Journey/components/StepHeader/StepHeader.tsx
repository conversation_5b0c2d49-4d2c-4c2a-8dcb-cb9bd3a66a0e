/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box, Text, Accordion } from '@mantine/core';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';

// Styles
import classes from './StepHeader.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type StepHeaderProps = {
  stepId: StepId;
  stepTitle: string;
  viewedStepId: StepId;
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
};

function StepHeader(props: StepHeaderProps) {
  const { stepId, stepTitle, viewedStepId, setViewedStepId } = props;

  return (
    <Accordion.Control
      icon={<Box className={classes['step-header-icon']}>{stepId}</Box>}
      classNames={{
        control: classes['step-header-control'],
        label: classes['step-header-label'],
        chevron: classes['step-header-chevron'],
      }}
      chevron={<></>}
      onClick={() => setViewedStepId(stepId)}
      style={{
        opacity: stepId === viewedStepId ? 1 : 0.5,
      }}
    >
      <Text component="h2" className={classes['step-header-text']}>
        {stepTitle}
      </Text>
    </Accordion.Control>
  );
}

export { StepHeader };
