/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box, Container, Flex, Skeleton } from '@mantine/core';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Styles
import classes from './Steps.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function StepsLoading() {
  return (
    <Box className={classes['steps-root']}>
      <Container size="xs" className={classes['steps-wrapper']}>
        <Flex direction="column" align="center" rowGap={70}>
          {Object.values(STATIC_STEPS).map((step) => (
            <Skeleton key={step.id} radius={15} w={303} h={29} />
          ))}
        </Flex>
        <Skeleton />
      </Container>
    </Box>
  );
}

export { StepsLoading };
