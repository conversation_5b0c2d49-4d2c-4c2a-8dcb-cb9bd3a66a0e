/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Container, Accordion } from '@mantine/core';

// Components
import { Step1 } from '@/pages/private/Journey/components/Step1';
import { Step2 } from '@/pages/private/Journey/components/Step2';
import { Step3 } from '@/pages/private/Journey/components/Step3';
import { Step4 } from '@/pages/private/Journey/components/Step4';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';

// Styles
import classes from './Steps.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type StepsProps = {
  viewedStepId: StepId;
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
};

function Steps(props: StepsProps) {
  const { viewedStepId, setViewedStepId } = props;

  return (
    <Accordion
      className={classes['steps-root']}
      variant="filled"
      value={viewedStepId}
    >
      <Container size="xs" className={classes['steps-wrapper']}>
        <Step1 setViewedStepId={setViewedStepId} viewedStepId={viewedStepId} />
        <Step2 setViewedStepId={setViewedStepId} viewedStepId={viewedStepId} />
        <Step3 setViewedStepId={setViewedStepId} viewedStepId={viewedStepId} />
        <Step4 setViewedStepId={setViewedStepId} viewedStepId={viewedStepId} />
      </Container>
    </Accordion>
  );
}

export { Steps };
