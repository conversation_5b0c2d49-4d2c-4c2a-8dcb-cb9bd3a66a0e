/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  ActionIcon,
  Button,
  Center,
  Flex,
  rem,
  Text,
  useMantineTheme,
} from '@mantine/core';


// Components
import {
  AnimatedContent,
  AnimationWrapper,
} from '@/pages/private/Journey/components/AnimatedContent';
import { Icons } from '@/components/Icons';

// Types
import { DynamicStep2 } from '@/pages/private/Journey/types/stepper';
import { IconBellRinging } from '@tabler/icons-react';
import { openContextModal } from '@mantine/modals';
// import { useTranslation } from 'react-i18next';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type IoaServiceRequestContentProps = {
  data: DynamicStep2;
};

function IoaServiceRequestContent(props: IoaServiceRequestContentProps) {
  const { data } = props;
  const theme = useMantineTheme();
  // const { t } = useTranslation();

  return (
    <AnimationWrapper>
      {!data.ioaServiceRequest ||
      data.ioaServiceRequest.status === 'not-yet-started' ||
      data.ioaServiceRequest.status === 'cancelled' 
      // || data.ioaServiceRequest.status === 'in-progress' ||
      // data.ioaServiceRequest.status === 'waiting-for-results' 
      ? (
        <AnimatedContent key="val1">
          <Center pt={10}>
            <Button
              variant="outline"
              bg="rgb(255, 213, 213)"
              c={theme.black}
              fw={500}
              h={32}
              fz={rem(16)}
              px={18}
              style={{ border: 'solid 0.5px silver' }}
              radius="xl"
              mt={14}
              mb={-8}
            >
              Vous allez bientôt voir l'IOA &nbsp;
              <IconBellRinging width={20} height={20} />
            </Button>
          </Center>
        </AnimatedContent>
      ) : null}

      {data.ioaServiceRequest?.status === 'in-progress' ||
      data.ioaServiceRequest?.status === 'waiting-for-results' 
      ?(
        <AnimatedContent key="val1">
          <Center pt={10}>
            <Button
              variant="outline"
              bg="rgb(255, 213, 213)"
              c={theme.black}
              fw={500}
              h={32}
              fz={rem(16)}
              px={18}
              style={{ border: 'solid 0.5px silver' }}
              radius="xl"
              mt={14}
              mb={-8}
            >
              En cours d'évaluation par l'IOA &nbsp;
              <IconBellRinging width={20} height={20} />
            </Button>
          </Center>
        </AnimatedContent>
      ) : null}

      {data.ioaServiceRequest?.status === 'completed' ? (
        <AnimatedContent>
          <Text c="gray.3" fz={rem(12.5)} lh={rem(15)} ta="center" mt={8} pb={10}>
            Vous êtes affecté à la filière
          </Text>
          <Flex justify="center" align="center" columnGap={5} pl={28} mt={4}>
            <Button
              variant="outline"
              bg="blue.6"
              c={theme.white}
              fw={900}
              // h={28}
              fz={rem(15)}
              // px={17}
              pt={4}
              pr={17}
              pb={5}
              pl={17}
              style={{ border: 'none' }}
            >
              {data.affectedHealthcareService ?? 'Service non défini'}
            </Button>
              <ActionIcon
                variant="transparent"
                size="xs"
                // style={{ whiteSpace: 'pre-wrap' }}
                onClick={() =>
                  openContextModal({
                    modal: 'info', 
                    innerProps: {
                      title: 'L\'organisation des urgences en fililères, à quoi ça sert ?',
                      description:
                      "Afin d'optimiser l'efficacité et la réactivité du service des urgences, vous allez être affecté à une des 'filières' suivantes. \r\n" + 
                      "La filière 'Ambulatoire' est destinée aux patients dont la situation est rapidement identifiable et dont le diagnostic peut être établi rapidement. Elle permet une prise en charge accélérée, avec une évaluation, des examens et une décision (sortie, transfert ou admission dans une unité spécialisée) effectués dans un délai réduit. L'objectif est de désengorger le service des urgences en orientant rapidement les patients qui ne nécessitent pas un bilan complet. \n\n" + 
                      "La filière 'Hospitalisation' s'applique aux patients présentant des symptômes ou une situation clinique plus complexe, nécessitant un bilan complet avec des examens complémentaires et une observation prolongée. Ces patients passent par plusieurs étapes d'évaluation avant qu'une décision thérapeutique ou d'orientation ne soit prise. Le circuit long est associé à des délais d'attente plus longs, reflétant la complexité de la prise en charge." ,
                    },
                  })
                }
              >
              <Icons.Info />
            </ActionIcon>
          </Flex>
        </AnimatedContent>
      ) : null}
    </AnimationWrapper>
  );
}

export { IoaServiceRequestContent };
