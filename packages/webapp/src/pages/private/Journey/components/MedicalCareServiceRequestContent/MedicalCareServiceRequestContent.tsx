/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Center, Flex, rem, Text, useMantineTheme } from '@mantine/core';
import {
  IconBellRinging,
  IconStopwatch,
  IconStethoscope,
} from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';

// Components
import {
  AnimationWrapper,
  AnimatedContent,
} from '@/pages/private/Journey/components/AnimatedContent';
import { Button } from '@/components/Button';
import { Queue } from '@/pages/private/Journey/components/Queue';

// Hooks
import { useDurationFromNow } from '@/pages/private/Journey/hooks/duration.hook';

// Types
import { MedicalCareServiceRequest } from '@/pages/private/Journey/types/stepper';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type MedicalCareServiceRequestContentProps = {
  data?: MedicalCareServiceRequest;
};

export function MedicalCareServiceRequestContent(
  props: MedicalCareServiceRequestContentProps
) {
  const { data } = props;
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const duration = useDurationFromNow(
    data && data.status && data.status == 'in-progress'
      ? data.plannedEndDate
      : ''
  );

  return (
    <AnimationWrapper>
      {!data ||
      (data && !data.status) ||
      (data &&
        (data.status === 'not-yet-started' || data.status === 'cancelled')) ? (
        <AnimatedContent key="val1">
          <Center py={50}>
            <Text c="gray.0" fw={700} ta="center">
              {t('AWAITING_MEDICAL_CARE')}
            </Text>
          </Center>
        </AnimatedContent>
      ) : null}

      {data &&
      data.status &&
      (data.status === 'waiting-for-results') ? (
        <AnimatedContent key="val2">
          <Flex pt={10} direction="column" align="center" rowGap={3} mt={2}>
            <IconStethoscope size={32} color={theme.colors.dark[2]} />
            <Text c="green.7" fw={700} ta="center" mt="4">
              {t('CARE_BY_THE_DOCTOR')}
            </Text>
          </Flex>
        </AnimatedContent>
      ) : null}

      {data &&
      data.status &&
      (data.status === 'completed') ? (
        <AnimatedContent key="val2">
          <Flex pt={10} direction="column" align="center" rowGap={3} mb={16}>
            <IconStethoscope size={32} color={theme.colors.dark[3]} />
            <Text fw={700} ta="center" c="dodgerblue" mt="6" mb="-16">
              {t('DONE_WITH_CARE_BY_THE_DOCTOR')} &nbsp;√
            </Text>
          </Flex>
        </AnimatedContent>
      ) : null}

      {data &&
      data.status &&
      data.status === 'in-progress' &&
      data.waitCount > 5 ? (
        <AnimatedContent key="val3">
          <Flex direction="column" align="center" pt={6}>
            <Text
              c="gray.0"
              fw={700}
              fz={rem(12)}
              lh={rem(15)}
              ta="center"
              pb={16}
            >
              {t('UPCOMING_MEDICAL_CARE')} 
            </Text>
            <Queue personsCount={data.waitCount} />
            <Text c="gray.3" fz={rem(13)} lh={rem(15)} ta="center">
              {t('IT_REMAINS')}
            </Text>
            <Text c="gray.0" fw={500} lh={rem(15)} ta="center" pb={10}>
              <Text c="blue.0" component="span">
                {data.waitCount}{' '}
              </Text>
              {t('PEOPLE_AHEAD_OF_YOU')}
            </Text>

            <Flex justify="center" align="center">
              <Text c="gray.3" fz={rem(13.5)} lh={rem(15)} ta="center"> 
                <IconStopwatch color={theme.colors.gray[3]} style={{ verticalAlign: 'top', marginRight: '2px', marginTop: '-1px', lineHeight: '14px' }} />
                {t('ESTIMATED_WAIT_TIME_1')} <br></br>
                {t('ESTIMATED_WAIT_TIME_2')} : {' '}
                <Text c="blue.0" fz={theme.fontSizes.md} component="span">
                  {data.plannedEndDate &&
                  data.plannedEndDate > Date.now() / 1000
                    ? duration
                    : t('IMMINENT_CARE')}
                  . 
                </Text>
                <Text c="red" fz={rem(12)} lh={rem(17)} mt={rem(12)} ta="center">
                  Les patients sont pris en charge en fonction de leur<br />état de santé et non pas selon leur ordre d’arrivée.
                </Text>
              </Text>
            </Flex>
          </Flex>
        </AnimatedContent>
      ) : null}

      {data &&
      data.status &&
      data.status === 'in-progress' &&
      data.waitCount <= 5 && data.waitCount >= 1 ? (
        <AnimatedContent key="val4">
          <Flex direction="column" align="center" pt={6}>
          <Text
              c="gray.0"
              fw={700}
              fz={rem(12)}
              lh={rem(15)}
              ta="center"
              pb={16}
            >
              {t('UPCOMING_MEDICAL_CARE')} 
            </Text>
            <Queue personsCount={data.waitCount} />
            <Button
              variant="outline"
              bg="red.6"
              c={theme.white}
              fw={700}
              h={30}
              fz={rem(16)}
              px={16}
              pb={2}
              style={{ border: 'none' }}
            >
              {data.waitCount}{' '}
              {data.waitCount > 1
                ? t('PEOPLE_AHEAD_OF_YOU')
                : t('PERSON_AHEAD_OF_YOU')}
              <IconBellRinging width={20} height={20} />
            </Button>

            <Flex justify="center" align="center" pt={16}>
              <Text c="gray.3" fz={rem(13.5)} lh={rem(15)} ta="center">
                <IconStopwatch color={theme.colors.gray[3]} style={{ verticalAlign: 'top', marginRight: '2px', marginTop: '-4px', lineHeight: '14px' }} />
                {t('ESTIMATED_WAIT_TIME_1')} <br></br>
                {t('ESTIMATED_WAIT_TIME_2')} : {' '}
                <Text c="blue.0" fz={theme.fontSizes.md} component="span">
                  {data.plannedEndDate 
                  // && data.plannedEndDate > Date.now() / 1000
                    ? duration 
                    : t('IMMINENT_CARE')} 
                  . 
                </Text>
                <Text c="red.8" fz={rem(12)} lh={rem(17)} mt={rem(12)} ta="center">
                  Les patients sont pris en charge en fonction de leur<br />état de santé et non pas selon leur ordre d’arrivée.
                </Text>
              </Text>
            </Flex>
          </Flex>
        </AnimatedContent>
      ) : null}

{data &&
      data.status &&
      data.status === 'in-progress' &&
      data.waitCount == 0 ? (
        <AnimatedContent key="val4">
          <Flex direction="column" align="center" pt={6}>
          <Text
              c="gray.0"
              fw={700}
              fz={rem(12)}
              lh={rem(15)}
              ta="center"
              pb={16}
            >
              {t('UPCOMING_MEDICAL_CARE')} 
            </Text>
            <Queue personsCount={data.waitCount} />
            <Button
              variant="outline"
              bg="red.6"
              c={theme.white}
              fw={700}
              h={30}
              fz={rem(16)}
              px={16}
              pb={2}
              style={{ border: 'none' }}
            >
              {/* {data.waitCount}{' '} */}
              {data.waitCount > 1
                ? t('ITS_YOUR_TURN')
                : t('ITS_YOUR_TURN')}
              <IconBellRinging width={20} height={20} />
            </Button>

            <Flex justify="center" align="center" pt={16}>
              <Text c="gray.3" fz={rem(13.5)} lh={rem(15)} ta="center">
                <IconStopwatch color={theme.colors.gray[3]} style={{ verticalAlign: 'top', marginRight: '2px', marginTop: '-4px', lineHeight: '14px' }} />
                {t('ESTIMATED_WAIT_TIME_1')} <br></br>
                {t('ESTIMATED_WAIT_TIME_2')} : {' '}
                <Text c="blue.0" fz={theme.fontSizes.md} component="span">
                  {data.plannedEndDate 
                  // && data.plannedEndDate > Date.now() / 1000
                    ? duration 
                    : t('IMMINENT_CARE')} 
                  . 
                </Text>
                <Text c="red.8" fz={rem(12)} lh={rem(17)} mt={rem(12)} ta="center">
                  Les patients sont pris en charge en fonction de leur<br />état de santé et non pas selon leur ordre d’arrivée.
                </Text>
              </Text>
            </Flex>
          </Flex>
        </AnimatedContent>
      ) : null}

    </AnimationWrapper>
  );
}
