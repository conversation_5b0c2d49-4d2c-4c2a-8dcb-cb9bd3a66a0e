/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  ActionIcon,
  Box,
  Flex,
  Text,
  Title,
  Tooltip,
  useMantineTheme,
} from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { IconStopwatch } from '@tabler/icons-react';

// Constants
import { SERVICE_REQUEST_STATUS_LABELS } from '@/pages/private/Journey/constants/stepper';
// import { RESULTS_AVAILABLE_MORE } from '@/locales/fr.json';

// Components
import { Icons } from '@/components/Icons';
import {
  AnimationWrapper,
  AnimatedContent,
} from '@/pages/private/Journey/components/AnimatedContent';

// Types
import { OtherServicesRequests } from '@/pages/private/Journey/types/stepper';

// Hooks
import { useDurationFromNow } from '@/pages/private/Journey/hooks/duration.hook';

// Styles
import classes from './OtherServiceRequestContent.module.css';
import { ActivityDefinition } from '@/models/activity_definition';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type OtherServiceRequestContentProps = {
  data: OtherServicesRequests;
  activityDefinitions: ActivityDefinition[];
};

export function OtherServiceRequestContent(
  props: OtherServiceRequestContentProps
) {
  const { data, activityDefinitions } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const duration = useDurationFromNow(
    data.status && data.status == 'in-progress' ? data.plannedEndDate : ''
  );
  const meanWaitTime = activityDefinitions.find(
    (e) => e.activity_definition_id === data.activityDefinition.id
  )?.default_wait_time;

  return (
    <Box className={classes['other-service-request-root']}>
      <Box className={classes['title-wrapper']}>
        <Title className={classes['title']} component="h3">
          {data.activityDefinition.name}
        </Title>
        <Tooltip
          label={data.activityDefinition.description}
          events={{
            focus: true,
            hover: true,
            touch: true,
          }}
        >
          <ActionIcon variant="transparent" size="xs">
            <Icons.Info />
          </ActionIcon>
        </Tooltip>
      </Box>
      <AnimationWrapper>
        <AnimatedContent key={data.id}>
          <Text className={classes['value']} mt={6}>
            {
              SERVICE_REQUEST_STATUS_LABELS[
                (data.status && data.status) ?? 'not-yet-started'
              ]
            }
            <br />
            {/* {t('RESULTS_AVAILABLE_MORE')} */}
          </Text>
          {data.status && data.status === 'in-progress' ? (
            <>
              <Text
                ta="center"
                className={classes['progress-info']}
                hidden={data.waitCount === 0}
              >
                {t('IT_REMAINS')}{' '}
                <Text component="span" c="gray.0">
                  {data.waitCount}
                </Text>{' '}
                {t('PEOPLE_AHEAD_OF_YOU')}
              </Text>
              {data.plannedEndDate === 0 && !meanWaitTime ? (
                <></> // No mean wait time and no planned end date
              ) : (
                <Flex justify="center" align="center">
                  <IconStopwatch color={theme.colors.gray[3]}/>
                  <Text
                    className={classes['progress-info']}
                    hidden={data.plannedEndDate === 0}
                  >
                    {t('ESTIMATED_WAIT_TIME')} : {' '}
                    <Text c="blue.0" component="span">
                      {data.plannedEndDate &&
                      data.plannedEndDate > Date.now() / 1000
                        ? duration
                        : t('IMMINENT_CARE')}
                      .
                    </Text>
                  </Text>
                  <Text
                    className={classes['progress-info']}
                    hidden={data.plannedEndDate !== 0 || !meanWaitTime}
                  >
                    {t('MEAN_WAIT_TIME')} :{' '}
                    <Text c="blue.0" component="span">
                      {Math.floor((meanWaitTime ?? 0) / 60)}h
                      {(meanWaitTime ?? 0) % 60 !== 0 &&
                        `${(meanWaitTime ?? 0) % 60}min`}
                    </Text>
                  </Text>
                </Flex>
              )}
            </>
          ) : (
            <></>
          )}
        </AnimatedContent>
      </AnimationWrapper>
    </Box>
  );
}
