/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  ActionIcon,
  Box,
  Flex,
  rem,
  Text,
  useMantineTheme,
  Anchor,
} from '@mantine/core';
import { IconDownload } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';

// Config
import { env } from '@/configs/env.config';

// Components
import {
  AnimatedContent,
  AnimationWrapper,
} from '@/pages/private/Journey/components/AnimatedContent';
import { Icons } from '@/components/Icons';
import { openContextModal } from '@mantine/modals';

// Types
import { Discharge } from '@/models/encounter';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type DischargeContentProps = {
  data?: Discharge | null;
};

function DischargeContent(props: DischargeContentProps) {
  const { data } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();

  return (
    <AnimationWrapper>
      {!data ||
      !data.disposition ||
      data.disposition === 'pending' ? (
        <AnimatedContent key="val1">
          <Flex px={20} justify="center" columnGap={5} pb={30}>
            <Text c="gray.0" fw={700} fz={rem(12)} mt={2} ml={6} lh={rem(15)} ta="center">
              {t('UPCOMING_ORIENTATION_DECISION')}
            </Text>
              <ActionIcon
                variant="transparent"
                size="xs"
                onClick={() =>
                  openContextModal({
                    modal: 'info',
                    innerProps: {
                      title: t('UPCOMING_ORIENTATION_DECISION_COMMENT'),
                      // title: 'Title',
                      description:
                        "En fonction de votre état de santé, le docteur qui vous a pris en charge décidera de votre orientation post-urgences : Le retour domicile, l'hospitalisation dans les services de l'hôpital ou le transfert vers une autre structure d'hospitalisation, plus adaptée à votre situation.",
                    },
                  })
                }
              >
              <Icons.Info />
            </ActionIcon>
          </Flex>
        </AnimatedContent>
      ) : null}

      {data?.disposition === 'same-hospital' ? (
        <AnimatedContent key="val2">
          <Box px={20}>
            <Text
              c="blue.5"
              fz={theme.fontSizes.lg}
              lh={rem(22)}
              ta="center"
              pb={4}
              mt={6}
            >
              {t('YOU_WILL_BE_ADMITTED_TO_THE_DEPARTMENT')} <br />
              {t('THE_DEPARTMENT')} 
              {data.destination?.main_destination}
            </Text>
            <Text c="gray.3" fz={rem(12)} lh={rem(15)} ta="center">
              {data.destination?.details}
            </Text>
          </Box>
        </AnimatedContent>
      ) : null}

      {data?.disposition === 'other-hospital' ? (
        <AnimatedContent key="val3">
          <Box px={20}>
            <Text
              c="pink.6"
              fz={theme.fontSizes.lg}
              lh={rem(22)}
              ta="center"
              pb={4}
              mt={6}
            >
              {t('YOU_ARE_TRANSFERRED_TO')} <br />
              dans un autre établissement
            </Text>
            <Text
              c="pink.6"
              fz={theme.fontSizes.lg}
              lh={rem(17)}
              ta="center"
              pb={4}
              mt={4}
            >
              {data.destination?.main_destination}
            </Text>
            <Text c="gray.3" fz={rem(12)} lh={rem(15)} ta="center" mt={2}> 
              {data.destination?.details}
            </Text>
          </Box>
        </AnimatedContent>
      ) : null}

      {data?.disposition === 'home' && (
        <AnimatedContent key="val4-home">
          <Flex justify="center" align="flex-end" columnGap={8} px={20}>
            <Box>
              <Text
                c="green.8"
                fz={theme.fontSizes.lg}
                lh={rem(22)}
                ta="center"
                pb={4}
                mt={6}
              >
                {t('YOU_ARE_AUTHORIZED_TO')} <br />
                {t('TO_RETURN_TO_YOUR_HOME')}
              </Text>
            </Box>
          </Flex>
        </AnimatedContent>
      )}

      {(data?.disposition === 'same-hospital' ||
        data?.disposition === 'other-hospital' ||
        data?.disposition === 'other') && (
        <AnimatedContent key="val4-other">
          <Flex justify="center" align="flex-end" columnGap={8} px={20}>
            <Box>
              <Text
                c="blue.8"
                fz={theme.fontSizes.lg}
                lh={rem(17)}
                ta="center"
                pb={4}
                mt={6} // ?
              >
                {/* {t('BEFORE_DISCHARGE')} */}
              </Text>
            </Box>
          </Flex>
        </AnimatedContent>
      )}

      <AnimatedContent key="val5">
        <Flex justify="center" align="flex-end" columnGap={8} px={20}>
          <Box>
            {data?.instructions && data.instructions.length > 0 ? (
              <>
                <Text
                  c="gray.3"
                  fz={rem(12)}
                  fw={500}
                  ta="center"
                  pt={10}
                  pb={1}
                >
                  {t('DOWNLOAD_ADAPTED_MONITORING_INSTRUCTIONS')}
                </Text>
                <Flex direction="column" align="flex-start">
                  {data.instructions.map((instruction, index) => (
                    <Flex
                      key={index}
                      justify="space-between"
                      align="center"
                      w="100%"
                      style={{
                        borderBottom: `1px solid ${theme.colors.gray[1]}`,
                      }}
                    >
                      <Anchor
                        href={`${env.ppu.api.baseUrl}/document_reference/${instruction.id}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        fz={rem(12)}
                        c="gray.3"
                      >
                        {instruction.name}
                      </Anchor>
                      <ActionIcon
                        bg="transparent"
                        onClick={() =>
                          window.open(
                            `${env.ppu.api.baseUrl}/document_reference/${instruction.id}`,
                            '_blank'
                          )
                        }
                        c="gray.3"
                      >
                        <IconDownload size={16} stroke={1.5} />
                      </ActionIcon>
                    </Flex>
                  ))}
                </Flex>
              </>
            ) : null}
          </Box>
        </Flex>
      </AnimatedContent>

      {data?.disposition === 'against-medical-advice' ||
      data?.disposition === 'escaped' ? (
        <AnimatedContent key="val6">
          <Box px={20}>
            <Text
              c="red.5"
              fz={theme.fontSizes.lg}
              lh={rem(17)}
              ta="center"
              pb={4}
            >
              {t('PATIENT_LEFT')}
            </Text>
            <Text c="gray.3" fz={rem(12)} lh={rem(15)} ta="center">
              {data.destination?.details}
            </Text>
          </Box>
        </AnimatedContent>
      ) : null}
    </AnimationWrapper>
  );
}

export { DischargeContent };
