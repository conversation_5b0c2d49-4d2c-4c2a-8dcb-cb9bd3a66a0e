/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type AnimationWrapperProps = {
  children?: ReactNode;
};

export function AnimationWrapper(props: AnimationWrapperProps) {
  const { children } = props;

  return <AnimatePresence mode="wait">{children}</AnimatePresence>;
}

type AnimatedContentProps = {
  children?: ReactNode;
};

export function AnimatedContent(props: AnimatedContentProps) {
  const { children } = props;

  return (
    <motion.div
      initial={{ x: 100, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -100, opacity: 0 }}
      transition={{
        duration: 0.8,
        ease: 'easeInOut',
      }}
    >
      {children}
    </motion.div>
  );
}
