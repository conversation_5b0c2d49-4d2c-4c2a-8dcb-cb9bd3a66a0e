/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Accordion, Box } from '@mantine/core';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Components
import { StepHeader } from '@/pages/private/Journey/components/StepHeader';
import { MedicalCareServiceRequestContent } from '@/pages/private/Journey/components/MedicalCareServiceRequestContent';
import { OtherServiceRequestContent } from '@/pages/private/Journey/components/OtherServiceRequestContent';
import { EstimatedDuration } from '@/pages/private/Journey/components/EstimatedDuration';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';

// Hooks
import { useView } from '@/pages/private/Journey/hooks/view.hook';

// Stores
import useJourney from '@/pages/private/Journey/stores/journey';

// Styles
import classes from './Step3.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type Step3Props = {
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
  viewedStepId: StepId;
};

function Step3(props: Step3Props) {
  const { setViewedStepId, viewedStepId } = props;
  // `loading` is always `false` when this component renders,
  // as it's only displayed when loading is complete (handled by a condition in the page component).
  // However, we explicitly check `isLoading === false` when rendering the UI
  // to safeguard against any potential future changes in the page component's logic.
  const { data, isLoading } = useView();
  const { defaultActiveStepId } = useJourney();

  const activityDefinitions = data?.response?.healthcare_service?.flatMap(
    (e) => e.activity_definition ?? []
  );

  // if (defaultActiveStepId === '1') return <>{STATIC_STEPS[1].title}</>;

  return (
    <Accordion.Item
      key={STATIC_STEPS[3].id}
      value={STATIC_STEPS[3].id}
      className={classes['step-3-root']}
      component="section"
    >
      <StepHeader
        setViewedStepId={setViewedStepId}
        viewedStepId={viewedStepId}
        stepId={STATIC_STEPS[3].id}
        stepTitle={
          !isLoading && data?.formattedResponse && defaultActiveStepId === '3'
            ? data.formattedResponse.steps[3].title + "\u00A0\u00A0📌\u00A0\u00A0"
            :
            !isLoading && data?.formattedResponse
            ? data.formattedResponse.steps[3].title
            : ''
        }
      />
      <Accordion.Panel
        classNames={{
          panel: classes['accordion-panel'],
          content: classes['accordion-content'],
        }}
      >
        {!isLoading && data?.formattedResponse.steps[3] ? (
          <>
            <Box
              className={
                classes['step-3-medicale-care-service-request-wrapper']
              }
              >
              <MedicalCareServiceRequestContent
                data={data.formattedResponse.steps[3].medicalCareServiceRequest}
                />
                {data.formattedResponse.steps[3].plannedLength && (
                  <EstimatedDuration plannedLength={data.formattedResponse.steps[3].plannedLength} />
                )}
            </Box>
          </>
        ) : (
          <></>
        )}

        {!isLoading &&
        data?.formattedResponse.steps[3] &&
        data.formattedResponse.steps[3].otherServicesRequests &&
        data.formattedResponse.steps[3].otherServicesRequests.length ? (
          <Box className={classes['other-services-requests-wrapper']}>
            {data.formattedResponse.steps[3].otherServicesRequests.map(
              (serviceRequest, index) => (
                <OtherServiceRequestContent
                  key={index}
                  data={serviceRequest}
                  activityDefinitions={activityDefinitions ?? []}
                />
              )
            )}
          </Box>
        ) : (
          <></>
        )}
      </Accordion.Panel>
    </Accordion.Item>
  );
}

export { Step3 };
