import { Footer } from '@/pages/private/Journey/components/Footer';
import { Container, Stack, Title } from '@mantine/core';
import classes from './StaticTextPage.module.css';

interface StaticTextPageProps {
  title: string;
  children: React.ReactNode;
}

export function StaticTextPage({ title, children }: StaticTextPageProps) {
  return (
    <>
      <Container size="sm">
        <Title component="h1" className={classes['title']}>
          {title}
        </Title>
        <Stack gap="sm">{children}</Stack>
      </Container>
      <Footer />
    </>
  );
}
