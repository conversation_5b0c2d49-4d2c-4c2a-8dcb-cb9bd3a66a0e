import { Text } from '@mantine/core';
import { formatDuration } from '@/utils/formatDuration';
import { useTranslation } from 'react-i18next';
import {
  rem,
  useMantineTheme
} from '@mantine/core';
import {
  IconStopwatch
} from '@tabler/icons-react';

interface EstimatedDurationProps {
  plannedLength: string | number;
}

function EstimatedDuration({ plannedLength }: EstimatedDurationProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();

  return (
    <Text c="gray.3" fz={rem(13.5)} lh={rem(19)} ta="center" mt={16} mb={-16}>
        <IconStopwatch color={theme.colors.gray[3]} style={{ verticalAlign: 'top', marginRight: '2px', marginTop: '-2px', lineHeight: '14px' }} />
        {t('ESTIMATED_TIME_BEFORE_DISCHARGE1')}<br/>{t('ESTIMATED_TIME_BEFORE_DISCHARGE2')}
        <span style={{ color:'rgb(68,166,247)'}}> {formatDuration(Number(plannedLength))}</span>
    </Text>
  );
}

export { EstimatedDuration }; 