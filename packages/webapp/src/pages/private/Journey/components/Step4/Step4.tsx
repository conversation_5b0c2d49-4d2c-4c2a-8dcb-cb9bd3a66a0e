/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Accordion } from '@mantine/core';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Components
import { StepHeader } from '@/pages/private/Journey/components/StepHeader';
import { DischargeContent } from '@/pages/private/Journey/components/DischargeContent';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';

// Hooks
import { useView } from '@/pages/private/Journey/hooks/view.hook';

// Styles
import classes from './Step4.module.css';

// Stores
import useJourney from '@/pages/private/Journey/stores/journey';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type Step4Props = {
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
  viewedStepId: StepId;
};

function Step4(props: Step4Props) {
  const { setViewedStepId, viewedStepId } = props;
  // `loading` is always `false` when this component renders,
  // as it's only displayed when loading is complete (handled by a condition in the page component).
  // However, we explicitly check `isLoading === false` when rendering the UI
  // to safeguard against any potential future changes in the page component's logic.
  const { data, isLoading } = useView();
  const { defaultActiveStepId } = useJourney();
  const stepTitleName = "Décision d'orientation";
  // const stepTitleName = data.formattedResponse.steps[3].title;

  return (
    <Accordion.Item
      key={STATIC_STEPS[4].id}
      value={STATIC_STEPS[4].id}
      component="section"
      className={classes['step-4-root']}
    >
      <StepHeader
        setViewedStepId={setViewedStepId}
        viewedStepId={viewedStepId}
        stepId={STATIC_STEPS[4].id}
        stepTitle={
          !isLoading && data?.formattedResponse && defaultActiveStepId === '4'
            ? stepTitleName + "\u00A0\u00A0📌\u00A0\u00A0"
            :
            !isLoading && data?.formattedResponse
            ? stepTitleName
            : ''
        }
      />
      <Accordion.Panel
        classNames={{
          panel: classes['accordion-panel'],
          content: classes['accordion-content'],
        }}
      >
        {!isLoading && data?.formattedResponse.steps[4] ? (
          <DischargeContent data={data.formattedResponse.steps[4].discharge} />
        ) : (
          <></>
        )}
      </Accordion.Panel>
    </Accordion.Item>
  );
}

export { Step4 };
