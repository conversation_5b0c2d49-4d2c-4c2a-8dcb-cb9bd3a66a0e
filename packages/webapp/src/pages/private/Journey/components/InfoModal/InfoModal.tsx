/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ContextModalProps } from '@mantine/modals';
import { Box, Text, Title } from '@mantine/core';

// Components
import { CloseButton } from '@/components/CloseButton';

// Styles
import classes from './InfoModal.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type InfoModalProps = ContextModalProps<{ title: string; description: string }>;

function InfoModal(props: InfoModalProps) {
  const { id, context, innerProps } = props;

  return (
    <Box className={classes['info-modal-root']}>
      <Box className={classes['close-button-wrapper']}>
        <CloseButton
          variant="transparent"
          onClick={() => context.closeModal(id)}
        />
      </Box>

      <Box className={classes['info-modal-content']}>
        <Title className={classes.title} component="h1">
          {innerProps.title}
        </Title>
        <Text className={classes.description}>{innerProps.description}</Text>
      </Box>
    </Box>
  );
}

export { InfoModal };
