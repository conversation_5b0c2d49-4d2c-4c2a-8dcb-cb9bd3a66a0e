.info-modal-root {
  overflow: hidden;
  width: 100%;
  border-radius: var(--mantine-radius-lg);
  padding: 10px;
  background-color: var(--mantine-color-white);
}

.close-button-wrapper {
  width: 100%;
  padding-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.info-modal-content {
  width: 100%;
  padding: 0px 15px 15px;
}

.title {
  padding-bottom: 20px;
  color: var(--mantine-color-gray-0);
  font-weight: 500;
  font-size: var(--mantine-font-size-xl);
}

.description {
  color: var(--mantine-color-gray-3);
}
