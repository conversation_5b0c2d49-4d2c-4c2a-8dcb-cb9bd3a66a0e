/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Accordion } from '@mantine/core';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Components
import { StepHeader } from '@/pages/private/Journey/components/StepHeader';
import { IoaServiceRequestContent } from '@/pages/private/Journey/components/IoaServiceRequestContent';
import { EstimatedDuration } from '@/pages/private/Journey/components/EstimatedDuration';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';

// Hooks
import { useView } from '@/pages/private/Journey/hooks/view.hook';

// Stores
import useJourney from '@/pages/private/Journey/stores/journey';

// Styles
import classes from './Step2.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type Step2Props = {
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
  viewedStepId: StepId;
};

function Step2(props: Step2Props) {
  const { setViewedStepId, viewedStepId } = props;
  // `loading` is always `false` when this component renders,
  // as it's only displayed when loading is complete (handled by a condition in the page component).
  // However, we explicitly check `isLoading === false` when rendering the UI
  // to safeguard against any potential future changes in the page component's logic.
  const { data, isLoading } = useView();
  const { defaultActiveStepId } = useJourney();

  return (
    <Accordion.Item
      key={STATIC_STEPS[2].id}
      value={STATIC_STEPS[2].id}
      component="section"
      className={classes['step-2-root']}
    >
      <StepHeader
        setViewedStepId={setViewedStepId}
        viewedStepId={viewedStepId}
        stepId={STATIC_STEPS[2].id}
        stepTitle={
          !isLoading && data?.formattedResponse && defaultActiveStepId === '2'
            ? data.formattedResponse.steps[2].title + "\u00A0\u00A0📌\u00A0\u00A0"
            :
            !isLoading && data?.formattedResponse
            ? data.formattedResponse.steps[2].title
            : ''
        }
      />
      <Accordion.Panel
        classNames={{
          panel: classes['accordion-panel'],
          content: classes['accordion-content'],
        }}
      >
        {!isLoading && data?.formattedResponse.steps[2] ? (
          <>
            <IoaServiceRequestContent data={data.formattedResponse.steps[2]} />
            {data.formattedResponse.steps[2].plannedLength && (
              <EstimatedDuration plannedLength={data.formattedResponse.steps[2].plannedLength} />
            )}
          </>
        ) : (
          <></>
        )}
      </Accordion.Panel>
    </Accordion.Item>
  );
}

export { Step2 };
