.footer-root {
  width: 100%;
  padding-top: 14.5px;
  background: linear-gradient(
    var(--mantine-color-blue-1),
    var(--mantine-color-blue-6),
    var(--mantine-color-blue-0)
  );
}

.media-wrapper {
  height: 37px;
  width: 100%;
  padding: 0px 21px;
  display: flex;
  column-gap: 8px;
}

/* Special positioning for MonParcours logo when alone */
.media-wrapper-single {
  height: 37px;
  width: 100%;
  padding: 0px 21px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.image {
  height: 100%;
  width: auto;
}

.image2 {
  height: 100%;
  width: auto;
  margin-left: 4px;
  margin-right: 4px;
}

.links-wrapper {
  width: 100%;
  padding: 32px 21px 41px;
  display: flex;
  flex-direction: column;
}

.link-wrapper {
  display: flex;
  align-items: center;
  column-gap: 2px;
}

.link-title {
  color: var(--mantine-color-white);
  font-size: var(--mantine-font-size-sm);
  line-height: rem(25);
}

.link-title2 {
  color: var(--mantine-color-white);
  font-size: var(--mantine-font-size-sm);
  line-height: rem(25);
  margin-left: -14px;
}

.copyright-wrapper {
  width: 100%;
  padding: 8px 19px;
  background-color: var(--mantine-color-white);
  color: var(blue---mantine-color-blue-3);
  font-size: var(--mantine-font-size-sm);
  line-height: var(13);
}
