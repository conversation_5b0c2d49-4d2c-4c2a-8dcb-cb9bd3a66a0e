/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useTranslation } from 'react-i18next';
import { Box, Image, Title, useMantineTheme } from '@mantine/core';
import { Link } from '@tanstack/react-router';
import { IconChevronRight } from '@tabler/icons-react';
import { openContextModal } from '@mantine/modals';
import {
  // Drawer,
  // Flex,
  // Burger,
  // Text,
  // rem,
  // Divider,
  // Stack,
  Group,
} from '@mantine/core';

// Hooks
import { useOrganization } from '@/hooks/useOrganization';

// Styles
import classes from './Footer.module.css';
// import {
//   IconMoodCry as IconPain,
//   IconChevronDown,
//   IconX as IconDelete,
// } from '@tabler/icons-react';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function Footer() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { logoPath } = useOrganization();
  // const handleOpenModal = function() {
  //   console.log("Ouverture de la modale !");
  //   // ici tu déclenches ton setShowModal(true) ou équivalent
  //   StartAxeptio(1);
  // };

  return (
    <footer className={classes['footer-root']}>
      <Box
        className={
          logoPath ? classes['media-wrapper'] : classes['media-wrapper-single']
        }
      >
        {logoPath && <Image src={logoPath} className={classes['image']} />}
        <Image
          src="/images/logo_monparcours-blanc-2.png"
          className={classes['image2']}
        />
      </Box>

      <Box className={classes['links-wrapper']}>
        <Group
          onClick={() =>
            openContextModal({
              modal: 'solution-presentation',
              innerProps: {},
            })
          }
        >
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title className={classes['link-title2']}>
            {t('DETAILED_PRESENTATION_OF_THE_MPP_SOLUTION')}
          </Title>
        </Group>

        <Link className={classes['link-wrapper']} to="/presentation">
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title component="h1" className={classes['link-title']}>
            {t('EMERGENCY_DEPARTMENT_PHONE_NUMBER')}
          </Title>
        </Link>

        {/* <Link className={classes['link-wrapper']} to="/emergency-guidelines">
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title component="h1" className={classes['link-title']}>
            {t('EMERGENCY_DEPARTMENT_VISIT_GUIDELINES')}
          </Title>
        </Link> */}

        <Link className={classes['link-wrapper']} to="/legal-notices">
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title component="h1" className={classes['link-title']}>
            {t('LEGAL_NOTICES')}
          </Title>
        </Link>

        <Link className={classes['link-wrapper']} to="/privacy-policy">
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title component="h1" className={classes['link-title']}>
            {t('PRIVACY_POLICY')}
          </Title>
        </Link>

        <Link className={classes['link-wrapper']} to="/terms-and-conditions">
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title component="h1" className={classes['link-title']}>
            {t('GENERAL_TERMS_AND_CONDITIONS_OF_USE')}
          </Title>
        </Link>

        {/* <div
          onClick={handleOpenModal}
          className={classes['link-wrapper']}
          style={{ all: 'unset', cursor: 'pointer', display: 'flex', alignItems: 'center', gap: '0.5rem' }}
        >
          <IconChevronRight
            color={theme.colors.blue[0]}
            width={14}
            height={14}
          />
          <Title component="h1" className={classes['link-title']} ml={-6}>
            Conditions générales d'utilisation / CGU
          </Title>
        </div> */}
      </Box>

      <Box className={classes['copyright-wrapper']}>
        &copy; {t('COPYRIGHT_2024_CHU_DE_LIMOGES_FOCUS_SANTE')}
      </Box>
    </footer>
  );
}

export { Footer };
