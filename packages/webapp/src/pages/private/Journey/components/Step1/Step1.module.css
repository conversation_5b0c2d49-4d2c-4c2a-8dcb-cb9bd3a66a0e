.step-1-root {
  width: 100%;
  padding: 0px 0px 44px;
  background-color: var(--mantine-color-white);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.accordion-panel {
  width: 100%;
}

.accordion-content {
  width: 100%;
  padding: 0px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.step-description-wrapper {
  width: 100%;
  padding: 15px 20px 0px;
  display: flex;
  justify-content: center;
}

.step-description {
  color: var(--mantine-color-gray-0);
  font-weight: 700;
  font-size: rem(12);
  line-height: rem(15);
  text-align: center;
  max-width: rem(300);
}

.action-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.action-title-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  column-gap: 5px;
}

.action-title {
  color: var(--mantine-color-gray-4);
  font-size: var(--mantine-font-size-lg);
  line-height: rem(19);
  text-align: center;
  letter-spacing: -0.16px;
}

.action-description {
  color: var(--mantine-color-gray-3);
  font-size: rem(12);
  line-height: rem(15);
  text-align: center;
}

.action-button {
  padding: 0px 19px;
  color: var(--mantine-color-gray-3);
}

.action-button-clicked {
  border-color: var(--mantine-color-green-8);
  background-color: var(--mantine-color-green-8);
  color: var(--mantine-color-white);
}
