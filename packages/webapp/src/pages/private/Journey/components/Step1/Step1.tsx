/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Accordion, ActionIcon, Box, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { openContextModal } from '@mantine/modals';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Components
import { StepHeader } from '@/pages/private/Journey/components/StepHeader';
import { Icons } from '@/components/Icons';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';
import { Dispatch, SetStateAction } from 'react';
// Hooks
import { useView } from '@/pages/private/Journey/hooks/view.hook';

// Stores
import useJourney from '@/pages/private/Journey/stores/journey';

// Styles
import classes from './Step1.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type Step1Props = {
  setViewedStepId: Dispatch<SetStateAction<StepId | undefined>>;
  viewedStepId: StepId;
};

function Step1(props: Step1Props) {
  const { setViewedStepId, viewedStepId } = props;
  const { t } = useTranslation();
  const { defaultActiveStepId } = useJourney();
  const { data, isLoading } = useView();

  return (
    <Accordion.Item
      key={STATIC_STEPS[1].id}
      value={STATIC_STEPS[1].id}
      component="section"
      className={classes['step-1-root']}
    >
      <StepHeader
        setViewedStepId={setViewedStepId}
        stepId={STATIC_STEPS[1].id}
        viewedStepId={viewedStepId}
        stepTitle={
          !isLoading && data?.formattedResponse && defaultActiveStepId === '1'
            ? t('PREPARATION_OF_USEFUL_INFORMATION') +
              '\u00A0\u00A0📌\u00A0\u00A0'
            : !isLoading &&
                data?.formattedResponse &&
                defaultActiveStepId !== '1'
              ? t('PREPARATION_OF_USEFUL_INFORMATION')
              : ''
        }
      />

      <Accordion.Panel
        classNames={{
          panel: classes['accordion-panel'],
          content: classes['accordion-content'],
        }}
      >
        {/* Description */}
        <Box className={classes['step-description-wrapper']}>
          <Text
            className={classes['step-description']}
            style={{ lineHeight: '19px' }}
          >
            {defaultActiveStepId === '1'
              ? t('YOU_WILL_MEET_THE_ADMISSION_COORDINATOR_NURSE')
              : t('YOU_HAVE_MET_THE_ADMISSION_COORDINATOR_NURSE')}
          </Text>
          <ActionIcon
            variant="transparent"
            size="xs"
            ml="-3px"
            mt="20px"
            onClick={() =>
              openContextModal({
                modal: 'info',
                innerProps: {
                  title: t('THE_ROLE_OF_THE_RECEPTION_ORGANIZING_NURSE'),
                  description:
                    "L'Infirmière Organisatrice de l'Accueil (IOA) est responsable du tri des patients à leur arrivée aux urgences. Elle évalue rapidement leur état de santé pour prioriser la prise en charge en fonction de la gravité, oriente les patients vers les services adaptés, et coordonne avec l'équipe médicale pour optimiser le flux des urgences. L'IOA réévalue régulièrement les patients en salle d'attente pour ajuster les priorités en cas d'évolution de leur état.",
                },
              })
            }
          >
            <Icons.Info />
          </ActionIcon>
        </Box>
      </Accordion.Panel>
    </Accordion.Item>
  );
}

export { Step1 };
