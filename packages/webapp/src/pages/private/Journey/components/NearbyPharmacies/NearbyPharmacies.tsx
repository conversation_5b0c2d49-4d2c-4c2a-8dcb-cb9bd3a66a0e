/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useTranslation } from 'react-i18next';
import { Box, Text, Title } from '@mantine/core';
import { IconMapPin2 } from '@tabler/icons-react';
import { Link } from '@tanstack/react-router';

// Components
import { Button } from '@/components/Button';

// Styles
import classes from './NearbyPharmacies.module.css';

// Constants
import { externalLinks } from '@/constants/site.constants';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function NearbyPharmacies() {
  const { t } = useTranslation();

  return (
    <section className={classes['nearby-pharmacies-root']}>
      <Title className={classes['title']}>
        {t('LIST_OF_NEARBY_PHARMACIES')}
      </Title>
      <Box className={classes['description-wrapper']}>
        <IconMapPin2 />
        <Text className={classes['description']}>
          {t('PHARMACY_DIRECTORY')}
        </Text>
      </Box>
      <Link to={externalLinks.nearbyPharmacies as string} target="_blank">
        <Button variant="outline" className={classes['action-button']}>
          {t('ACCESS_THE_SERVICE')}
        </Button>
      </Link>
    </section>
  );
}

export { NearbyPharmacies };
