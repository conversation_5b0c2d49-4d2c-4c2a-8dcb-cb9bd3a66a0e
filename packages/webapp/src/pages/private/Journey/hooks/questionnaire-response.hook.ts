/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';

// Services
import { QuestionnaireResponseService } from '@/pages/private/Journey/services/questionnaire-response.service';

// Configs
import i18next from '@/configs/organization-i18n.config';

/* -------------------------------------------------------------------------- */
/*                                  Post Hook                                 */
/* -------------------------------------------------------------------------- */

const questionnaireResponseService = new QuestionnaireResponseService();

const usePostQuestionnaireResponse = () =>
  useMutation({
    mutationFn: questionnaireResponseService.post,
    onSuccess: (_response, args) => {
      notifications.show({
        message: `${i18next.t('YOU_HAVE_VALIDATED_A_PAIN_LEVEL_OF')} ${args.body.item[0].answer[0].value_integer}`,
      });
    },
  });

export { usePostQuestionnaireResponse };
