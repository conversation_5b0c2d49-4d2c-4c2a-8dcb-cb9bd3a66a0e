/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { queryOptions, useQuery } from '@tanstack/react-query';

// Services
import { ViewService } from '@/services/view.service';

// Types
import { DynamicSteps, StepId } from '@/pages/private/Journey/types/stepper';
import { PatientView } from '@/models/view';

// Store
import useJourney from '@/pages/private/Journey/stores/journey';

// Helpers
import {
  getDefaultActiveStepId,
  formatStep1Data,
  formatStep2Data,
  formatStep3Data,
  formatStep4Data,
} from '@/pages/private/Journey/helpers/stepper.helper';

/* -------------------------------------------------------------------------- */
/*                              Get View Hook                                 */
/* -------------------------------------------------------------------------- */

const viewService = new ViewService();

type UseViewResponse = {
  response: PatientView;
  formattedResponse: {
    defaultActiveStepId: StepId;
    steps: DynamicSteps;
  };
};

const viewQueryOptions = queryOptions({
  queryKey: ['view-query'],
  queryFn: async () => await viewService.get(),
  refetchInterval: 3000,
  refetchOnWindowFocus: true,
  select: (response): UseViewResponse => {
    // Calculate the default step based on the latest response
    const calculatedDefaultActiveStepId = getDefaultActiveStepId(response);
    const currentStoredDefaultStepId = useJourney.getState().defaultActiveStepId;

    // Only update the store if the calculated default step is different from the one stored
    if (calculatedDefaultActiveStepId !== currentStoredDefaultStepId) {
      useJourney.getState().setDefaultActiveStepId(calculatedDefaultActiveStepId);
    }

    // Format the data for each step
    const step1 = formatStep1Data(response);
    const step2 = formatStep2Data(response);
    const step3 = formatStep3Data(response);
    const step4 = formatStep4Data(response);

    return {
      response,
      formattedResponse: {
        defaultActiveStepId: calculatedDefaultActiveStepId,
        steps: {
          1: step1,
          2: step2,
          3: step3,
          4: step4,
        },
      },
    };
  },
});

const useView = () => useQuery(viewQueryOptions);

export { viewQueryOptions, useView, type UseViewResponse };
