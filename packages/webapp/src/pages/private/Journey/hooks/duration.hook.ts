/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { useCallback, useEffect, useState } from 'react';

// Helpers
import { Dayjs } from '@/helpers/dayjs.helper';

/* -------------------------------------------------------------------------- */
/*                                     Hook                                   */
/* -------------------------------------------------------------------------- */

const dayjs = new Dayjs();

const useDurationFromNow = (targetDate: Date | number | string) => {
  const [duration, setDuration] = useState<string>('');

  const calculateDurationFromNow = useCallback(
    () => dayjs.getDurationFromNow({ date: targetDate }),
    [targetDate]
  );

  useEffect(() => {
    const updateDuration = () => {
      setDuration(calculateDurationFromNow());
    };

    updateDuration();
    const interval = setInterval(updateDuration, 60000);
    return () => clearInterval(interval);
  }, [calculateDurationFromNow]);

  return duration;
};

export { useDurationFromNow };
