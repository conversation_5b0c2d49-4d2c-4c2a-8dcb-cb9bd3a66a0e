import { ServiceRequestStatus } from '@/models/service_request';
import { Questionnaire } from '@/models/questionnaire';
import { Discharge } from '@/models/encounter';

// Static types : Types representing step data that is managed and stored on the client side
type StepId = '1' | '2' | '3' | '4';

type StaticStep = {
  id: StepId;
};

type StaticStep1 = StaticStep & {
  title: string;
  painQuesionnaireId: string;
  painDefaultValue: number;
};

type StaticStep2 = StaticStep & {
  ioaActivityDefinitionIdFragment: string;
};

type StaticStep3 = StaticStep & {
  medicalCareActivityDefinitionIdFragment: string;
};

type StaticStep4 = StaticStep & {
  title: string;
};

type StaticSteps = {
  1: StaticStep1;
  2: StaticStep2;
  3: StaticStep3;
  4: StaticStep4;
};

// Dynamic types: Types representing step data fetched from the server
type DynamicStep1 = {
  painQuestionnaire?: Questionnaire;
  painDefaultValue?: number;
};

type IoaServiceRequest = {
  waitCount: number;
  plannedEndDate: number;
  status?: ServiceRequestStatus;
};

type DynamicStep2 = {
  title: string;
  description: string;
  ioaServiceRequest?: IoaServiceRequest;
  affectedHealthcareService?: string;
  plannedLength?: number;
};

type MedicalCareServiceRequest = {
  waitCount: number;
  plannedEndDate: number;
  status?: ServiceRequestStatus;
};

type OtherServicesRequests = {
  id: string;
  waitCount: number;
  plannedEndDate: number;
  status?: ServiceRequestStatus;
  activityDefinition: {
    id: string;
    name: string;
    description: string;
  };
};

type DynamicStep3 = {
  title: string;
  medicalCareServiceRequest?: MedicalCareServiceRequest;
  otherServicesRequests?: OtherServicesRequests[];
  plannedLength?: number;
};

type DynamicStep4 = {
  discharge?: Discharge | null;
};

type DynamicSteps = {
  1: DynamicStep1;
  2: DynamicStep2;
  3: DynamicStep3;
  4: DynamicStep4;
};

export type {
  StepId,
  StaticStep,
  StaticStep1,
  StaticStep2,
  StaticStep3,
  StaticStep4,
  StaticSteps,
  DynamicStep1,
  DynamicStep2,
  DynamicStep3,
  DynamicStep4,
  DynamicSteps,
  IoaServiceRequest,
  MedicalCareServiceRequest,
  OtherServicesRequests,
};
