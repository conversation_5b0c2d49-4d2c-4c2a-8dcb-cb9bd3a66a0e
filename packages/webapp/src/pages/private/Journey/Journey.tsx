/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box } from '@mantine/core';
import { getRouteApi } from '@tanstack/react-router';
import { useState, useEffect } from 'react';

// Components
import { Header } from '@/components/Header';
import { StepperProgress } from '@/pages/private/Journey/components/StepperProgress';
import { Steps } from '@/pages/private/Journey/components/Steps';
import { NearbyPharmacies } from '@/pages/private/Journey/components/NearbyPharmacies';
import { Footer } from '@/pages/private/Journey/components/Footer';

// Hooks
import { useView } from '@/pages/private/Journey/hooks/view.hook';
import { StepId } from '@/pages/private/Journey/types/stepper';

// Styles
import classes from './Journey.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

const route = getRouteApi('/_private/journey/');

function Journey() {
  const { shareAccessData } = route.useLoaderData();
  const { data } = useView();
  const [viewedStepId, setViewedStepId] = useState<StepId | undefined>();

  const calculatedDefaultActiveStepId = data?.formattedResponse?.defaultActiveStepId;

  useEffect(() => {
    if (calculatedDefaultActiveStepId) {
      setViewedStepId(calculatedDefaultActiveStepId);
    }
  }, [calculatedDefaultActiveStepId]);

  if (!viewedStepId) {
    return null;
  }

  return (
    <main className={classes['journey-page-root']}>
      <Header hasShareButton shareAccessData={shareAccessData} />
      <StepperProgress
        viewedStepId={viewedStepId}
        setViewedStepId={setViewedStepId}
      />
      <Box className={classes['up-content-wrapper']}>
        <Steps
          viewedStepId={viewedStepId}
          setViewedStepId={setViewedStepId}
        />
      </Box>
      <Box className={classes['down-content-wrapper']}>
        <NearbyPharmacies />
        <Footer />
      </Box>
    </main>
  );
}

export { Journey };
