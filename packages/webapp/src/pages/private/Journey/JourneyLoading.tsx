/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box } from '@mantine/core';

// Components
import { Header } from '@/components/Header';
import { StepperProgressLoading } from '@/pages/private/Journey/components/StepperProgress';
import { StepsLoading } from '@/pages/private/Journey/components/Steps';
import { NearbyPharmacies } from '@/pages/private/Journey/components/NearbyPharmacies';
import { Footer } from '@/pages/private/Journey/components/Footer';

// Styles
import classes from './Journey.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function JourneyLoading() {
  return (
    <main className={classes['journey-page-root']}>
      <Header hasShareButton />
      <StepperProgressLoading />
      <Box className={classes['up-content-wrapper']}>
        <StepsLoading />
      </Box>
      <Box className={classes['down-content-wrapper']}>
        <NearbyPharmacies />
        <Footer />
      </Box>
    </main>
  );
}

export { JourneyLoading };
