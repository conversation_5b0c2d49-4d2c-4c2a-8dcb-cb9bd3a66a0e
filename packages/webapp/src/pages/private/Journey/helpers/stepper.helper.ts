/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Types
import { PatientView } from '@/models/view';
import {
  DynamicStep1,
  DynamicStep2,
  DynamicStep3,
  DynamicStep4,
  StepId,
} from '@/pages/private/Journey/types/stepper';
import { ServiceRequest } from '@/models/service_request';
import { HealthcareService } from '@/models/organization';

// Constants
import { STATIC_STEPS } from '@/pages/private/Journey/constants/stepper';

// Configs
import i18next from '@/configs/organization-i18n.config';

/* -------------------------------------------------------------------------- */
/*                               Helper functions                             */
/* -------------------------------------------------------------------------- */

export function getLatestServiceRequest(
  servicesRequests: ServiceRequest[]
): ServiceRequest {
  return servicesRequests.reduce((latest, current) =>
    current.authored_on > latest.authored_on ? current : latest
  );
}

export function getDefaultActiveStepId(viewApiResponse: PatientView): StepId {
  try {
    // Case: the discharge object is present => set the default active step to step 4.
    if (viewApiResponse.encounter.discharge) return '4';

    const { service_request } = viewApiResponse;

    if (service_request && service_request.length) {
      // Case: the medical-care service request is found => set the default active step to step 3.
      const medicalCareServiceRequests = service_request.filter(
        (serviceRequest) =>
          serviceRequest.activity_definition_id.includes(
            STATIC_STEPS[3].medicalCareActivityDefinitionIdFragment
          )
      );
      if (medicalCareServiceRequests.length) {
        const latestMedicalServiceRequest = getLatestServiceRequest(
          medicalCareServiceRequests
        );

        if (
          ['in-progress', 'waiting-for-results', 'completed'].includes(
            latestMedicalServiceRequest?.status ?? ''
          )
        ) {
          return '3';
        }
      }

      const ioaServicesRequests = service_request.filter((serviceRequest) =>
        serviceRequest.activity_definition_id.includes(
          STATIC_STEPS[2].ioaActivityDefinitionIdFragment
        )
      );

      // Case: no ioa service requests are found => set the default active step to step 1
      if (!ioaServicesRequests.length) return '1';

      const latestIoaServiceRequest =
        getLatestServiceRequest(ioaServicesRequests);

      // Case: ongoing ioa service request is found => set the default active step to step 2.
      if (
        latestIoaServiceRequest.status &&
        (latestIoaServiceRequest.status == 'in-progress' ||
          latestIoaServiceRequest.status == 'waiting-for-results' ||
          latestIoaServiceRequest.status == 'completed')
      ) {
        return '2';
      }
    }
  } catch (error) {
    // Case: unhandled TypeScript error
    console.log('Error while selecting default active step');
    console.log(error);
  }

  return '1';
}

export function formatStep1Data(viewApiResponse: PatientView): DynamicStep1 {
  const step1: DynamicStep1 = {};

  try {
    const { questionnaire } = viewApiResponse;

    if (questionnaire && questionnaire.length) {
      step1.painQuestionnaire = questionnaire.find(
        (questionnaire) =>
          questionnaire.questionnaire_id === STATIC_STEPS[1].painQuesionnaireId
      );

      if (
        step1.painQuestionnaire &&
        step1.painQuestionnaire.item?.length &&
        step1.painQuestionnaire.item[0].initial?.length
      ) {
        step1.painDefaultValue =
          step1.painQuestionnaire.item[0].initial[0].value_integer;
      }
    }
  } catch (error) {
    // Case: unhandled TypeScript error
    console.log('Error while formatting step 1 data');
    console.log(error);
  }
  return step1;
}

export function formatStep2Data(viewApiResponse: PatientView): DynamicStep2 {
  let step2: DynamicStep2 = {
    title: i18next.t('ORIENTATION_BY_THE_IOA_NURSE'),
    description: i18next.t('AFTER_REVIEWING_YOUR_FILE_THE_NURSE_WILL'),
  };

  try {
    const {
      healthcare_service = [],
      service_request,
      encounter,
    } = viewApiResponse;

    if (
      healthcare_service &&
      healthcare_service.length &&
      healthcare_service[0].activity_definition &&
      healthcare_service[0].activity_definition.length
    ) {
      const { activity_definition } = healthcare_service[0];
      const ioaActivityDefinition = activity_definition.find(
        (activityDefinition) =>
          activityDefinition.activity_definition_id.includes(
            STATIC_STEPS[2].ioaActivityDefinitionIdFragment
          )
      );
      if (ioaActivityDefinition) {
        step2 = {
          title: ioaActivityDefinition.name,
          description: ioaActivityDefinition.description,
        };
      }
    }

    if (service_request?.length) {
      const ioaServiceRequests = service_request.filter((serviceRequest) =>
        serviceRequest.activity_definition_id.includes(
          STATIC_STEPS[2].ioaActivityDefinitionIdFragment
        )
      );

      if (ioaServiceRequests.length) {
        const latestIoaServiceRequest =
          getLatestServiceRequest(ioaServiceRequests);

        if (latestIoaServiceRequest) {
          step2.ioaServiceRequest = {
            waitCount: latestIoaServiceRequest.encounter_timing.wait_count,
            plannedEndDate:
              latestIoaServiceRequest.encounter_timing.planned_end_date,
            status: latestIoaServiceRequest.status,
          };
        }
      }
    }

    if (encounter.service_type) {
      const healthcareService = healthcare_service?.find(
        (service) =>
          (service as HealthcareService).healthcare_service_id ===
          encounter.service_type
      );
      if (healthcareService) {
        step2.affectedHealthcareService = (
          healthcareService as HealthcareService
        ).name;
      }
    }

    if (encounter.planned_length) {
      step2.plannedLength = encounter.planned_length;
    }
  } catch (error) {
    // Case: unhandled TypeScript error
    console.log('Error while formatting step 2 data');
    console.log(error);
  }

  return step2;
}

export function formatStep3Data(viewApiResponse: PatientView): DynamicStep3 {
  const step3: DynamicStep3 = {
    title: i18next.t('MEDICAL_CARE'),
  };

  try {
    const { healthcare_service, service_request, encounter } = viewApiResponse;

    if (
      healthcare_service &&
      healthcare_service.length &&
      healthcare_service[0].activity_definition &&
      healthcare_service[0].activity_definition.length
    ) {
      const { activity_definition } = healthcare_service[0];
      const medicalCareActivityDefinition = activity_definition.find(
        (activityDefinition) =>
          activityDefinition.activity_definition_id.includes(
            STATIC_STEPS[3].medicalCareActivityDefinitionIdFragment
          )
      );
      if (medicalCareActivityDefinition) {
        step3.title = medicalCareActivityDefinition.name;
      }
    }

    if (encounter.planned_length) {
      step3.plannedLength = encounter.planned_length;
    }

    if (service_request && service_request.length) {
      const medicalCareServiceRequests = service_request.filter(
        (serviceRequest) =>
          serviceRequest.activity_definition_id.includes(
            STATIC_STEPS[3].medicalCareActivityDefinitionIdFragment
          )
      );

      if (medicalCareServiceRequests.length) {
        const latestMedicalServiceRequest = getLatestServiceRequest(
          medicalCareServiceRequests
        );

        if (latestMedicalServiceRequest) {
          step3.medicalCareServiceRequest = {
            waitCount: latestMedicalServiceRequest.encounter_timing.wait_count,
            plannedEndDate:
              latestMedicalServiceRequest.encounter_timing.planned_end_date,
            status: latestMedicalServiceRequest.status,
          };
        }
      }

      if (
        healthcare_service &&
        healthcare_service.length &&
        healthcare_service[0].activity_definition &&
        healthcare_service[0].activity_definition.length
      ) {
        const otherServicesRequests = service_request.filter(
          (serviceRequest) =>
            !serviceRequest.activity_definition_id.includes(
              STATIC_STEPS[2].ioaActivityDefinitionIdFragment
            ) &&
            !serviceRequest.activity_definition_id.includes(
              STATIC_STEPS[3].medicalCareActivityDefinitionIdFragment
            )
        );

        if (otherServicesRequests.length) {
          const otherServicesRequestsMap = new Map<string, ServiceRequest>();
          otherServicesRequests.forEach((serviceRequest) => {
            const existingServiceRequest = otherServicesRequestsMap.get(
              serviceRequest.activity_definition_id
            );
            if (!existingServiceRequest) {
              otherServicesRequestsMap.set(
                serviceRequest.activity_definition_id,
                serviceRequest
              );
            }

            if (
              existingServiceRequest &&
              serviceRequest.authored_on > existingServiceRequest.authored_on
            ) {
              otherServicesRequestsMap.set(
                serviceRequest.activity_definition_id,
                serviceRequest
              );
            }
          });

          let uniqueOtherServiceRequests = Array.from(
            otherServicesRequestsMap.values()
          );

          if (uniqueOtherServiceRequests.length) {
            const activityDefinitions =
              healthcare_service[0].activity_definition;
            uniqueOtherServiceRequests = uniqueOtherServiceRequests.filter(
              (serviceRequest) =>
                activityDefinitions.find(
                  (activityDefinition) =>
                    activityDefinition.activity_definition_id ===
                    serviceRequest.activity_definition_id
                )
            );

            step3.otherServicesRequests = uniqueOtherServiceRequests.map(
              (serviceRequest) => {
                const activityDefinition = activityDefinitions.find(
                  (activityDefinition) =>
                    activityDefinition.activity_definition_id ===
                    serviceRequest.activity_definition_id
                )!;

                return {
                  id: serviceRequest.service_request_id,
                  waitCount: serviceRequest.encounter_timing.wait_count,
                  plannedEndDate:
                    serviceRequest.encounter_timing.planned_end_date,
                  status: serviceRequest.status,
                  activityDefinition: {
                    id: activityDefinition.activity_definition_id,
                    name: activityDefinition.name,
                    description: activityDefinition.description,
                  },
                };
              }
            );
          }
        }
      }
    }
  } catch (error) {
    // Case: unhandled TypeScript error
    console.log('Error while formatting step 3 data');
    console.log(error);
  }
  return step3;
}

export function formatStep4Data(viewApiResponse: PatientView): DynamicStep4 {
  const step4: DynamicStep4 = { discharge: null };

  try {
    const { encounter } = viewApiResponse;
    if (encounter.discharge) step4.discharge = encounter.discharge;
  } catch (error) {
    // Case: unhandled TypeScript error
    console.log('Error while formatting step 4 data');
    console.log(error);
  }
  return step4;
}
