/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Slices
import { StepperSlice, createStepperSlice } from './slices/stepper.slice';

/* -------------------------------------------------------------------------- */
/*                                   Store                                    */
/* -------------------------------------------------------------------------- */

const useJourney = create<StepperSlice>()(
  devtools((...a) => ({
    ...createStepperSlice(...a),
  }))
);

export default useJourney;
