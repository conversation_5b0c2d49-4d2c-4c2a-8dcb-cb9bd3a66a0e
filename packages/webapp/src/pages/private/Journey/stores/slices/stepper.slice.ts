/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { type StateCreator } from 'zustand';

// Types
import { StepId } from '@/pages/private/Journey/types/stepper';

/* -------------------------------------------------------------------------- */
/*                                   Slice                                    */
/* -------------------------------------------------------------------------- */

export type StepperSlice = {
  defaultActiveStepId?: StepId;
  setDefaultActiveStepId: (id: StepId) => void;
};

export const createStepperSlice: StateCreator<StepperSlice> = (
  set,
  _get
): StepperSlice => ({
  setDefaultActiveStepId: (id) => set({ defaultActiveStepId: id }),
});
