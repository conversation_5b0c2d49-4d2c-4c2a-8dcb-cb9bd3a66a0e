/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Types
import { StaticSteps } from '@/pages/private/Journey/types/stepper';
import { ServiceRequestStatus } from '@/models/service_request';

// Configs
import i18next from '@/configs/organization-i18n.config';

/* -------------------------------------------------------------------------- */
/*                                 Constants                                  */
/* -------------------------------------------------------------------------- */

const STATIC_STEPS: StaticSteps = {
  '1': {
    id: '1',
    title: i18next.t('PREPARATION_OF_USEFUL_INFORMATION'),
    painQuesionnaireId: 'douleur',
    painDefaultValue: 3,
  },
  '2': {
    id: '2',
    ioaActivityDefinitionIdFragment: 'ioa',
  },
  '3': {
    id: '3',
    medicalCareActivityDefinitionIdFragment: 'medical-care',
  },
  '4': {
    id: '4',
    title: i18next.t('ORIENTATION_DECISION'),
  },
} as const;

const SERVICE_REQUEST_STATUS_ALLOWED_VALUES: ServiceRequestStatus[] = [
  'cancelled',
  'not-yet-started',
  'in-progress',
  'waiting-for-results',
  'completed',
] as const;

const SERVICE_REQUEST_STATUS_LABELS: {
  [Key in ServiceRequestStatus]: string;
} = {
  cancelled: i18next.t('TO_BE_DONE'),
  'not-yet-started': i18next.t('TO_BE_DONE'),
  'in-progress': i18next.t('AWAITING_DECISION'),
  'waiting-for-results': i18next.t('AWAITING_RESULT'),
  completed: i18next.t('RESULTS_AVAILABLE'),
} as const;

export {
  STATIC_STEPS,
  SERVICE_REQUEST_STATUS_ALLOWED_VALUES,
  SERVICE_REQUEST_STATUS_LABELS,
};
