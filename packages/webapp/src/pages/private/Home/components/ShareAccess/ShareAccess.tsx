/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box, Container, Title } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { openContextModal } from '@mantine/modals';
import { IconShare2 } from '@tabler/icons-react';
import { getRouteApi } from '@tanstack/react-router';

// Components
import { Button } from '@/components/Button';

// Helpers
import { shareAccess } from '@/helpers/access.helper';

// Styles
import classes from './ShareAccess.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

const route = getRouteApi('/_private/home/');

function ShareAccess() {
  const { session } = route.useRouteContext();
  const { t } = useTranslation();

  return (
    <Container
      component="section"
      size="xs"
      className={classes['share-access-root']}
    >
      <Title className={classes.title} component="h1">
        {t('I_SHARE_ACCESS_TO_MY_JOURNEY_WITH')}
      </Title>

      <Box className={classes['action-wrapper']}>
        <Button
          variant="outline"
          onClick={() =>
            openContextModal({
              modal: 'share-access',
              innerProps: {
                onConfirm: () => shareAccess({ session }),
              },
            })
          }
        >
          {t('SHARE')}
          <IconShare2 width={14} height={14} />
        </Button>
      </Box>
    </Container>
  );
}

export { ShareAccess };
