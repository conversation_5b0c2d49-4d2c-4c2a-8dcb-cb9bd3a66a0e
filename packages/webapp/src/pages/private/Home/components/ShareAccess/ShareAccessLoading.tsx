/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box, Container, Title, Skeleton } from '@mantine/core';
import { useTranslation } from 'react-i18next';

// Styles
import classes from './ShareAccess.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function ShareAccessLoading() {
  const { t } = useTranslation();

  return (
    <Container
      component="section"
      size="xs"
      className={classes['share-access-root']}
    >
      <Title className={classes.title} component="h1">
        {t('I_SHARE_ACCESS_TO_MY_JOURNEY_WITH')}
      </Title>

      <Box className={classes['action-wrapper']}>
        <Title className={classes['action-title']} component="h4">
          {t('I_SHARE_ACCESS_TO_MY_JOURNEY_WITH_MY_CLOSE_ONES')}
          <Skeleton w={18} h={18} radius="lg" />
        </Title>
        <Skeleton w={156} h={32} radius={15} />
      </Box>
    </Container>
  );
}

export { ShareAccessLoading };
