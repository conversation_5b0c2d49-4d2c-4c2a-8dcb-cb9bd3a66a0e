/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ContextModalProps } from '@mantine/modals';
import {
  Box,
  Flex,
  Text,
  Title,
  useMantineTheme,
  Anchor,
  List,
} from '@mantine/core';
import { IconNurse } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';

// Components
import { CloseButton } from '@/components/CloseButton';

// Styles
import classes from './SolutionPresentationModal.module.css';

// Constants
import { externalLinks } from '@/constants/site.constants';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type SolutionPresentationModalProps = ContextModalProps;

function SolutionPresentationModal(props: SolutionPresentationModalProps) {
  const { id, context } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();

  return (
    <Box className={classes['solution-presentation-modal-root']}>
      <Box className={classes['close-button-wrapper']}>
        <CloseButton
          variant="transparent"
          onClick={() => context.closeModal(id)}
        />
      </Box>

      <Box className={classes['solution-presentation-modal-content']}>
        <Title className={classes['title']}>
          Présentation detaillée de la solution
        </Title>
        <Text className={classes['text']} pb={0} w={'auto'}>
          Mon Parcours aux Urgences vous permet de suivre en temps réel
          l'avancement de votre prise en charge.
        </Text>
        <Flex
          columnGap={20}
          pb={25}
          justify={'center'}
          align={'center'}
          wrap="wrap"
        >
          <IconNurse size={100} color={theme.colors.dark[3]} />
          {/* <Box w={100} h={100} bg={theme.colors.gray[2]} /> */}
          <Text className={classes['text']}>
            L'Infirmière Organisatrice de l'Accueil (IOA) est le premier
            professionnel de santé que vous rencontrez aux urgences. Elle évalue
            votre état de santé et détermine la priorité de votre prise en
            charge.
          </Text>
        </Flex>
        <Box className={classes['sections-wrapper']}>
          <section>
            <Title className={classes['section-title']}>Parcours type</Title>

            <List className={classes['text']}>
              <List.Item>Accueil et enregistrement administratif</List.Item>
              <List.Item>
                Évaluation initiale par l'Infirmière Organisatrice de l'Accueil
                (IOA)
              </List.Item>
              <List.Item>
                Attente en salle d'attente selon la priorité établie
              </List.Item>
              <List.Item>Consultation médicale</List.Item>
              <List.Item>
                Examens complémentaires si nécessaire (radiologie, analyses de
                sang, etc.)
              </List.Item>
              <List.Item>Traitement ou soins</List.Item>
              <List.Item>
                Décision médicale (retour à domicile, hospitalisation,
                transfert)
              </List.Item>
              <List.Item>Sortie et remise des documents nécessaires</List.Item>
            </List>
            <Text mt={10} className={classes['text']}>
              La durée et l'ordre de ces étapes peuvent varier en fonction de
              votre état de santé et de l'affluence aux urgences.
            </Text>
          </section>
          <section>
            <Title className={classes['section-title']}>
              Annuaire des Pharmacies
            </Title>
            <Text className={classes['text']}>
              <Anchor href={externalLinks.nearbyPharmacies} target="_blank">
                Pharmacies autour de moi
              </Anchor>
            </Text>
          </section>
          <section>
            <Title className={classes['section-title']}>
              {t('EMERGENCY_DEPARTMENT_VISIT_GUIDELINES_FOR')}{' '}
              {t('MY_PERSONALIZED_JOURNEY_AT_THE_EMERGENCY_2')}
            </Title>
            <Text className={classes['text']}>
              Visite des familles autorisées de manière systématique :
            </Text>
            <List className={classes['text']}>
              <List.Item>Patients mineurs</List.Item>
              <List.Item>Patients polyhandicapés</List.Item>
              <List.Item>Patients en soins palliatifs</List.Item>
              <List.Item>Patients non francophones</List.Item>
              <List.Item>
                Patients hospitalisés aux urgences depuis + de 24h : visites de
                14h à 17h
              </List.Item>
            </List>
            <Text className={classes['text']} pt={10}>
              <b>Attention :</b> Selon l’affluence dans les urgences, toutes les
              visites peuvent être temporairement suspendues.
            </Text>
          </section>
        </Box>
      </Box>
    </Box>
  );
}

export { SolutionPresentationModal };
