.solution-presentation-modal-root {
  overflow: hidden;
  width: 100%;
  height: 80vh;
  max-height: 647px;
  border-radius: var(--mantine-radius-lg);
  padding: 10px 10px 51px;
  background-color: var(--mantine-color-white);
  display: flex;
  flex-direction: column;
}

.close-button-wrapper {
  width: 100%;
  padding-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.solution-presentation-modal-content {
  width: 100%;
  flex: 1;
  padding: 0px 25px 0px 15px;
  overflow-y: scroll;
}

.title {
  padding-bottom: 10px;
  color: var(--mantine-color-gray-0);
  font-weight: 500;
  font-size: var(--mantine-font-size-xl);
}

.sections-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 25px;
}

.section-title {
  padding-bottom: 2px;
  color: var(--mantine-color-gray-0);
  font-weight: 500;
  font-size: var(--mantine-font-size-lg);
}

.text {
  color: var(--mantine-color-gray-3);
}
