/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ContextModalProps } from '@mantine/modals';
import { useTranslation } from 'react-i18next';
import { Box, Title } from '@mantine/core';
import { IconShare2 } from '@tabler/icons-react';

// Components
import { Button } from '@/components/Button';
import { CloseButton } from '@/components/CloseButton';

// Styles
import classes from './ShareAccessModal.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

type ShareAccessModalProps = ContextModalProps<{ onConfirm: () => void }>;

function ShareAccessModal(props: ShareAccessModalProps) {
  const { id, context, innerProps } = props;
  const { t } = useTranslation();

  const handleSubmit = () => {
    context.closeModal(id);
    innerProps.onConfirm();
  };

  return (
    <Box className={classes['share-access-modal-root']}>
      <Box className={classes['close-button-wrapper']}>
        <CloseButton
          variant="transparent"
          onClick={() => context.closeModal(id)}
        />
      </Box>

      <Box className={classes['share-access-modal-content']}>
        <Box className={classes['titles-wrapper']}>
          <Title className={classes.title} component="h1">
            {t('I_SHARE_ACCESS_TO_MY_JOURNEY_WITH_MY_CLOSE_ONES')}
          </Title>

          <Title className={classes.subtitle} component="h3">
            {t('I_SHARE_THE_ACCESS_LINK_WITH_MY_LOVED_ONES_VIA_EMAIL_OR_SMS')}
          </Title>
        </Box>

        <Box className={classes['submit-button-wrapper']}>
          <Button variant="outline" onClick={handleSubmit}>
            {t('I_SHARE_ACCESS')}
            <IconShare2 width={14} height={14} />
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export { ShareAccessModal };
