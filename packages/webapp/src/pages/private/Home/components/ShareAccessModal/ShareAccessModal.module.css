.share-access-modal-root {
  overflow: hidden;
  height: 317px;
  width: 100%;
  border-radius: var(--mantine-radius-lg);
  background-color: var(--mantine-color-white);
  display: flex;
  flex-direction: column;
}

.close-button-wrapper {
  width: 100%;
  padding: 10px;
  display: flex;
  justify-content: flex-end;
}

.share-access-modal-content {
  width: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.titles-wrapper {
  width: 100%;
  padding: 0px 30px;
}

.title {
  padding-bottom: 25px;
  color: var(--mantine-color-gray-0);
  text-align: center;
  font-size: rem(22);
}

.subtitle {
  color: var(--mantine-color-gray-1);
  text-align: center;
  font-size: rem(15);
  line-height: rem(23);
}

.submit-button-wrapper {
  width: 100%;
  padding: 24px 0px;
  background-color: var(--mantine-color-blue-2);
  display: flex;
  justify-content: center;
}
