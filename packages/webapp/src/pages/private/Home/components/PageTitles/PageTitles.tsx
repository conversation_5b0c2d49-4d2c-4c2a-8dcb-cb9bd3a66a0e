/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Container, Title } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { openContextModal } from '@mantine/modals';

// Components
import { Button } from '@/components/Button';

// Styles
import classes from './PageTitles.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function PageTitles() {
  const { t } = useTranslation();

  return (
    <Container className={classes['page-titles-root']} size="sm">
      <Title className={classes.title} component="h1">
        {t('MY_PERSONALIZED_JOURNEY_AT_THE_EMERGENCY_1')} <br />
        {t('MY_PERSONALIZED_JOURNEY_AT_THE_EMERGENCY_2')} 
      </Title>

      <Title className={classes['welcome-message']} component="h2">
        {t('WELCOME')}
      </Title>

      <Title className={classes.subtitle} component="h3">
        Vous êtes bien connecté(e) à l’interface qui va vous permettre de suivre
        votre parcours aux urgences. Pour en savoir plus cliquez sur le bouton
        ci-dessous
      </Title>

      <Button
        className={classes.button}
        variant="outline"
        onClick={() =>
          openContextModal({
            modal: 'solution-presentation',
            innerProps: {},
          })
        }
      >
        {t('FIND_OUT_MORE')}
      </Button>
    </Container>
  );
}

export { PageTitles };
