/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { useNavigate } from '@tanstack/react-router';

// Components
import { PageTitles } from '@/pages/private/Home/components/PageTitles';
import { ShareAccessLoading } from '@/pages/private/Home/components/ShareAccess';
import { Button } from '@/components/Button';
import { Header } from '@/components/Header';

// Styles
import classes from './Home.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function HomeLoading() {
  const { t } = useTranslation();
  const navigate = useNavigate({ from: '/home' });

  return (
    <main className={classes['home-page-root']}>
      <Header />
      <PageTitles />
      <ShareAccessLoading />
      <Box className={classes['submit-button-wrapper']}>
        <Button onClick={() => navigate({ to: '/journey' })}>
          {t('MY_PERSONALIZED_JOURNEY')}
        </Button>
      </Box>
    </main>
  );
}

export { HomeLoading };
