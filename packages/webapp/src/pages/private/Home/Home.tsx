/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Box } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { useNavigate } from '@tanstack/react-router';

// Components
import { Header } from '@/components/Header';
import { PageTitles } from '@/pages/private/Home/components/PageTitles';
import { ShareAccess } from '@/pages/private/Home/components/ShareAccess';
import { Button } from '@/components/Button';

// Styles
import classes from './Home.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Component                                  */
/* -------------------------------------------------------------------------- */

function Home() {
  const { t } = useTranslation();
  const navigate = useNavigate({ from: '/home' });

  return (
    <main className={classes['home-page-root']}>
      <Header />
      <PageTitles />
      <ShareAccess />
      <Box className={classes['submit-button-wrapper']}>
        <Button onClick={() => navigate({ to: '/journey' })}>
          {t('MY_PERSONALIZED_JOURNEY')}
        </Button>
      </Box>
    </main>
  );
}

export { Home };
