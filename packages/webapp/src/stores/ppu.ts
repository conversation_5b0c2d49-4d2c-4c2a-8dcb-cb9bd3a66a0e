/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { create } from 'zustand';
import { devtools, persist, createJSONStorage } from 'zustand/middleware';

// Configs
import { env } from '@/configs/env.config';

// Slices
import { AuthSlice, createAuthSlice } from '@/stores/slices/auth.slice';

/* -------------------------------------------------------------------------- */
/*                                   Store                                    */
/* -------------------------------------------------------------------------- */

const usePpuStore = create<AuthSlice>()(
  devtools(
    persist(
      (...a) => ({
        ...createAuthSlice(...a),
      }),
      {
        name: '_ppu',
        storage: createJSONStorage(() => localStorage),
      }
    ),
    { enabled: env.nodeEnv === 'development' }
  )
);

export default usePpuStore;
