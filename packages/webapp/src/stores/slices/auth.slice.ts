/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { type StateCreator } from 'zustand';

// Types
import type { User } from '@/types/user';

/* -------------------------------------------------------------------------- */
/*                                   Slice                                    */
/* -------------------------------------------------------------------------- */

type AuthenticatedUser = {
  accessToken: string;
  authId: string;
  data: User;
};

type SignInArgs = {
  accessToken: string;
  authId: string;
  data: User;
};

export type AuthSlice = {
  authenticatedUser: AuthenticatedUser | null;
  signIn: (args: SignInArgs) => Promise<void>;
  getAuthAccessToken: () => Promise<string | null>;
  getAuthId: () => Promise<string | null>;
  getSession: () => Promise<User | null>;
  updateSession: (data: User) => Promise<void>;
  signOut: () => Promise<void>;
};

export const createAuthSlice: StateCreator<AuthSlice> = (
  set,
  get
): AuthSlice => ({
  authenticatedUser: null,

  signIn: async ({ accessToken, authId, data }) => {
    set({ authenticatedUser: { accessToken, authId, data } });
  },

  getAuthAccessToken: async () => {
    const { authenticatedUser } = get();
    if (!authenticatedUser) return null;
    return authenticatedUser.accessToken;
  },

  getAuthId: async () => {
    const { authenticatedUser } = get();
    if (!authenticatedUser) return null;
    return authenticatedUser.authId;
  },

  getSession: async () => {
    const { authenticatedUser } = get();
    if (!authenticatedUser) return null;
    return authenticatedUser.data;
  },

  updateSession: async (data) => {
    const { authenticatedUser } = get();
    if (!authenticatedUser) return;
    set({ 
      authenticatedUser: {
        ...authenticatedUser,
        data
      }
    });
  },

  signOut: () =>
    new Promise((resolve) => {
      set({ authenticatedUser: null });
      resolve();
    }),
});
