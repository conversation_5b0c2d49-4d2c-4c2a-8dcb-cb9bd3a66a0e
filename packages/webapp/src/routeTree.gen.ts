/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

import { createFileRoute } from '@tanstack/react-router'

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as PrivateImport } from './routes/_private'
import { Route as PublicAuthImport } from './routes/_public/_auth'
import { Route as PublicSessionExpiredIndexImport } from './routes/_public/session-expired/index'
import { Route as PrivateJourneyIndexImport } from './routes/_private/journey/index'
import { Route as PrivateHomeIndexImport } from './routes/_private/home/<USER>'
import { Route as PublicAuthSignInAuthIdIndexImport } from './routes/_public/_auth/sign-in/$auth-id/index'

// Create Virtual Routes

const PublicLazyImport = createFileRoute('/_public')()
const PublicTermsAndConditionsIndexLazyImport = createFileRoute(
  '/_public/terms-and-conditions/',
)()
const PublicPrivacyPolicyIndexLazyImport = createFileRoute(
  '/_public/privacy-policy/',
)()
const PublicPresentationIndexLazyImport = createFileRoute(
  '/_public/presentation/',
)()
const PublicLegalNoticesIndexLazyImport = createFileRoute(
  '/_public/legal-notices/',
)()
const PublicEmergencyGuidelinesIndexLazyImport = createFileRoute(
  '/_public/emergency-guidelines/',
)()

// Create/Update Routes

const PublicLazyRoute = PublicLazyImport.update({
  id: '/_public',
  getParentRoute: () => rootRoute,
} as any).lazy(() => import('./routes/_public.lazy').then((d) => d.Route))

const PrivateRoute = PrivateImport.update({
  id: '/_private',
  getParentRoute: () => rootRoute,
} as any).lazy(() => import('./routes/_private.lazy').then((d) => d.Route))

const PublicAuthRoute = PublicAuthImport.update({
  id: '/_auth',
  getParentRoute: () => PublicLazyRoute,
} as any).lazy(() => import('./routes/_public/_auth.lazy').then((d) => d.Route))

const PublicTermsAndConditionsIndexLazyRoute =
  PublicTermsAndConditionsIndexLazyImport.update({
    path: '/terms-and-conditions/',
    getParentRoute: () => PublicLazyRoute,
  } as any).lazy(() =>
    import('./routes/_public/terms-and-conditions/index.lazy').then(
      (d) => d.Route,
    ),
  )

const PublicPrivacyPolicyIndexLazyRoute =
  PublicPrivacyPolicyIndexLazyImport.update({
    path: '/privacy-policy/',
    getParentRoute: () => PublicLazyRoute,
  } as any).lazy(() =>
    import('./routes/_public/privacy-policy/index.lazy').then((d) => d.Route),
  )

const PublicPresentationIndexLazyRoute =
  PublicPresentationIndexLazyImport.update({
    path: '/presentation/',
    getParentRoute: () => PublicLazyRoute,
  } as any).lazy(() =>
    import('./routes/_public/presentation/index.lazy').then((d) => d.Route),
  )

const PublicLegalNoticesIndexLazyRoute =
  PublicLegalNoticesIndexLazyImport.update({
    path: '/legal-notices/',
    getParentRoute: () => PublicLazyRoute,
  } as any).lazy(() =>
    import('./routes/_public/legal-notices/index.lazy').then((d) => d.Route),
  )

const PublicEmergencyGuidelinesIndexLazyRoute =
  PublicEmergencyGuidelinesIndexLazyImport.update({
    path: '/emergency-guidelines/',
    getParentRoute: () => PublicLazyRoute,
  } as any).lazy(() =>
    import('./routes/_public/emergency-guidelines/index.lazy').then(
      (d) => d.Route,
    ),
  )

const PublicSessionExpiredIndexRoute = PublicSessionExpiredIndexImport.update({
  path: '/session-expired/',
  getParentRoute: () => PublicLazyRoute,
} as any)

const PrivateJourneyIndexRoute = PrivateJourneyIndexImport.update({
  path: '/journey/',
  getParentRoute: () => PrivateRoute,
} as any).lazy(() =>
  import('./routes/_private/journey/index.lazy').then((d) => d.Route),
)

const PrivateHomeIndexRoute = PrivateHomeIndexImport.update({
  path: '/home/',
  getParentRoute: () => PrivateRoute,
} as any).lazy(() =>
  import('./routes/_private/home/<USER>').then((d) => d.Route),
)

const PublicAuthSignInAuthIdIndexRoute =
  PublicAuthSignInAuthIdIndexImport.update({
    path: '/sign-in/$auth-id/',
    getParentRoute: () => PublicAuthRoute,
  } as any).lazy(() =>
    import('./routes/_public/_auth/sign-in/$auth-id/index.lazy').then(
      (d) => d.Route,
    ),
  )

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_private': {
      id: '/_private'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PrivateImport
      parentRoute: typeof rootRoute
    }
    '/_public': {
      id: '/_public'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PublicLazyImport
      parentRoute: typeof rootRoute
    }
    '/_public/_auth': {
      id: '/_public/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof PublicAuthImport
      parentRoute: typeof PublicLazyImport
    }
    '/_private/home/': {
      id: '/_private/home/'
      path: '/home'
      fullPath: '/home'
      preLoaderRoute: typeof PrivateHomeIndexImport
      parentRoute: typeof PrivateImport
    }
    '/_private/journey/': {
      id: '/_private/journey/'
      path: '/journey'
      fullPath: '/journey'
      preLoaderRoute: typeof PrivateJourneyIndexImport
      parentRoute: typeof PrivateImport
    }
    '/_public/session-expired/': {
      id: '/_public/session-expired/'
      path: '/session-expired'
      fullPath: '/session-expired'
      preLoaderRoute: typeof PublicSessionExpiredIndexImport
      parentRoute: typeof PublicLazyImport
    }
    '/_public/emergency-guidelines/': {
      id: '/_public/emergency-guidelines/'
      path: '/emergency-guidelines'
      fullPath: '/emergency-guidelines'
      preLoaderRoute: typeof PublicEmergencyGuidelinesIndexLazyImport
      parentRoute: typeof PublicLazyImport
    }
    '/_public/legal-notices/': {
      id: '/_public/legal-notices/'
      path: '/legal-notices'
      fullPath: '/legal-notices'
      preLoaderRoute: typeof PublicLegalNoticesIndexLazyImport
      parentRoute: typeof PublicLazyImport
    }
    '/_public/presentation/': {
      id: '/_public/presentation/'
      path: '/presentation'
      fullPath: '/presentation'
      preLoaderRoute: typeof PublicPresentationIndexLazyImport
      parentRoute: typeof PublicLazyImport
    }
    '/_public/privacy-policy/': {
      id: '/_public/privacy-policy/'
      path: '/privacy-policy'
      fullPath: '/privacy-policy'
      preLoaderRoute: typeof PublicPrivacyPolicyIndexLazyImport
      parentRoute: typeof PublicLazyImport
    }
    '/_public/terms-and-conditions/': {
      id: '/_public/terms-and-conditions/'
      path: '/terms-and-conditions'
      fullPath: '/terms-and-conditions'
      preLoaderRoute: typeof PublicTermsAndConditionsIndexLazyImport
      parentRoute: typeof PublicLazyImport
    }
    '/_public/_auth/sign-in/$auth-id/': {
      id: '/_public/_auth/sign-in/$auth-id/'
      path: '/sign-in/$auth-id'
      fullPath: '/sign-in/$auth-id'
      preLoaderRoute: typeof PublicAuthSignInAuthIdIndexImport
      parentRoute: typeof PublicAuthImport
    }
  }
}

// Create and export the route tree

export const routeTree = rootRoute.addChildren({
  PrivateRoute: PrivateRoute.addChildren({
    PrivateHomeIndexRoute,
    PrivateJourneyIndexRoute,
  }),
  PublicLazyRoute: PublicLazyRoute.addChildren({
    PublicAuthRoute: PublicAuthRoute.addChildren({
      PublicAuthSignInAuthIdIndexRoute,
    }),
    PublicSessionExpiredIndexRoute,
    PublicEmergencyGuidelinesIndexLazyRoute,
    PublicLegalNoticesIndexLazyRoute,
    PublicPresentationIndexLazyRoute,
    PublicPrivacyPolicyIndexLazyRoute,
    PublicTermsAndConditionsIndexLazyRoute,
  }),
})

/* prettier-ignore-end */

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_private",
        "/_public"
      ]
    },
    "/_private": {
      "filePath": "_private.tsx",
      "children": [
        "/_private/home/",
        "/_private/journey/"
      ]
    },
    "/_public": {
      "filePath": "_public.lazy.tsx",
      "children": [
        "/_public/_auth",
        "/_public/session-expired/",
        "/_public/emergency-guidelines/",
        "/_public/legal-notices/",
        "/_public/presentation/",
        "/_public/privacy-policy/",
        "/_public/terms-and-conditions/"
      ]
    },
    "/_public/_auth": {
      "filePath": "_public/_auth.tsx",
      "parent": "/_public",
      "children": [
        "/_public/_auth/sign-in/$auth-id/"
      ]
    },
    "/_private/home/": {
      "filePath": "_private/home/<USER>",
      "parent": "/_private"
    },
    "/_private/journey/": {
      "filePath": "_private/journey/index.tsx",
      "parent": "/_private"
    },
    "/_public/session-expired/": {
      "filePath": "_public/session-expired/index.tsx",
      "parent": "/_public"
    },
    "/_public/emergency-guidelines/": {
      "filePath": "_public/emergency-guidelines/index.lazy.tsx",
      "parent": "/_public"
    },
    "/_public/legal-notices/": {
      "filePath": "_public/legal-notices/index.lazy.tsx",
      "parent": "/_public"
    },
    "/_public/presentation/": {
      "filePath": "_public/presentation/index.lazy.tsx",
      "parent": "/_public"
    },
    "/_public/privacy-policy/": {
      "filePath": "_public/privacy-policy/index.lazy.tsx",
      "parent": "/_public"
    },
    "/_public/terms-and-conditions/": {
      "filePath": "_public/terms-and-conditions/index.lazy.tsx",
      "parent": "/_public"
    },
    "/_public/_auth/sign-in/$auth-id/": {
      "filePath": "_public/_auth/sign-in/$auth-id/index.tsx",
      "parent": "/_public/_auth"
    }
  }
}
ROUTE_MANIFEST_END */
