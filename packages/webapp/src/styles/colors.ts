import { DefaultMantineColor, MantineColorsTuple } from '@mantine/core';

const blue: MantineColorsTuple = [
  'hsla(204, 93%, 65%, 1)', // 0
  'hsla(240, 47%, 24%, 1)', // 1
  'rgba(238, 247, 254, 0.5)', // 2
  'hsla(200, 1%, 43%, 1)', // 3
  'hsla(214, 50%, 44%, 1)', // 4
  'hsla(204, 91%, 64%, 1)', // 5
  'hsla(212, 49%, 46%, 1)', // 6
  'hsla(210, 11%, 96%, 1)', // 7
  'hsla(213, 100%, 85%, 1)', // 8
  '#1d4391',
];

const gray: MantineColorsTuple = [
  'hsla(0, 0%, 24%, 1)', // 0
  'hsla(30, 1%, 69%, 1)', // 1
  'hsla(0, 0%, 96%, 1)', // 2
  'hsla(0, 0%, 53%, 1)', // 3
  'hsla(36, 6%, 15%, 1)', // 4
  'hsla(0, 0%, 44%, 1)', // 5
  '#767c91',
  '#656a7e',
  '#585e72',
  '#4a5167',
];

const green: MantineColorsTuple = [
  'hsla(69, 54%, 52%, 1)', // 0
  '#d2f9e0',
  '#a8f1c0',
  '#7aea9f',
  '#53e383',
  '#3bdf70',
  '#2bdd66',
  '#1ac455',
  '#0caf49',
  '#00963c',
];

const colors: Partial<Record<DefaultMantineColor, MantineColorsTuple>> = {
  blue,
  gray,
  green,
};

export { colors };
