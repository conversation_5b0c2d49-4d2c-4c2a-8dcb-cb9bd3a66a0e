/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import CryptoJS from 'crypto-js';

// Configs
import { env } from '@/configs/env.config';

/* -------------------------------------------------------------------------- */
/*                                  Helper Class                              */
/* -------------------------------------------------------------------------- */

type EncryptAgs = {
  data: unknown;
};

type DecryptArgs = {
  ciphertext: string;
};

export class CryptoJs {
  private static instance: CryptoJs | null = null;
  private secret = env.cryptoJs.scret;

  constructor() {
    if (CryptoJs.instance) {
      return CryptoJs.instance;
    }
    CryptoJs.instance = this;
  }

  /**
   * Encrypts the provided data object using AES encryption.
   * The encrypted data is returned as a string.
   *
   * @param {EncryptAgs} args - The arguments containing the data to be encrypted.
   * @param {unknown} args.data - The data object to be encrypted. This can be any object or value.
   */
  async encrypt({ data }: EncryptAgs) {
    return CryptoJS.AES.encrypt(JSON.stringify(data), this.secret).toString();
  }

  /**
   * Decrypts the provided AES-encrypted string.
   * The decrypted data is returned as a JavaScript object.
   *
   * @param {DecryptArgs} args - The arguments containing the ciphertext to be decrypted.
   * @param {string} args.ciphertext - The AES-encrypted string to be decrypted.
   */
  async decrypt({ ciphertext }: DecryptArgs) {
    const bytes = CryptoJS.AES.decrypt(ciphertext, this.secret);
    return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
  }
}
