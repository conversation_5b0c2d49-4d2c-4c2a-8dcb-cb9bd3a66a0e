/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { notifications } from '@mantine/notifications';

// Utils
import { ApiError } from '@/helpers/api.helper';

// Store
import usePpuStore from '@/stores/ppu';

// Configs
import i18next from '@/configs/organization-i18n.config';

// Constants
import { apiErrors } from '@/constants/api.constant';

// Providers
import { router } from '@/providers/RouterProvider';

/* -------------------------------------------------------------------------- */
/*                                Helper Function                            */
/* -------------------------------------------------------------------------- */

export default async function globalErrorHandler(error: unknown) {
  if (error instanceof ApiError) {
    let message: string = '';

    if (error.method) {
      message =
        apiErrors[error.route]?.[error.method]?.[error.status]?.fr ?? '';
    }

    if (message) {
      return notifications.show({
        color: 'red',
        title: i18next.t('ERROR'),
        message,
      });
    }

    if (error.status === 401 || error.status === 403 || error.status === 500) {
      notifications.show({
        color: 'red',
        title: i18next.t('ERROR'),
        message: i18next.t('OOPS_SOMETHING_WENT_WRONG_PLEASE_TRY_AGAIN_LATER'),
      });

      const { authenticatedUser, signOut } = usePpuStore.getState();
      const isAuthenticatedUser = !!authenticatedUser;

      if (isAuthenticatedUser) {
        await signOut();
        router.navigate({ to: '/home' });
      }

      return;
    }
  }

  notifications.show({
    color: 'red',
    title: i18next.t('ERROR'),
    message: i18next.t('OOPS_SOMETHING_WENT_WRONG_PLEASE_TRY_AGAIN_LATER'),
  });
}
