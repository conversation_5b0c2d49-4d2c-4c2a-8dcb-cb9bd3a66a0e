/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib depencencies
import { notifications } from '@mantine/notifications';

// Configs
import i18next from '@/configs/organization-i18n.config';

// Types
import { User } from '@/types/user';

// Helpers
import { makeUrl } from '@/helpers/api.helper';

// Configs
import { env } from '@/configs/env.config';

// Services (Add event service import)
import { logSentShareLink } from '@/services/event.service';

/* -------------------------------------------------------------------------- */
/*                               Helper functions                             */
/* -------------------------------------------------------------------------- */

export type ShareAccessData = {
  shareApiData: {
    title: string;
    text: string;
  };
  mailToUrl: string;
};

type GetShareAccessDataArgs = {
  session: User;
};

export function getShareAccessData({
  session,
}: GetShareAccessDataArgs): ShareAccessData {
  const accessUrl = makeUrl({
    baseUrl: env.ppu.front.baseUrl,
    path: `/sign-in/${session.sharedId}`,
    searchParams: {
      'is-share-link': true,
      'shared-by': session.firstName,
    },
  });
  const subject = i18next.t('MONITORING_OF_THE_PERSONALIZED');

  const body = `${i18next.t('I_AM_AT_THE_EMERGENCY_DEPARTMENT_OF')}\n${accessUrl}\n\n${i18next.t('YOU_WILL_THEN_NEED_TO_ENTER_MY_LAST_NAME_FIRST_NAME_AND_DATE_OF_BIRTH')}`;
  const mailToUrl = makeUrl({
    path: 'mailto:',
    searchParams: {
      subject,
      body,
    },
  });

  return {
    shareApiData: {
      title: subject,
      text: body,
    },
    mailToUrl,
  };
}

type ShareAccessArgs = {
  session: User;
};

export async function shareAccess({ session }: ShareAccessArgs) {
  try {
    const shareAccessData = getShareAccessData({ session });

    if (!shareApiIsSupported()) {
      window.open(shareAccessData.mailToUrl, '_blank');
      // Log event after attempting mailto fallback
      logSentShareLink();
      return;
    }

    await navigator.share(shareAccessData.shareApiData);
    // Log event after successful navigator.share
    logSentShareLink();

    notifications.show({
      message: i18next.t('YOU_HAVE_BEEN_SUCCESSFULLY_SHARED_YOUR_ACCESS'),
      autoClose: true,
    });
  } catch (error) {
    // Don't log if the share action itself failed (e.g., user cancelled)
    if (error instanceof DOMException && error.name === 'AbortError') {
      console.log('Share action cancelled by user.');
    } else {
      console.log('Error while sharing access:');
      console.error(error);
    }
  }
}

function shareApiIsSupported() {
  return !!navigator.share;
}
