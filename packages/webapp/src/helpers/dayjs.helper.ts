/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import dayjs from 'dayjs';
import 'dayjs/locale/fr';
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';

/* -------------------------------------------------------------------------- */
/*                                 Helper Class                               */
/* -------------------------------------------------------------------------- */

dayjs.extend(relativeTime);
dayjs.extend(updateLocale);

export class Dayjs {
  private static instance: Dayjs;

  constructor() {
    if (Dayjs.instance) {
      return Dayjs.instance;
    }
    Dayjs.instance = this;
    dayjs.updateLocale('fr', {
      relativeTime: {
        future: 'dans %s',
        past: 'il y a %s',
        s: 'moins de 20 mn',
        m: 'une minute',
        mm: '%d minutes',
        h: 'une heure',
        hh: '%d heures',
        d: 'un jour',
        dd: '%d jours',
        M: 'un mois',
        MM: '%d mois',
        y: 'un an',
        yy: '%d ans'
      },
    })
  }

  /**
   * Format a given date using a specified format and locale.
   * @param options - An object containing parameters for date formatting.
   * @param options.date - The date to format, can be a Date object, string, or number.
   * @param [options.locale] - The locale to use for formatting (default is "en").
   * @param [options.format] - The format string to use for formatting (default is "YYYY-MM-DD").
   * @returns The formatted date as a string.
   */
  formatDate({
    date,
    locale = 'fr',
    format = 'YYYY-MM-DD',
  }: {
    date: Date | string | number;
    locale?: 'fr';
    format?: string;
  }): string {
    dayjs.locale(locale);
    return dayjs(date).format(format);
  }

  /**
   * Calculates the duration from the current time to a specified date.
   * If the date is in the past, returns duration as if it was 1 second ago.
   *
   * @param {Object} params - The parameters for the function.
   * @param {Date | number | string} params.date - The target date or timestamp from which to calculate the duration.
   * @param {'fr'} [params.locale] - Optional locale to format the duration. Defaults to the current locale.
   */
  getDurationFromNow({
    date,
    locale = 'fr',
  }: {
    date: Date | number | string;
    locale?: 'fr';
  }) {
    dayjs.locale(locale);
    const now = dayjs();
    const target = dayjs(typeof date === 'number' ? date * 1000 : date);

    if (target.isBefore(now)) {
      return dayjs().add(1, 'second').from(now, true);
    }

    return target.from(now, true);
  }
}
