/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Types
import { OrganizationId } from '@/models/organization';

/* -------------------------------------------------------------------------- */
/*                                Helper Functions                            */
/* -------------------------------------------------------------------------- */

/**
 * Get list of known organizations
 * In the future, this could be dynamic from a config or API
 */
function getKnownOrganizations(): string[] {
  return ['chu-limoges', 'demo-akihospit'];
}

/* -------------------------------------------------------------------------- */
/*                                Helper Functions                            */
/* -------------------------------------------------------------------------- */

/**
 * Extract organization ID from encounter ID
 * Encounter IDs have format: {organization_id}-{encounter_number}
 * e.g., "chu-limoges-12345" -> "chu-limoges"
 */
export function extractOrganizationFromEncounterId(
  encounterId: string
): OrganizationId {
  const lastHyphenIndex = encounterId.lastIndexOf('-');

  if (lastHyphenIndex === -1) {
    return encounterId as OrganizationId;
  }

  return encounterId.substring(0, lastHyphenIndex) as OrganizationId;
}

/**
 * Get the logo path for a specific organization
 */
export function getOrganizationLogoPath(
  organizationId: OrganizationId
): string {
  return `/images/organizations/${organizationId}/logo.png`;
}

/**
 * Check if an organization is known/supported
 */
export function isKnownOrganization(organizationId: OrganizationId): boolean {
  const knownOrganizations = getKnownOrganizations();
  return knownOrganizations.includes(organizationId);
}

/**
 * Get logo path with fallback
 * Returns null if organization is unknown (no logo will be shown)
 */
export function getLogoPathWithFallback(
  organizationId?: OrganizationId
): string | null {
  if (!organizationId || !isKnownOrganization(organizationId)) {
    // Return null for unknown organizations (no logo will be shown)
    return null;
  }
  return getOrganizationLogoPath(organizationId);
}
