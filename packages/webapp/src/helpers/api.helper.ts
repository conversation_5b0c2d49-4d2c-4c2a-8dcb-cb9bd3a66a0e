/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import queryString from 'query-string';

// Types
import { ApiRequestMethod } from '@/types/api';

/* -------------------------------------------------------------------------- */
/*                                Helper class                               */
/* -------------------------------------------------------------------------- */

class ApiError extends Error {
  route: string;
  status: number;
  method?: ApiRequestMethod;
  code?: number;

  constructor({
    route,
    method,
    status = 500,
    message = '',
    code,
  }: {
    route: string;
    method?: ApiRequestMethod;
    status?: number;
    message?: string;
    code?: number;
  }) {
    super(message);
    this.route = route;
    this.method = method;
    this.status = status;
    this.code = code || status;
    this.name = this.constructor.name;
  }
}

/* -------------------------------------------------------------------------- */
/*                               Helper functions                             */
/* -------------------------------------------------------------------------- */

type MakeUrlArgs = {
  path: string;
  searchParams?: Record<string, unknown>;
  baseUrl?: string;
};

/**
 * Generates a URL to make an API call.
 *
 * @param {MakeUrlArgs} args - An object containing the path and optional query parameters.
 * @param {string} args.path - The path for the API call.
 * @param {Record<string, unknown>} [args.searchParams] - Optional query parameters, which will be parsed into a query string (e.g., `{ a: 1, b: 2 }` becomes `"?a=1&b=2"`).
 * @param {string} [args.baseUrl] - An optional base URL to prepend to the path. If not provided, the path will be used as the URL.
 * @returns {string} The constructed URL.
 */
function makeUrl({ path, searchParams, baseUrl }: MakeUrlArgs): string {
  const url = baseUrl ? baseUrl + path : path;

  if (!searchParams) {
    return url;
  }

  // The following section parses the query params for convenience
  // E.g. parses {a: 1, b: 2} to "?a=1&b=2"

  const searchParamsString = `?${queryString.stringify(searchParams)}`;

  return `${url}${searchParamsString}`;
}

export { ApiError, makeUrl };
