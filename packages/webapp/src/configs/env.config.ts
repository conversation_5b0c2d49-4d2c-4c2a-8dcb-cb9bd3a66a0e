/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { z } from 'zod';

/* -------------------------------------------------------------------------- */
/*                                   Config                                   */
/* -------------------------------------------------------------------------- */

const schema = z.object({
  VITE_NODE_ENV: z
    .enum(['development', 'test', 'staging', 'production'])
    .default('production'),
  VITE_PPU_FRONT_BASE_URL: z.string().default(window.location.origin),
  VITE_PPU_API_BASE_URL: z.string().default(window.location.origin + '/api/v1'),
  VITE_CRYPTO_JS_SECRET: z.string().default(Math.random().toString(36)),
});

const parsed = schema.safeParse(import.meta.env);

if (!parsed.success) {
  throw new Error(
    `❌ Invalid environment variables: ${JSON.stringify(parsed.error.format(), null, 4)}`
  );
}

export const env = {
  nodeEnv: parsed.data.VITE_NODE_ENV,
  ppu: {
    front: {
      baseUrl: parsed.data.VITE_PPU_FRONT_BASE_URL,
    },
    api: {
      baseUrl: parsed.data.VITE_PPU_API_BASE_URL,
    },
  },
  cryptoJs: {
    scret: parsed.data.VITE_CRYPTO_JS_SECRET,
  },
} as const;
