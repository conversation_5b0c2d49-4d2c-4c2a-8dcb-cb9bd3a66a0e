/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import i18next, { Resource } from 'i18next';
import { initReactI18next } from 'react-i18next';

// locales
import frTranslation from '@/locales/fr.json';

/* -------------------------------------------------------------------------- */
/*                                   Config                                   */
/* -------------------------------------------------------------------------- */

const resources: Resource = {
  fr: {
    translation: frTranslation,
  },
};

void i18next.use(initReactI18next).init({
  resources,
  lng: 'fr',
});

export default i18next;
