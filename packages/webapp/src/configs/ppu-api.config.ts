/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import axios, {
  type AxiosError,
  type AxiosResponse,
  type InternalAxiosRequestConfig,
} from 'axios';

// Configs
import { env } from '@/configs/env.config';

// Store
import usePpuStore from '@/stores/ppu';

// Providers
import { router } from '@/providers/RouterProvider';

// Helpers
import { ApiError } from '@/helpers/api.helper';
import globalErrorHandler from '@/helpers/global-error-handler.helper';

// Types
import { ApiRequestMethod } from '@/types/api';

/* -------------------------------------------------------------------------- */
/*                                   Config                                   */
/* -------------------------------------------------------------------------- */
const ppuApi = axios.create({
  baseURL: env.ppu.api.baseUrl,
});

const onRequest = async (
  config: InternalAxiosRequestConfig
): Promise<InternalAxiosRequestConfig> => {
  const accessToken = await usePpuStore.getState().getAuthAccessToken();
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`;
  }

  return config;
};

const onRequestError = (error: AxiosError): Promise<AxiosError> => {
  return Promise.reject(error);
};

const onResponse = (response: AxiosResponse) => {
  return response.data;
};

const onResponseError = async (error: AxiosError): Promise<AxiosError> => {
  const { responseURL } = error.request;
  let { pathname } = new URL(responseURL);
  pathname = pathname.replace('/api/v1', '');

  if (error.response?.status === 401 && (error.response?.data as any)?.detail === 'Session expired due to discharge.') {
    const { signOut } = usePpuStore.getState();
    await signOut();

    router.navigate({ to: '/session-expired' });

    return Promise.reject(new Error('Session expired due to discharge, redirecting.'));
  }

  const apiError = new ApiError({
    route: pathname,
    status: error.response?.status,
    method: error.config?.method?.toUpperCase() as ApiRequestMethod,
    message: (error.response?.data as any)?.detail || error.message
  });

  globalErrorHandler(apiError);
  return Promise.reject(error);
};

ppuApi.interceptors.request.use(onRequest, onRequestError);
ppuApi.interceptors.response.use(onResponse, onResponseError);

export default ppuApi;
