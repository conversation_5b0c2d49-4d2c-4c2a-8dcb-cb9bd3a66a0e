/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import i18next, { Resource } from 'i18next';
import { initReactI18next } from 'react-i18next';

// Types
import { OrganizationId } from '@/models/organization';

// Base locales
import frTranslation from '@/locales/fr.json';

// Organization-specific locales
import chuLimogesTranslation from '@/locales/organizations/chu-limoges.json';
import demoAkihospitTranslation from '@/locales/organizations/demo-akihospit.json';

/* -------------------------------------------------------------------------- */
/*                            Organization Config                             */
/* -------------------------------------------------------------------------- */

// Map of organization-specific translations
const organizationTranslations: Record<string, Record<string, string>> = {
  'chu-limoges': chuLimogesTranslation,
  'demo-akihospit': demoAkihospitTranslation,
};

// Current organization ID (will be set dynamically)
let currentOrganizationId: OrganizationId | null = null;

/* -------------------------------------------------------------------------- */
/*                                Enhanced i18n                               */
/* -------------------------------------------------------------------------- */

// Setup resources with enhanced translations
const resources: Resource = {
  fr: {
    translation: {
      ...frTranslation,
      // This will be dynamically enhanced based on organization
    },
  },
};

// Initialize i18next
void i18next.use(initReactI18next).init({
  resources,
  lng: 'fr',
  interpolation: {
    escapeValue: false, // React already escapes
  },
});

/* -------------------------------------------------------------------------- */
/*                              Helper Functions                              */
/* -------------------------------------------------------------------------- */

/**
 * Set the current organization and update translations accordingly
 */
export function setCurrentOrganization(
  organizationId: OrganizationId | null
): void {
  currentOrganizationId = organizationId;

  if (organizationId && organizationTranslations[organizationId]) {
    // Add organization-specific translations to the current language
    i18next.addResourceBundle(
      'fr',
      'translation',
      {
        ...frTranslation,
        ...organizationTranslations[organizationId],
      },
      true, // deep merge
      true // overwrite
    );
  } else {
    // Reset to base translations
    i18next.addResourceBundle(
      'fr',
      'translation',
      frTranslation,
      true, // deep merge
      true // overwrite
    );
  }
}

/**
 * Get current organization ID
 */
export function getCurrentOrganization(): OrganizationId | null {
  return currentOrganizationId;
}

/**
 * Get organization-specific translation
 */
export function getOrganizationTranslation(
  key: string,
  organizationId?: OrganizationId
): string {
  const orgId = organizationId || currentOrganizationId;

  if (orgId && organizationTranslations[orgId]) {
    const orgTranslations = organizationTranslations[orgId];
    if (orgTranslations[key]) {
      return orgTranslations[key];
    }
  }

  // Fallback to base translation
  return frTranslation[key as keyof typeof frTranslation] || key;
}

export default i18next;
