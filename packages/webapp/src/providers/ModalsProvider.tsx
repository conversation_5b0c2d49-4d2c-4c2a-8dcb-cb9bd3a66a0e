/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ReactNode } from 'react';
import { ModalsProvider as NativeModalsProvider } from '@mantine/modals';

// Components
import { ShareAccessModal } from '@/pages/private/Home/components/ShareAccessModal';
import { InfoModal } from '@/pages/private/Journey/components/InfoModal';
import { SolutionPresentationModal } from '@/pages/private/Home/components/SolutionPresentationModal';
import { ConfirmationModal } from '@/components/ConfirmationModal';
import { PainModal } from '@/components/PainModal';
// Styles
import classes from '@/styles/modal.module.css';

/* -------------------------------------------------------------------------- */
/*                                 Provider                                   */
/* -------------------------------------------------------------------------- */

type ModalsProviderProps = {
  children: ReactNode;
};

const modals = {
  'share-access': ShareAccessModal,
  info: InfoModal,
  'solution-presentation': SolutionPresentationModal,
  confirmation: ConfirmationModal,
  pain: PainModal,
};

declare module '@mantine/modals' {
  export interface MantineModalsOverride {
    modals: typeof modals;
  }
}

export default function ModalsProvider({ children }: ModalsProviderProps) {
  return (
    <NativeModalsProvider
      modalProps={{
        classNames: {
          overlay: classes['modal-overlay'],
          content: classes['modal-content'],
          body: classes['modal-body'],
        },
        withCloseButton: false,
        centered: true,
      }}
      modals={modals}
    >
      {children}
    </NativeModalsProvider>
  );
}
