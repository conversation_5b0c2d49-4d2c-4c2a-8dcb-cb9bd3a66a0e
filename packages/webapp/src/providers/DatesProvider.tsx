/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ReactNode } from '@tanstack/react-router';
import { DatesProvider as NativeDatesProvider } from '@mantine/dates';
import '@mantine/dates/styles.css';
import 'dayjs/locale/fr';

/* -------------------------------------------------------------------------- */
/*                                 Provider                                   */
/* -------------------------------------------------------------------------- */

type DatesProviderProps = {
  children: ReactNode;
};

export default function DatesProvider({ children }: DatesProviderProps) {
  return (
    <NativeDatesProvider
      settings={{
        locale: 'fr',
        firstDayOfWeek: 1, // Monday
      }}
    >
      {children}
    </NativeDatesProvider>
  );
}
