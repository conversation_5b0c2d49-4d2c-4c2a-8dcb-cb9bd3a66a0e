/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { Notifications } from '@mantine/notifications';
import { useMantineTheme } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import '@mantine/notifications/styles.css';

/* -------------------------------------------------------------------------- */
/*                                 Provider                                   */
/* -------------------------------------------------------------------------- */

export default function NotificationsProvider() {
  const theme = useMantineTheme();
  const isMobileOrTablet = useMediaQuery(
    `(max-width: ${theme.breakpoints.md})`
  );

  return (
    <Notifications
      position={isMobileOrTablet ? 'top-center' : 'bottom-right'}
    />
  );
}
