/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import { ReactNode } from 'react';
import {
  MantineProvider as NativeMantineProvider,
  createTheme,
} from '@mantine/core';
import '@mantine/core/styles.css';
import '@mantine/carousel/styles.css';

// Styles
import { colors } from '@/styles/colors';
import { fontSizes, lineHeights } from '@/styles/typography';

/* -------------------------------------------------------------------------- */
/*                                 Provider                                   */
/* -------------------------------------------------------------------------- */

const theme = createTheme({
  fontFamily: 'lato, sans-serif',
  colors,
  fontSizes,
  lineHeights,
  headings: {
    fontWeight: '400',
    sizes: {
      h1: {
        fontSize: fontSizes.md,
      },
      h2: {
        fontSize: fontSizes.md,
      },
      h3: {
        fontSize: fontSizes.md,
      },
      h4: {
        fontSize: fontSizes.md,
      },
      h5: {
        fontSize: fontSizes.md,
      },
      h6: {
        fontSize: fontSizes.md,
      },
    },
  },
});

type MantineProviderProps = {
  children: ReactNode;
};

export default function MantineProvider({ children }: MantineProviderProps) {
  return (
    <NativeMantineProvider theme={theme} defaultColorScheme="light">
      {children}
    </NativeMantineProvider>
  );
}
