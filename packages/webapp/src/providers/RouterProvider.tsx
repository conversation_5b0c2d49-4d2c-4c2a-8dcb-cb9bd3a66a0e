/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  RouterProvider as NativeRouterProvider,
  createRouter,
} from '@tanstack/react-router';

// Providers
import { queryClient } from '@/providers/QueryClientProvider';

// Stores
import usePpuStore from '@/stores/ppu';

// Others
import { routeTree } from '@/routeTree.gen';

/* -------------------------------------------------------------------------- */
/*                                 Provider                                   */
/* -------------------------------------------------------------------------- */

// Create a new router instance
export const router = createRouter({
  routeTree,
  context: {
    queryClient,
    isAuthenticated: undefined!,
    session: null,
  },

  // defaultPreload: "intent",
  // Since we're using React Query, we don't want loader calls to ever be stale
  // This will ensure that the loader is always called when the route is preloaded or visited
  // defaultPreloadStaleTime: 0,
});

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

export default function RouterProvider() {
  const { authenticatedUser } = usePpuStore();

  return (
    <NativeRouterProvider
      router={router}
      context={{ isAuthenticated: !!authenticatedUser }}
    />
  );
}
