/* -------------------------------------------------------------------------- */
/*                                Dependencies                                */
/* -------------------------------------------------------------------------- */

// Lib dependencies
import {
  QueryClient,
  QueryClientProvider as NativQueryClientProvider,
} from '@tanstack/react-query';
import { type ReactNode } from 'react';

/* -------------------------------------------------------------------------- */
/*                                 Provider                                   */
/* -------------------------------------------------------------------------- */

export const queryClient = new QueryClient();

type QueryClientProviderProps = {
  children: ReactNode;
};

export default function QueryClientProvider({
  children,
}: QueryClientProviderProps): JSX.Element {
  return (
    <NativQueryClientProvider client={queryClient}>
      {children}
    </NativQueryClientProvider>
  );
}
