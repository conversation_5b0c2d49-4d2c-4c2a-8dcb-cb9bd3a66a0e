export function formatDuration(minutes: number): string {
  if (!minutes) return '';

  let hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours === 0) {
    return `${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
  }

  if (hours < 10 && remainingMinutes < 10) {
    // return `0${hours} heure${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    return `0${hours}h0${remainingMinutes}`;
  }

  if (hours < 10 && remainingMinutes > 9) {
    // return `0${hours} heure${hours !== 1 ? 's' : ''} 0${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    return `0${hours}h${remainingMinutes}`;
  }

  if (hours > 9 && remainingMinutes < 10) {
    // return `${hours} heure${hours !== 1 ? 's' : ''} 0${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
    return `${hours}h0${remainingMinutes}`;
  }

  if (remainingMinutes === 0) {
    return `${hours} heure${hours !== 1 ? 's' : ''}`;
  }

  // return `${hours} heure${hours !== 1 ? 's' : ''} et ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
  return `${hours}h${remainingMinutes}`;
}
