# PPU

"Mon parcours personnalisé aux urgences"

# Technologies

- UI application: library: React

- Typescript

- Routing : Tanstack Router

- Query Management : React Query

- UI component framework: Mantine UI

- Style: CSS Modules

- State management: Zustand

- API communication: REST API

# Prerequisites

Summary

| Node (version 16.13.0) | Node is the server running environment of JavaScript language. |

## Node.js

```

$ sudo apt-get update

$ curl -sL https://deb.nodesource.com/setup_12.x | sudo -E bash -

$ sudo apt install nodejs

$ node -v

v16.13.0

$ npm -v

16.13.0

```

## Pnpm

```
$ npm install -g pnpm

$ pnpm --version

8.8.0

```

## Getting Started 🚀

You can start by cloning this repository and using:

```sh

$ <NAME_EMAIL>:Focus-Sante/ppu.git

```

## Install Project Dependencies

To install the project's dependencies, run the following command:

```sh

pnpm install

```

## Setup environments

Refer to the Zod schema in the env.config.ts file and populate the .env file accordingly to ensure the application runs correctly.

## Run application

```sh

# Start the development server

$ pnpm dev

# Create an optimized production build of your application

$ pnpm build

# Start production server

$ pnpm preview

```

## Folder Structure

This template follows a straightforward project structure:

- `/public`: Contains static assets such as images, fonts, and favicons.
- `/src`: Contains the main application code.
  - `/src/components`: Contains all shared components.
  - `/src/configs`: Contains configuration files, such as interceptor configurations.
  - `/src/constants`: Contains files with static data.
  - `/src/hooks`: Contains custom hooks.
  - `/src/layouts`: Contains layout components for structuring pages.
  - `/src/locales`: Contains JSON files for translations and localization.
  - `/src/pages`: Contains the pages of the application.
  - `/src/providers`: Contains provider components.
  - `/src/routes`: Contains all route definitions (file-based routing).
  - `/src/services`: Contains service modules for handling data operations and API interactions.
  - `/src/stores`: Contains store configurations.
  - `/src/styles`: Contains stylesheets for global and component-specific styling.
  - `/src/types`: Contains custom TypeScript types.
  - `/src/helpers`: Contains utility functions used throughout the project.

## Code Quality

### ESLint

ESLint is used to identify and report on patterns found in TypeScript code. It helps maintain code quality and consistency across the project. The configuration for ESLint is located in the `.eslint.config.js` file. You can run ESLint manually with the following command:

```bash
$ pnpm lint
```

## Prettier

Prettier is an opinionated code formatter that ensures a consistent style across the codebase. It automatically formats your code based on the rules defined in the `.prettierrc` configuration file.

### Installation

To install Prettier, use the following command:

```bash
pnpm add -D prettier
```

## GIT COMMIT MESSAGE STRUCTURE

Good commit messages' structure help the readability for code reviewers. Please follow the following structure for the commits.

**Commit title message should be short, no larger than 50 chars**
Commit body can be used to add more precise explanation for the current commit.

```
    [<action>] <subject> <reason>
```

Some examples :

```
    [refactor] ComponentX for readability
    [remove] PageY not used
    [release] version 1.0.0
```

The list of action can be the following:

- feat: The new feature you're adding to a particular application
- add: Add new element / component to the project
- fix: A bug fix
- edit: An editing of current code
- style: Feature and updates related to styling
- refactor: Refactoring a specific section of the codebase
- test: Everything related to testing
- doc: Everything related to documentation
- maint: Regular code maintenance
- ... etc

## Translation

The application handles translations using i18next. i18next is a powerful internationalization framework that allows for easy management of translations and localization.

### Configuration

Translations are managed through JSON files located in the `src/locales` directory. Each language has its own file containing key-value pairs for translated strings.

### Usage

To use a translation in your components, you can utilize the i18next hooks or higher-order components provided by the `react-i18next` library. This ensures that your application can dynamically switch between languages and display content based on the user's locale.

For more details on how to configure and use i18next, refer to the [i18next documentation](https://www.i18next.com/).
