<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- <link rel="icon" type="image/svg+xml" href="/vite.svg" /> -->
    <link rel="icon" type="image/svg+xml" href="/images/favicon_2.ico">
    <link rel="stylesheet" href="https://use.typekit.net/zmf6vtz.css" />
    <meta
      name="viewport"
      content="minimum-scale=1, initial-scale=1, width=device-width, user-scalable=no"
    />
    <title>MonParcours</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <script>
      // window.axeptioSettings = {
      //   clientId: '64ac1cb4f9b563bb44df1960',
      //   cookiesVersion: 'UC-fr-26-03-2024_Cp',
      // };
      // (function (d, s) {
      //   var t = d.getElementsByTagName(s)[0],
      //     e = d.createElement(s);
      //   e.async = true;
      //   e.src = '//static.axept.io/sdk.js';
      //   t.parentNode.insertBefore(e, t);
      // })(document, 'script');
    </script>
    
    <script>
      // Axeptio tag Cookies
      (function(d, s) {
        var t = d.getElementsByTagName(s)[0], e = d.createElement(s);
        e.async = true; e.src = "//static.axept.io/sdk.js";
        t.parentNode.insertBefore(e, t);
      })(document, "script");

      // Axeptio tag CGU // v1 ››› UC-fr-13-07-2023 //
      function StartAxeptio(val) {
        window.axeptioSettings = {
          clientId: "64ac1cb4f9b563bb44df1960",
          cookiesVersion: "UC-fr-26-03-2024_Cp",
        };

        // will give consent for
        mandateUserId = "user-123452";
        window._axcb = window._axcb || [];
        window._axcb.push(function (sdk) {
        
          window.axeptioSettings.token = window.axeptioSDK.getToken();
        
          sdk.checkContracts(["f7cbdeee-9626-41da-a7b3-1282e326c6bc",
            { name: "cgvu_axeptio", consentFor: mandateUserId }
          ])
        
          // Ajout AM 1 pour affichage des cgu via lien du footer-panel-template.html
          if (val == 1) {
            sdk.openContract("f7cbdeee-9626-41da-a7b3-1282e326c6bc", {
              readOnly: true,
              variables: {username:"André", versionNumber: "1966"},
            })
          }
        
          // Ajout Loïc de Axeptio
          sdk.on("contract:complete", function (result) {
            if (ax_cookies == false) {
              setTimeout(() => {
                window.axeptioSDK.openCookies();
              }, 5000);
            }
            console.log("contract:complete:", result); 
          })
        })
      }
      // StartAxeptio(0)
    </script>

  </body>
</html>
