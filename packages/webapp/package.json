{"name": "ppu", "private": true, "version": "0.14.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write ./src"}, "dependencies": {"@mantine/carousel": "^7.12.1", "@mantine/core": "^7.12.1", "@mantine/dates": "^7.12.1", "@mantine/form": "^7.12.1", "@mantine/hooks": "^7.12.1", "@mantine/modals": "^7.12.1", "@mantine/notifications": "^7.12.1", "@tabler/icons-react": "^3.12.0", "@tanstack/react-query": "^5.51.24", "@tanstack/react-router": "^1.48.4", "add": "^2.0.6", "axios": "^1.8.2", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "embla-carousel-react": "^7.1.0", "framer-motion": "^11.5.2", "i18next": "^23.14.0", "mantine-form-zod-resolver": "^1.1.0", "pnpm": "^10.0.0", "query-string": "^9.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.0.1", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tanstack/eslint-plugin-query": "^5.51.15", "@tanstack/react-query-devtools": "^5.51.24", "@tanstack/router-devtools": "^1.48.4", "@tanstack/router-plugin": "^1.48.6", "@types/crypto-js": "^4.2.2", "@types/node": "^22.4.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.41", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.3.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.4"}}