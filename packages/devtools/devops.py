import base64
import click
import os
import subprocess
from typing import List
from textwrap import dedent
import json
import tempfile
from pathlib import Path
import yaml

from scaleway import Client
from scaleway.secret.v1beta1 import SecretV1Beta1API

scw_profile = os.environ.get("SCW_PROFILE")
if scw_profile is None:
    client = Client.from_config_file_and_env(profile_name="focus")
else:
    client = Client.from_config_file_and_env()

_SCALEWAY_PROJECT_ID = {
    "dev": "************************************",
    "uat": "",
    "prod": "",
    "infra": "************************************",
}

_HOSTNAME = {
    "dev": "ppu.urgenceschrono.com",
    "uat": "",
    "prod": "",
}

_CADDY_FRONTEND_HOSTNAME = {
    "dev": "ppu.urgenceschrono.com",
    "uat": "",
    "prod": "",
}

_CADDY_BACKEND_HOSTNAME = {
    "dev": "backend.ppu.urgenceschrono.com",
    "uat": "",
    "prod": "",
}


@click.group()
def devops():
    """DevOps commands."""
    pass


@devops.command()
@click.argument(
    "configuration_name",
    type=click.Choice(["infrastructure", "application"]),
)
@click.argument(
    "command",
    type=click.Choice(["apply", "destroy"]),
    default="apply",
)
@click.option("--env", type=str, default="dev")
def terraform(configuration_name: str, env: str, command: str) -> List[str]:
    """
    Applies the configuration level of Terraform.
    """
    if configuration_name == "infrastructure" and env != "infra":
        raise Exception(
            "You can only run the infrastructure when setting env as infra."
        )

    if command == "destroy" and configuration_name == "infrastructure":
        raise Exception("You can't destroy the infrastructure.")

    return _terraform(configuration_name, env, command)


def _terraform(configuration_name: str, env: str, command: str) -> List[str]:
    _set_scw_credentials(env)

    cwd = ""
    if configuration_name == "infrastructure":
        cwd = "deployment/terraform/infrastructure"
    else:
        cwd = "deployment/terraform/application"

    backend_config_arg = (
        f"-backend-config=environments/{env}.tfbackend" if env != "infra" else ""
    )
    var_file_arg = (
        f"-var-file=environments/{env}.tfvars"
        if env != "infra"
        else "-var-file=environments/global.tfvars"
    )

    scw_profile = os.environ.get("SCW_PROFILE", "focus")

    with tempfile.TemporaryDirectory() as tmpdir:
        tmpdir_path = Path(tmpdir)
        terraform_output_local_path = tmpdir_path / "terraform_output.json"
        subprocess.run(
            dedent(
                f"""
                export SCW_PROFILE={scw_profile}
            terraform init -reconfigure -upgrade {backend_config_arg}
            terraform {command} {var_file_arg}
            terraform output -json > {terraform_output_local_path}
            """
            ),
            cwd=cwd,
            shell=True,
            check=True,
        )

        with terraform_output_local_path.open("r") as f:
            data = json.load(f)

    return data


def _extract_ssh_key(tmpdir: Path, env: str) -> None:
    secret_api = SecretV1Beta1API(client)
    ssh_key_path = tmpdir / "ssh_key"
    ssh_public_key_path = tmpdir / "ssh_key.pub"

    try:
        secret_version = secret_api.access_secret_version_by_path(
            secret_name="ssh-key",
            secret_path="",
            revision="latest_enabled",
            project_id=_SCALEWAY_PROJECT_ID[env],
        )
    except Exception:
        raise Exception("SSH key not found in Scaleway Secrets Manager.")

    with open(ssh_key_path, "w") as f:
        f.write(base64.b64decode(secret_version.data).decode())
        f.write("\n")  # Ensure the SSH key has an ending newline

    ssh_key_path.chmod(0o600)

    subprocess.run(
        f"ssh-keygen -y -f {ssh_key_path} > {ssh_public_key_path}",
        shell=True,
        check=True,
    )


@devops.command()
@click.option("--env", type=str, default="dev")
def deploy_all(env: str) -> None:
    _set_scw_credentials(env)
    _terraform("application", env, "apply")
    _docker_build(env)
    _docker_push(env)
    _deploy(env)


@devops.command()
@click.option("--env", type=str, default="dev")
def deploy(env: str) -> None:
    _set_scw_credentials(env)
    _deploy(env)


def _deploy(env: str) -> None:
    secret_api = SecretV1Beta1API(client)

    # extract SSH key, set ansible inventory, run ansible playbook, delete ssh key
    with tempfile.TemporaryDirectory() as tmpdir:
        tmpdir_path = Path(tmpdir)
        ssh_key_path = tmpdir_path / "ssh_key"
        _extract_ssh_key(tmpdir_path, env)
        try:
            env_file_content_version = secret_api.access_secret_version_by_path(
                secret_name="env-vars",
                secret_path="",
                revision="latest_enabled",
                project_id=_SCALEWAY_PROJECT_ID[env],
            )
        except Exception:
            raise Exception("env-vars secret not found in Scaleway Secrets Manager.")
        env_file_content = base64.b64decode(env_file_content_version.data).decode()

        ansible_inventory = {
            "all": {
                "hosts": {
                    "ppu": {
                        "ansible_host": _HOSTNAME[env],
                        "ansible_ssh_private_key_file": str(ssh_key_path),
                    }
                },
                "vars": {
                    "env": env,
                    "frontend_host": _CADDY_FRONTEND_HOSTNAME[env],
                    "backend_host": _CADDY_BACKEND_HOSTNAME[env],
                    "env_file_content": env_file_content,
                },
            }
        }

        inventory_path = os.path.join(tmpdir_path, "inventory.yml")
        with open(inventory_path, "w") as f:
            yaml.dump(ansible_inventory, f)

        run = subprocess.run(
            dedent(
                f"ansible-playbook deployment/ansible/playbook.yaml -i {inventory_path}"
            ),
            shell=True,
        )
        if run.returncode != 0:
            print("Deploy failed.")
            exit(1)


@devops.command()
@click.option("--env", type=str, default="dev")
def build(env: str) -> None:
    _docker_build(env)


def _docker_build(env: str) -> None:
    os.environ["IMAGE_TAG"] = env

    run = subprocess.run(
        dedent(
            f"""
        docker compose -f compose.yml -f compose.local.yml build
        """
        ),
        shell=True,
    )

    if run.returncode != 0:
        print("Docker build failed.")
        exit(1)


@devops.command()
@click.option("--env", type=str, default="dev")
def push(env: str) -> None:
    _docker_push(env)


def _docker_push(env: str) -> None:
    _set_scw_credentials(env)
    run = subprocess.run(dedent("scw registry login"), shell=True)
    if run.returncode != 0:
        print("Docker login failed.")
        exit(1)
    os.environ["IMAGE_TAG"] = env
    run = subprocess.run(
        dedent(
            """
        docker compose --parallel 1 -f compose.yml -f compose.local.yml push
        """
        ),
        shell=True,
    )
    if run.returncode != 0:
        print("Docker push failed.")
        exit(1)


@devops.command()
@click.option("--env", type=str, default="dev")
def ssh(env: str) -> None:
    _set_scw_credentials(env)
    with tempfile.TemporaryDirectory() as tmpdir:
        tmpdir_path = Path(tmpdir)
        ssh_key_path = tmpdir_path / "ssh_key"
        _extract_ssh_key(tmpdir_path, env)

        subprocess.run(
            dedent(
                f"ssh -i {ssh_key_path} -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null debian@{_HOSTNAME[env]}"
            ),
            shell=True,
        )


@devops.command()
@click.option("--env", type=str, default="dev")
def show_scw_credentials(env: str) -> None:
    """
    Shows the Scaleway credentials. This is useful for debugging, if you want to export all the variables you need to manually run commands.
    """
    _set_scw_credentials(env)
    if os.environ.get("SCW_PROFILE") is None:
        print("export SCW_PROFILE=focus")
    print(f"export AWS_ACCESS_KEY_ID={os.environ['AWS_ACCESS_KEY_ID']}")
    print(f"export AWS_SECRET_ACCESS_KEY={os.environ['AWS_SECRET_ACCESS_KEY']}")


def _set_scw_credentials(env: str) -> None:
    if os.environ.get("SCW_PROFILE") is None:
        os.environ["SCW_PROFILE"] = "focus"
    scw_access_key = client.access_key
    scw_secret_key = client.secret_key

    assert scw_access_key is not None
    assert scw_secret_key is not None

    os.environ["AWS_ACCESS_KEY_ID"] = f"{scw_access_key}@{_SCALEWAY_PROJECT_ID[env]}"
    os.environ["AWS_SECRET_ACCESS_KEY"] = scw_secret_key
