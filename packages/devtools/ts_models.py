import importlib
from pathlib import Path
import re
from packages.ts_types.generate import ModuleName, generate_ts_for_modules


#: Python Package containing the models.
_MODELS_PACKAGE = "packages.backend.models"

#: Path where the TypeScript types are written.  This is a folder, created if
#: it does not exist.  One TypeScript file is written per Python module contained
#: in the models package.
_TS_MODELS_PATH = Path(__file__).parent / "../webapp/src/models"


def generate_ts_for_projectname() -> None:
    models_init_path_str = importlib.import_module(_MODELS_PACKAGE).__file__
    assert models_init_path_str is not None
    models_init_path = Path(models_init_path_str)
    assert models_init_path.is_file() and models_init_path.name == "__init__.py"
    models_path = models_init_path.parent

    module_names = [
        re.sub(r"\.py$", "", path.name)
        for path in models_path.iterdir()
        if path.is_file() and path.name.endswith(".py")
    ]

    output = generate_ts_for_modules(
        [ModuleName(f"{_MODELS_PACKAGE}.{name}") for name in module_names]
    )

    output.write(_TS_MODELS_PATH)
