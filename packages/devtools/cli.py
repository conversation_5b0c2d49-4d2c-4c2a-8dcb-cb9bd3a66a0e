from textwrap import dedent
import click
import subprocess

from packages.devtools.devops import devops
from packages.devtools.ts_models import generate_ts_for_projectname


@click.group()
def cli():
    pass


@cli.command()
def check_types() -> None:
    """
    Checks all the Python types using mypy.
    """
    print("*** mypy ***")
    subprocess.run(
        "poetry run mypy --config-file=mypy.ini",
        shell=True,
        check=False,
    )


@cli.command()
def format() -> None:
    """
    Formats all the code.
    """
    subprocess.run(
        dedent(
            """
        find ./packages -type d \\( -name '.git' -o -name 'venv' -o -name 'node_modules' \\) \\
            -prune -o -name '*.py' -type f -print0 | \\
            xargs -0 poetry run black

        (cd packages/webapp && npx prettier . --write)
    """
        ),
        shell=True,
        check=False,
    )


@cli.command()
def generate_ts_models() -> None:
    """Generates TypeScript models from the Python models."""
    generate_ts_for_projectname()


@cli.command()
def pytest() -> None:
    """Runs the tests."""
    subprocess.run(
        "poetry run pytest",
        shell=True,
        check=False,
    )


cli.add_command(devops)


if __name__ == "__main__":
    cli()
