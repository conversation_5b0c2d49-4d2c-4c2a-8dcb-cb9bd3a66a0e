organization:
  - id: "chu-limoges-modele"
    name: "CHU Limoges"
    description: "Centre Hospitalier Universitaire de Limoges"
    patient_session_timeout_minutes: 60
    healthcare_service:
      - id: "chu-limoges-emergency"
        name: "Urgences adultes"
        description: "Urgences adultes"
        questionnaire:
          - id: douleur
        activity_definition:
          - id: "chu-limoges-emergency-activity-ioa"
            shareable: false
            name: "Orientation par l'infirmière IOA"
            description: "Après avoir pris connaissance de votre dossier, l'infirmière vous orientera vers une filière."
            send_notification:
              - trigger: "null"
                message: "Votre passage auprès de l'IOA est imminent"
          - id: "chu-limoges-emergency-activity-consultation"
            shareable: true
            name: "Consultation médicale"
            description: "Consultation médicale"
            send_notification:
              - trigger: 2
                message: "Vous allez voir le Docteur dans quelques minutes"
              - trigger: "not-yet-started"
                message: "Vous êtes dans la file d'attente pour le Dr"
        discharge_instructions:
          - name: "Platre"
            id: "Platre"
          - name: "TC"
            id: "TC"
      - id: "chu-limoges-emergency-pediatriques"
        name: "Urgences pédiatriques"
        description: "Urgences pédiatriques"
        questionnaire:
          - id: douleur
        activity_definition:
          - id: "chu-limoges-emergency-pediatriques-activity-ioa"
            shareable: false
            name: "Orientation par l'infirmière IOA"
            description: "Après avoir pris connaissance de votre dossier, l'infirmière vous orientera vers une filière."
            send_notification:
              - trigger: "in-progress"
                message: "Votre passage auprès de l'IOA est imminent"
          - id: "chu-limoges-emergency-pediatriques-activity-consultation"
            shareable: true
            name: "Consultation médicale"
            description: "Consultation médicale"
            send_notification:
              - trigger: 2
                message: "Vous allez voir le Docteur dans quelques minutes"
              - trigger: "not-yet-started"
                message: "Vous êtes dans la file d'attente pour le Dr"
        discharge_instructions:
          - name: "Platre"
            id: "Platre"
          - name: "TC"
            id: "TC"
      - id: "chu-limoges-emergency-filiere-generale"
        name: "Adultes - Circuit général"
        description: "Filière adultes - Circuit général"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-ambulatoire"
        name: "Ambulatoire - Circuit court"
        description: "Filière Ambulatoire - Circuit court"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-medico-chir"
        name: "Médico-chirurgicale"
        description: "Filière Médico-chirurgicale"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-psychiatrie"
        name: "Psychiatrie"
        description: "Filière Psychiatrie"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-sauvegarde"
        name: "Sauvegarde"
        description: "Filière Sauvegarde"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-infection"
        name: "Maladies infectieuses"
        description: "Filière maladies infectieuses"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-pediatriques-filiere-generale"
        name: "Pédiatrique Générale"
        description: "Filière pédiatrique générale"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-plaies"
        name: "Plaies"
        description: "Filière Plaies"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-medecine"
        name: "Médecine"
        description: "Filière Médecine"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-traumateau-chir"
        name: "Traumato-chirurgicale"
        description: "Filière Traumato-chirurgicale"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-psychiatrie"
        name: "Psychiatrie"
        description: "Filière Psychiatrie"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-infection"
        name: "Maladies infectieuses"
        description: "Filière maladies infectieuses"
        offered_in: "chu-limoges-emergency-pediatriques"

  - id: "demo-akihospit"
    name: "Aki Hopital"
    description: "Hopital de demo/test"
    patient_session_timeout_minutes: 2880
    healthcare_service:
      - id: "demo-akihospit-emergency"
        name: "Ambulatoire - Circuit court"
        description: "Urgences générales"
        questionnaire:
          - id: douleur
        activity_definition:
          - id: "demo-akihospit-emergency-activity-ioa"
            shareable: true
            name: "Orientation par l'infirmière IOA"
            description: "Après avoir pris connaissance de votre dossier, l'infirmière vous orientera vers une filière."
          - id: "demo-akihospit-emergency-activity-medical-care"
            shareable: true
            name: "Prise en charge médicale"
            description: "Prise en charge médicale du patient"
            send_notification:
              - trigger: 2
                message: "Vous allez voir le Docteur dans quelques minutes"
              - trigger: "not-yet-started"
                message: "Vous êtes dans la file d'attente pour le Dr"
          - id: "demo-akihospit-emergency-activity-imagerie"
            shareable: true
            name: "Imagerie"
            description: "Réalisation d'une imagerie médicale"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-imagerie-analysis"
            shareable: true
            name: "Analyse de l'imagerie"
            description: "Analyse médicale de l'imagerie"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-xray"
            shareable: true
            name: "Radiographie"
            description: "Réalisation d'une radiographie"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-xray-analysis"
            shareable: true
            name: "Analyse de la radiographie"
            description: "Analyse médicale de la radiographie"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-scan"
            shareable: true
            name: "Scanner"
            description: "Réalisation d'un scanner"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-scan-analysis"
            shareable: true
            name: "Analyse du scanner"
            description: "Analyse médicale de la radiographie"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-echo"
            shareable: true
            name: "Echographie"
            description: "Réalisation d'un scanner"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-echo-analysis"
            shareable: true
            name: "Analyse de l'échographie"
            description: "Analyse médicale de la radiographie"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-irm"
            shareable: true
            name: "IRM"
            description: "Réalisation d'un IRM"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-irm-analysis"
            shareable: true
            name: "Analyse de l'IRM'"
            description: "Analyse médicale d'IRM'"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-blood-test"
            shareable: true
            name: "Prise de sang"
            description: "Réalisation d'une prise de sang"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-urine-test"
            shareable: true
            name: "Test urinaire"
            description: "Réalisation d'un test urinaire"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-test-analysis"
            shareable: true
            name: "Analyse des tests"
            description: "Analyse médicale des tests sanguins et/ou urinaires"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-specialist-consult"
            shareable: true
            name: "Avis spécialiste"
            description: "Attente de l'avis d'un spécialiste"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-cardiologist-consult"
            shareable: true
            name: "Avis cardiologue"
            description: "Attente de l'avis d'un cardiologue"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-neurologist-consult"
            shareable: true
            name: "Avis neurologue"
            description: "Attente de l'avis d'un neurologue"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-orthopedist-consult"
            shareable: true
            name: "Avis orthopédiste"
            description: "Attente de l'avis d'un orthopédiste"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-advanced-consultation"
            shareable: true
            name: "Consultation médicale avancée"
            description: "Consultation médicale plus approfondie"
            default_wait_time: 120
          - id: "demo-akihospit-emergency-activity-cardiac-arrest"
            shareable: false
            name: "Déchoc"
            description: "Prise en charge d'un patient en arrêt cardiaque"
            default_wait_time: 120
      - id: "demo-akihospit-soins-non-programmes"
        name: "Soins non programmes"
        description: "Soins non programmes"
        activity_definition:
          - id: "demo-akihospit-soins-non-programmes-activity-ioa"
            shareable: true
            name: "Orientation par l'infirmière IOA"
            description: "Après avoir pris connaissance de votre dossier, l'infirmière vous orientera vers une filière."
            send_notification:
              - trigger: "in-progress"
                message: "Votre passage auprès de l'IOA est imminent"
          - id: "demo-akihospit-soins-non-programmes-activity-medical-care"
            shareable: true
            name: "Prise en charge médicale"
            description: "Prise en charge médicale du patient"
            send_notification:
              - trigger: 2
                message: "Vous allez voir le Docteur dans quelques minutes"
              - trigger: "not-yet-started"
                message: "Vous êtes dans la file d'attente pour le Dr"
      - id: "demo-akihospit-emergency-pediatriques"
        name: "Urgences pédiatriques"
        description: "Urgences pédiatriques"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-adultes"
        name: "Urgences adultes"
        description: "Urgences adultes"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-soins-non-programmes-pediatriques"
        name: "Soins non programmes pédiatriques"
        description: "Soins non programmes pédiatriques"
        offered_in: "demo-akihospit-soins-non-programmes"
      - id: "demo-akihospit-soins-non-programmes-adultes"
        name: "Soins non programmes adultes"
        description: "Soins non programmes adultes"
        offered_in: "demo-akihospit-soins-non-programmes"
      - id: "demo-akihospit-emergency-filiere-generale"
        name: "Circuit général"
        description: "Filière adultes - Circuit général"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-filiere-ambulatoire"
        name: "Ambulatoire - Circuit court"
        description: "Filière Ambulatoire - Circuit court"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-filiere-medico-chir"
        name: "Médico-chirurgicale"
        description: "Filière Médico-chirurgicale"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-filiere-psychiatrie"
        name: "Psychiatrie"
        description: "Filière Psychiatrie"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-filiere-sauvegarde"
        name: "Sauvegarde"
        description: "Filière Sauvegarde"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-filiere-infection"
        name: "Maladies infectieuses"
        description: "Filière maladies infectieuses"
        offered_in: "demo-akihospit-emergency"
      - id: "demo-akihospit-emergency-pediatriques-filiere-generale"
        name: "Générale"
        description: "Filière pédiatrique générale"
        offered_in: "demo-akihospit-emergency-pediatriques"
      - id: "demo-akihospit-emergency-pediatriques-filiere-plaies"
        name: "Plaies"
        description: "Filière pédiatrique plaies"
        offered_in: "demo-akihospit-emergency-pediatriques"
      - id: "demo-akihospit-emergency-pediatriques-filiere-medecine"
        name: "Médecine"
        description: "Filière Médecine"
        offered_in: "demo-akihospit-emergency-pediatriques"
      - id: "demo-akihospit-emergency-pediatriques-filiere-traumateau-chir"
        name: "Traumato-chirurgicale"
        description: "Filière Traumato-chirurgicale"
        offered_in: "demo-akihospit-emergency-pediatriques"
      - id: "demo-akihospit-emergency-pediatriques-filiere-psychiatrie"
        name: "Psychiatrie"
        description: "Filière Psychiatrie"
        offered_in: "demo-akihospit-emergency-pediatriques"
      - id: "demo-akihospit-emergency-pediatriques-filiere-infection"
        name: "Maladies infectieuses"
        description: "Filière maladies infectieuses"
        offered_in: "demo-akihospit-emergency-pediatriques"

  - id: "chu-limoges"
    name: "CHU Limoges"
    description: "Centre Hospitalier Universitaire de Limoges"
    patient_session_timeout_minutes: 60
    healthcare_service:
      - id: "chu-limoges-emergency"
        name: "Ambulatoire - Circuit court"
        description: "Urgences générales"
        questionnaire:
          - id: douleur
        activity_definition:
          - id: "chu-limoges-emergency-activity-ioa"
            shareable: true
            name: "Orientation par l'infirmière IOA"
            description: "Après avoir pris connaissance de votre dossier, l'infirmière vous orientera vers une filière."
          - id: "chu-limoges-emergency-activity-medical-care"
            shareable: true
            name: "Prise en charge médicale"
            description: "Prise en charge médicale du patient"
            send_notification:
              - trigger: 2
                message: "Vous allez voir le Docteur dans quelques minutes"
              - trigger: "not-yet-started"
                message: "Vous êtes dans la file d'attente pour le Dr"
          - id: "chu-limoges-emergency-activity-imagerie"
            shareable: true
            name: "Imagerie"
            description: "Réalisation d'une imagerie médicale"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-imagerie-analysis"
            shareable: true
            name: "Analyse de l'imagerie"
            description: "Analyse médicale de l'imagerie"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-xray"
            shareable: true
            name: "Radiographie"
            description: "Réalisation d'une radiographie"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-xray-analysis"
            shareable: true
            name: "Analyse de la radiographie"
            description: "Analyse médicale de la radiographie"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-scan"
            shareable: true
            name: "Scanner"
            description: "Réalisation d'un scanner"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-scan-analysis"
            shareable: true
            name: "Analyse du scanner"
            description: "Analyse médicale de la radiographie"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-echo"
            shareable: true
            name: "Echographie"
            description: "Réalisation d'un scanner"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-echo-analysis"
            shareable: true
            name: "Analyse de l'échographie"
            description: "Analyse médicale de la radiographie"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-irm"
            shareable: true
            name: "IRM"
            description: "Réalisation d'un IRM"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-irm-analysis"
            shareable: true
            name: "Analyse de l'IRM'"
            description: "Analyse médicale d'IRM'"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-blood-test"
            shareable: true
            name: "Prise de sang"
            description: "Réalisation d'une prise de sang"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-urine-test"
            shareable: true
            name: "Test urinaire"
            description: "Réalisation d'un test urinaire"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-test-analysis"
            shareable: true
            name: "Analyse des tests"
            description: "Analyse médicale des tests sanguins et/ou urinaires"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-specialist-consult"
            shareable: true
            name: "Avis spécialiste"
            description: "Attente de l'avis d'un spécialiste"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-cardiologist-consult"
            shareable: true
            name: "Avis cardiologue"
            description: "Attente de l'avis d'un cardiologue"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-neurologist-consult"
            shareable: true
            name: "Avis neurologue"
            description: "Attente de l'avis d'un neurologue"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-orthopedist-consult"
            shareable: true
            name: "Avis orthopédiste"
            description: "Attente de l'avis d'un orthopédiste"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-advanced-consultation"
            shareable: true
            name: "Consultation médicale avancée"
            description: "Consultation médicale plus approfondie"
            default_wait_time: 120
          - id: "chu-limoges-emergency-activity-cardiac-arrest"
            shareable: false
            name: "Déchoc"
            description: "Prise en charge d'un patient en arrêt cardiaque"
            default_wait_time: 120
      - id: "chu-limoges-soins-non-programmes"
        name: "Soins non programmes"
        description: "Soins non programmes"
        activity_definition:
          - id: "chu-limoges-soins-non-programmes-activity-ioa"
            shareable: true
            name: "Orientation par l'infirmière IOA"
            description: "Après avoir pris connaissance de votre dossier, l'infirmière vous orientera vers une filière."
            send_notification:
              - trigger: "in-progress"
                message: "Votre passage auprès de l'IOA est imminent"
          - id: "chu-limoges-soins-non-programmes-activity-medical-care"
            shareable: true
            name: "Prise en charge médicale"
            description: "Prise en charge médicale du patient"
            send_notification:
              - trigger: 2
                message: "Vous allez voir le Docteur dans quelques minutes"
              - trigger: "not-yet-started"
                message: "Vous êtes dans la file d'attente pour le Dr"
      - id: "chu-limoges-emergency-pediatriques"
        name: "Urgences pédiatriques"
        description: "Urgences pédiatriques"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-adultes"
        name: "Urgences adultes"
        description: "Urgences adultes"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-soins-non-programmes-pediatriques"
        name: "Soins non programmes pédiatriques"
        description: "Soins non programmes pédiatriques"
        offered_in: "chu-limoges-soins-non-programmes"
      - id: "chu-limoges-soins-non-programmes-adultes"
        name: "Soins non programmes adultes"
        description: "Soins non programmes adultes"
        offered_in: "chu-limoges-soins-non-programmes"
      - id: "chu-limoges-emergency-filiere-generale"
        name: "Circuit général"
        description: "Filière adultes - Circuit général"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-ambulatoire"
        name: "Ambulatoire - Circuit court"
        description: "Filière Ambulatoire - Circuit court"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-medico-chir"
        name: "Médico-chirurgicale"
        description: "Filière Médico-chirurgicale"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-psychiatrie"
        name: "Psychiatrie"
        description: "Filière Psychiatrie"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-sauvegarde"
        name: "Sauvegarde"
        description: "Filière Sauvegarde"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-filiere-infection"
        name: "Maladies infectieuses"
        description: "Filière maladies infectieuses"
        offered_in: "chu-limoges-emergency"
      - id: "chu-limoges-emergency-pediatriques-filiere-generale"
        name: "Générale"
        description: "Filière pédiatrique générale"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-plaies"
        name: "Plaies"
        description: "Filière pédiatrique plaies"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-medecine"
        name: "Médecine"
        description: "Filière Médecine"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-traumateau-chir"
        name: "Traumato-chirurgicale"
        description: "Filière Traumato-chirurgicale"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-psychiatrie"
        name: "Psychiatrie"
        description: "Filière Psychiatrie"
        offered_in: "chu-limoges-emergency-pediatriques"
      - id: "chu-limoges-emergency-pediatriques-filiere-infection"
        name: "Infection"
        description: "Filière Infection"
        offered_in: "chu-limoges-emergency-pediatriques"
