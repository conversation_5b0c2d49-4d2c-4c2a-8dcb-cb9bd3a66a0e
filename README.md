# Parcours patient aux urgences

This project is a key component of the Urgences Chrono service, offering patients a personalized interface to monitor their emergency care journey in French hospitals. Patient records are sourced directly from hospitals via the Urgences Chrono service and made accessible to patients through an SMS link.

Key features include:

- The ability for patients to share their care journey with relatives.
- Provision of after-care documents, tailored to the patient's condition, for easy access on their mobile devices.

## Requirements

### Local Development

- Node (LTS latest)
- Python 3.12
- Poetry

### Cloud Development & Deployment

- Docker
- Docker Compose
- Terraform
- Ansible
- Scaleway CLI (https://github.com/scaleway/scaleway-cli)
- Scaleway API key (put it under a profile named ppu)

## Run locally

### Backend

```
poetry install --with dev
poetry run uvicorn packages.backend.server.main:app --port=8080 --reload
```

Access the API Swagger: http://localhost:8080/docs#/

### Frontend

See the [frontend README](./packages/webapp/README.md)

### Everything in Docker Compose

By running the stack in Docker Compose, you are reproducing "production" conditions, and can validate the Dockerfiles and build of the components.
This is recommanded only for testing a bunch of changes, as is it not meant for developement.

```sh
docker compose -f compose.yml -f compose.local.yml up
```

## Cloud deployment

### Password

The dev and uat envs are protected by a user/password.
User: ppu
Password: ppupassword

https://backend.ppu.urgenceschrono.com/docs

### Install dependencies

```sh
poetry install --with dev
```

### Obtain your Scaleway API key

Once you have an account, select the project organization (top right) and go to "API keys". Generate a new API key for yourself. You don't need to select anything for Project preference.

On the screen where your API key is shown, click "Add API keys to your environement", copy the command under "Scaleway CLI". Paste it into your terminal, but make sure to change the argument after -p from newprofile to project.

**If SCW asks you if you want to add your SSH key, select no**

You should now have your API key setup.

### Basics of using the cli devops

Most commands will have the optional argument --env, which can be set to dev, uat, prod or infra. By default, it will be dev.
This is used to let you chose which environement you wish to apply the changes to.

### Deploying everything

Run the following command:

```sh
./cli devops deploy-all
```

This command will, in order:

- Set up your Scaleway credentials
- Run terraform for the given environment
- Build the docker images for the frontend and backend
- Push those images to Scaleway (IMPORTANT: if the push fails with errors 500 it's a known issue, just try again until it works)
- Deploy the Docker images onto the server
  - This step also downloads the SSH keys required for accessing the server.
  - Through ansible, the server will have Docker installed, the Docker images downloaded and launched. The web server will be configured for the hostname of the environement

You can run any of those steps manually by running the following:

```sh
./cli devops terraform application
./cli devops build
./cli devops push
./cli devops deploy
```

### Updating preprod or production servers

When updating the preprod or production servers, the process is:

1. On your local machine, build and push the new images to the registry:

```sh
./cli devops build
./cli devops push
```

This will build new Docker images for your changes and push them to the Scaleway Container Registry.

2. Once the images are pushed successfully, SSH into the target server (preprod or production)
   ››› SSH UC_Preprod
3. Navigate to `/opt/uc/ppu`

4. Pull the latest images from the registry and restart the services:

```sh
sudo docker compose pull
sudo docker compose up -d
```

These commands will:

- `docker compose pull`: Download the latest versions of the images from the registry
- `docker compose up -d`: Restart the containers with the new images. Only containers whose images have been modified will be restarted.

This workflow ensures that your changes are properly built, stored in the registry, and then deployed to the target environment.

### Destroying an environement

The dev environement is built so that it can be destroyed as required. To destroy it run the following:

```sh
./cli devops terraform application destroy
```

## User account reference

![Capture d'écran 2024-08-06 à 12 18 06](https://github.com/user-attachments/assets/3af25f65-f567-4898-b6f6-8daa7127203b)
