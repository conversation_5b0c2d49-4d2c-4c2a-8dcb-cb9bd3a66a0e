services:
  frontend:
    image: rg.fr-par.scw.cloud/ppu-builds/frontend:${IMAGE_TAG:-dev}
    ports:
      - 3000:3000
    restart: unless-stopped
  backend:
    image: rg.fr-par.scw.cloud/ppu-builds/backend:${IMAGE_TAG:-dev}
    ports:
      - 8000:8080
    restart: unless-stopped
    volumes:
      - ./assets:/app/assets
      - ./.db:/app/.db
    env_file:
      - path: .env
        required: false