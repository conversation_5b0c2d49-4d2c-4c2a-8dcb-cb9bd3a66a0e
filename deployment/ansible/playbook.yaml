- name: Deploy project environment
  hosts: ppu
  remote_user: debian
  vars:
    ansible_ssh_common_args: -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null

  tasks:
    - name: Install docker
      become: true
      block:
        - name: Install ca-certificates and curl
          apt:
            pkg:
              - ca-certificates
              - curl
            state: latest
            update_cache: yes
        - name: Add docker repository
          block:
            - name: Check if repo key exists
              stat:
                path: /etc/apt/keyrings/docker.asc
              register: docker_repo_key
            - name: Add repo key
              when: not docker_repo_key.stat.exists
              get_url:
                url: https://download.docker.com/linux/debian/gpg
                dest: /etc/apt/keyrings/docker.asc
            - name: Chmod repo key
              file:
                path: /etc/apt/keyrings/docker.asc
                mode: a+r
            - name: Add repo to apt sources
              apt_repository:
                repo: "deb [arch={{ 'amd64' if ansible_architecture == 'x86_64' else ansible_architecture }} signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/debian {{ ansible_distribution_release }} stable"
        - name: Install docker packages
          apt:
            pkg:
              - docker-ce
              - docker-ce-cli
              - containerd.io
              - docker-buildx-plugin
              - docker-compose-plugin
            state: latest
            update_cache: yes
    - name: Copy compose.yml
      copy: src=../../compose.yml dest=compose.yml
    - name: Copy assets
      copy: src=../../assets dest=/home/<USER>/
    - name: Login to Docker registery
      become: true
      # The hardcoded credentials is not an issue, this API key only has read access to the docker images
      community.docker.docker_login:
        username: "nologin"
        password: "548912ff-71cd-4534-b582-2e3f15415420"
        registry_url: rg.fr-par.scw.cloud/ppu-builds
    - name: Copy .env for compose stack
      copy:
        content: "{{ env_file_content }}"
        dest: /home/<USER>/.env
    - name: Start docker compose stack
      become: true
      environment:
        IMAGE_TAG: "{{ env }}"
      community.docker.docker_compose_v2:
        project_name: ppu
        project_src: /home/<USER>
        files:
          - compose.yml
        pull: always
    - name: Create /etc/caddy folder
      become: true
      file: path=/etc/caddy state=directory
    - name: Copy Caddyfile
      become: true
      template: src=Caddyfile.j2 dest=/etc/caddy/Caddyfile
    - name: Install caddy
      become: true
      apt: pkg=caddy state=latest
    - name: Reload caddy
      become: true
      service: name=caddy state=reloaded
