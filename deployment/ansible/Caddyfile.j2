{{ frontend_host }} {
    handle /api/* {
        reverse_proxy http://127.0.0.1:8000
    }
    handle_path /consignes/* {
        rewrite * /api/v1/document_reference{uri}
        reverse_proxy http://127.0.0.1:8000
    }
    handle {
{% if env != "prod" %}
        basicauth {
            ppu $2a$14$7.ZVQuHlV02y0gFpMw7Yj.Kw1dGJkfeoW7nPfBMjtLg0MtakBeR.K
        }
{% endif %}
        reverse_proxy http://127.0.0.1:3000
    }
}

{{ backend_host }} {
    reverse_proxy http://127.0.0.1:8000
}
