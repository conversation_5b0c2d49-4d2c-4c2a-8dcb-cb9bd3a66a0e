# locals {
#   admins = toset([
#     "<EMAIL>",
#   ])
#   devs = toset(["<EMAIL>"])
#   ops = toset([])
# }

# data "scaleway_iam_user" "admins" {
#   for_each = local.admins
#   email    = each.value
# }

# data "scaleway_iam_user" "devs" {
#   for_each = local.devs
#   email    = each.value
# }

# data "scaleway_iam_user" "ops" {
#   for_each = local.ops
#   email    = each.value
# }

# resource "scaleway_iam_group" "admins" {
#   name     = "Project Admins"
#   user_ids = [for user in data.scaleway_iam_user.admins : user.id]
# }

# resource "scaleway_iam_group" "devs" {
#   name = "Project Devs"
#   user_ids = distinct(concat(
#     [for user in data.scaleway_iam_user.admins : user.id],
#     [for user in data.scaleway_iam_user.ops : user.id],
#     [for user in data.scaleway_iam_user.devs : user.id]
#   ))
# }

# resource "scaleway_iam_group" "ops" {
#   name = "Project Ops"
#   user_ids = distinct(concat(
#     [for user in data.scaleway_iam_user.admins : user.id],
#     [for user in data.scaleway_iam_user.ops : user.id]
#   ))
# }


# resource scaleway_iam_policy "prod_admin" {
#   name = "prod_admin"
#   group_id = scaleway_iam_group.admins.id
#   rule {
#     project_ids = [var.environment.prod.scaleway_project_id]
#     permission_set_names = ["ObjectStorageFullAccess", "DomainsDNSFullAccess", "SecretManagerFullAccess"]
#   }
# }

# resource scaleway_iam_policy "uat_admin" {
#   name = "uat_admin"
#   group_id = scaleway_iam_group.ops.id
#   rule {
#     project_ids = [var.environment.uat.scaleway_project_id]
#     permission_set_names = ["ObjectStorageFullAccess", "DomainsDNSFullAccess", "SecretManagerFullAccess"]
#   }
# }

# resource "scaleway_iam_policy" "dev_admin" {
#   name = "dev_admin"
#   group_id = scaleway_iam_group.devs.id
#   rule {
#     project_ids = [var.environment.dev.scaleway_project_id]
#     permission_set_names = ["ObjectStorageFullAccess", "DomainsDNSFullAccess", "SecretManagerFullAccess"]
#   }
# }

# resource scaleway_iam_policy "uat_ssh" {
#   name = "uat_ssh"
#   group_id = scaleway_iam_group.devs.id
#   rule {
#     project_ids = [var.environment.uat.scaleway_project_id]
#     permission_set_names = ["SecretManagerReadOnly"]
#   }
# }

# resource scaleway_iam_policy "prod_ssh" {
#   name = "prod_ssh"
#   group_id = scaleway_iam_group.ops.id
#   rule {
#     project_ids = [var.environment.prod.scaleway_project_id]
#     permission_set_names = ["SecretManagerReadOnly"]
#   }
# }

# resource "scaleway_iam_policy" "registry_write" {
#   name = "registry_write"
#   group_id = scaleway_iam_group.devs.id
#   rule {
#     project_ids = [var.infra_project_id]
#     permission_set_names = ["ContainerRegistryFullAccess"]
#   }
# }

data "scaleway_iam_application" "ansible" {
  name        = "Ansible"
}

resource "scaleway_iam_policy" "registry_readonly" {
  name = "registry_readonly"
  application_id = data.scaleway_iam_application.ansible.id
  rule {
    project_ids = [var.infra_project_id]
    permission_set_names = ["ContainerRegistryReadOnly"]
  }
}