terraform {
  required_version = ">=1.5"
  required_providers {
    scaleway = {
      source  = "scaleway/scaleway"
      version = "2.44.0"
    }
  }
}

provider "scaleway" {
  zone   = "fr-par-2"
  region = "fr-par"
  project_id = var.scaleway_project_id
}

terraform {
  backend "s3" {
    region                      = "fr-par"
    endpoint                    = "https://s3.fr-par.scw.cloud"
    skip_credentials_validation = true
    skip_region_validation      = true
    skip_requesting_account_id  = true
  }
}