resource "scaleway_domain_zone" "domain_zone" {
  domain    = "dev.ppu.urgenceschrono.com"
  subdomain = ""
}

resource "scaleway_domain_record" "server_dns" {
  dns_zone = scaleway_domain_zone.domain_zone.id
  name = ""
  type = "A"
  data = scaleway_instance_server.staging.public_ip
  ttl = 3600
}

# resource "scaleway_domain_record" "server_dnsv6" {
#   dns_zone = scaleway_domain_zone.domain_zone.id
#   name = ""
#   type = "AAAA"
#   data = replace(replace(openstack_compute_instance_v2.compute.access_ip_v6, "[", ""), "]", "")
#   ttl = 3600
# }

resource "scaleway_domain_record" "server_dns_wildcard" {
  dns_zone = scaleway_domain_zone.domain_zone.id
  name = "*"
  type = "A"
  data = scaleway_instance_server.staging.public_ip
  ttl = 3600
}

# resource "scaleway_domain_record" "server_dnsv6_wildcard" {
#   dns_zone = scaleway_domain_zone.domain_zone.id
#   name = "*"
#   type = "AAAA"
#   data = replace(replace(openstack_compute_instance_v2.compute.access_ip_v6, "[", ""), "]", "")
#   ttl = 3600
# }