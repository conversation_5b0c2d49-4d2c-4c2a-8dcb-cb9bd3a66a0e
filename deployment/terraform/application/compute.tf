# The SSH key is manually uploaded once by an admin on setup, then never touched again (unless regenerated). The terraform state does not modify it, only reads it.
data "scaleway_secret" "ssh_key_secret" {
  name = "ssh-key"
}

data "scaleway_secret_version" "ssh_key" {
  secret_id  = data.scaleway_secret.ssh_key_secret.id
  revision = "1"
}

# Grab the public key from the private key
data "tls_public_key" "ssh_key_public" {
  private_key_openssh = base64decode(data.scaleway_secret_version.ssh_key.data)
}

resource "scaleway_secret" "env_vars" {
  name = "env-vars"
}

resource "scaleway_iam_ssh_key" "scw_ssh_key" {
  name = "scw-ssh-key"
  public_key = data.tls_public_key.ssh_key_public.public_key_openssh
}

resource "scaleway_instance_security_group" "staging" {
  inbound_default_policy  = "drop"
  outbound_default_policy = "accept"

  inbound_rule {
    action   = "accept"
    port     = "22"
    ip_range = "0.0.0.0/0"
  }

  inbound_rule {
    action = "accept"
    port   = "80"
  }

  inbound_rule {
    action = "accept"
    port   = "443"
  }

  # This flag conteols if SMTP is being blocked. We use it for the contact form.
  enable_default_security = false
}

resource "scaleway_instance_server" "staging" {
  type       = "PLAY2-PICO"
  image      = "debian_bookworm"

  routed_ip_enabled = true
  enable_dynamic_ip = true

  root_volume {
    size_in_gb = 10
  }

  security_group_id = scaleway_instance_security_group.staging.id
}